<script setup>
import { ref, onMounted, reactive, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElLoading, ElMessageBox, ElNotification } from 'element-plus'
import { TrendCharts, Coin, Plus, Edit, Delete, Search, RefreshRight, Loading, Check, Document, DataAnalysis, ArrowLeft, ArrowRight, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
import { getDepartmentList } from '@/api/department'
import { getEmployeePage, getEmployeeList } from '@/api/employee'
import {
    getAllStockPrices,
    getStockPricePage,
    addStockPrice,
    updateStockPrice,
    deleteStockPrice as deleteStockPriceAPI,
    getEmployeeStockPage,
    addEmployeeStock,
    addEmployeeStockBatch,
    updateEmployeeStock,
    deleteEmployeeStock as deleteEmployeeStockAPI,
    getStockWithdrawalPage,
    auditStockWithdrawal,
    batchAuditStockWithdrawal,
    getStockOverviewPage
} from '@/api/stock'

// 数据状态
const loading = ref(false)
const stockPriceLoading = ref(false)
const employeeStockLoading = ref(false)
const stockPriceFormLoading = ref(false)
const employeeStockFormLoading = ref(false)
const chartLoading = ref(false)

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 触摸滑动相关
const touchStartX = ref(0)
const touchStartY = ref(0)
const isSwiping = ref(false)

// 选中的数据点
const selectedDataPoint = ref(null)
const selectedDataIndex = ref(-1)

// 股票价格历史数据（用于图表显示，包含所有数据）
const stockPriceHistory = ref([])
// 图表显示的数据（范围滑块控制的数据）
const chartDisplayData = ref([])
// 图表范围滑块配置
const chartRange = reactive({
    range: [0, 14], // 范围滑块的值 [起始索引, 结束索引]
    maxIndex: 0 // 最大索引值（数据总数-1）
})
// 股票价格分页数据（如果需要分页表格显示）
const stockPricePageData = ref([])
const stockPricePagination = reactive({
    page: 1,
    size: 10,
    total: 0
})

// 员工股票数据
const employeeStocks = ref([])
const employeeStockPagination = reactive({
    page: 1,
    size: 10,
    total: 0
})

// 搜索条件
const stockPriceSearch = reactive({
    dateRange: '',
    remark: ''
})

const employeeStockSearch = reactive({
    employeeName: '',
    departmentId: '',
    dateRange: ''
})

// 对话框状态
const stockPriceDialogVisible = ref(false)
const employeeStockDialogVisible = ref(false)
const stockPriceDialogType = ref('add') // add, edit
const employeeStockDialogType = ref('add') // add, edit

// 部门和员工数据
const departmentList = ref([])
const employeeList = ref([])
const loadingDepartments = ref(false)
const loadingEmployees = ref(false)
const formDepartmentId = ref('') // 用于部门选择框的绑定值

// 员工选择相关（参考employeeexpense.vue）
const employeeSearchQuery = ref('')
const employeeCurrentPage = ref(1)
const selectedDepartmentId = ref('') // 员工筛选的部门ID
const allEmployeeList = ref([]) // 所有员工列表（包括离职）
const filteredEmployeeList = ref([]) // 过滤后的员工列表
const employeeHasMore = ref(true)
const employeePageSize = ref(100)

// 表单数据
const stockPriceForm = reactive({
    id: null,
    time: '',
    unitPrice: '',
    remark: ''
})

const employeeStockForm = reactive({
    id: null,
    stockId: '',
    employeeId: '', // 编辑模式使用
    employeeIds: [], // 添加模式使用（多选）
    employeeName: '',
    quantity: '',
    acquisitionTime: '',
    unlockTime: '',
    remark: ''
})

// 表单验证规则
const stockPriceRules = {
    time: [
        { required: true, message: '请选择日期', trigger: 'change' }
    ],
    unitPrice: [
        { required: true, message: '请输入股票单价', trigger: 'blur' },
        { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的价格（最多两位小数）', trigger: 'blur' }
    ]
}

// 统一验证规则
const employeeStockRules = {
    employeeIds: [
        { required: true, type: 'array', min: 1, message: '请至少选择一个员工', trigger: 'change' }
    ],
    quantity: [
        { required: true, message: '请输入股票数量', trigger: 'blur' },
        { pattern: /^\d+$/, message: '请输入有效的数量（正整数）', trigger: 'blur' }
    ],
    acquisitionTime: [
        { required: true, message: '请选择获取时间', trigger: 'change' }
    ],
    unlockTime: [
        { required: true, message: '请选择解禁时间', trigger: 'change' }
    ]
}

// 表单引用
const stockPriceFormRef = ref(null)
const employeeStockFormRef = ref(null)

// 股票提现审核相关状态
const withdrawalAuditDialogVisible = ref(false)
const withdrawalAuditLoading = ref(false)
const withdrawalList = ref([])
const withdrawalPagination = reactive({
    page: 1,
    size: 10,
    total: 0
})
const withdrawalQueryForm = reactive({
    status: '',
    employeeName: '',
    startDate: '',
    endDate: ''
})
const selectedWithdrawals = ref([])
const auditDialogVisible = ref(false)
const auditForm = reactive({
    action: '',
    rejectReason: ''
})
const auditFormRef = ref(null)

// 员工提现记录相关状态
const withdrawalRecordsDialogVisible = ref(false)
const withdrawalRecordsLoading = ref(false)
const withdrawalRecordsList = ref([])
const withdrawalRecordsPagination = reactive({
    page: 1,
    size: 10,
    total: 0
})
const withdrawalRecordsQueryForm = reactive({
    status: '',
    employeeName: '',
    startDate: '',
    endDate: ''
})

// 待审核提现数量
const pendingWithdrawalCount = ref(0)

// 持股总览相关状态
const stockOverviewDialogVisible = ref(false)
const stockOverviewLoading = ref(false)
const stockOverviewList = ref([])
const stockOverviewPagination = reactive({
    page: 1,
    size: 10,
    total: 0
})
const stockOverviewQueryForm = reactive({
    employeeName: '',
    departmentId: ''
})



// 计算最新股价
const latestPrice = computed(() => {
    if (stockPriceHistory.value.length === 0) return 0
    return stockPriceHistory.value[stockPriceHistory.value.length - 1].unitPrice
})

// 更新图表显示数据（基于范围滑块）
const updateChartDisplayData = () => {
    if (stockPriceHistory.value.length === 0) {
        chartDisplayData.value = []
        chartRange.maxIndex = 0
        return
    }

    // 更新最大索引
    chartRange.maxIndex = stockPriceHistory.value.length - 1

    // 确保范围值在有效范围内
    const startIndex = Math.max(0, Math.min(chartRange.range[0], chartRange.maxIndex))
    const endIndex = Math.max(startIndex, Math.min(chartRange.range[1], chartRange.maxIndex))

    // 更新范围值
    chartRange.range = [startIndex, endIndex]

    // 提取范围内的数据
    chartDisplayData.value = stockPriceHistory.value.slice(startIndex, endIndex + 1)
}

// 初始化范围到最新的15条数据
const initializeRangeToLatest = () => {
    if (stockPriceHistory.value.length === 0) return

    const totalCount = stockPriceHistory.value.length
    const defaultSize = 15

    // 计算默认范围：最新的15条数据
    const endIndex = totalCount - 1
    const startIndex = Math.max(0, endIndex - defaultSize + 1)

    chartRange.range = [startIndex, endIndex]
    updateChartDisplayData()
}

// 处理范围滑块变化
const handleRangeChange = (value) => {
    chartRange.range = value
    updateChartDisplayData()
    if (chartInstance) {
        updateChart(false)
    }
}

// 快速设置到最新数据
const setToLatest = () => {
    initializeRangeToLatest()
    if (chartInstance) {
        updateChart(false)
    }
}

// 快速设置到最早数据
const setToEarliest = () => {
    if (stockPriceHistory.value.length === 0) return

    const defaultSize = 15
    const endIndex = Math.min(defaultSize - 1, stockPriceHistory.value.length - 1)

    chartRange.range = [0, endIndex]
    updateChartDisplayData()
    if (chartInstance) {
        updateChart(false)
    }
}

// 加载所有股票价格数据用于图表显示
const loadAllStockPriceData = async () => {
    try {
        chartLoading.value = true

        // 调用获取所有数据的API
        const response = await getAllStockPrices()

        if (response.code === 200) {
            // 对数据按日期排序，确保最新日期在右边
            const rawData = response.data || []
            stockPriceHistory.value = rawData.sort((a, b) => {
                return new Date(a.time) - new Date(b.time)
            })

            // 初始化范围到最新数据
            initializeRangeToLatest()

            // 如果图表已初始化，更新图表数据
            if (chartInstance) {
                updateChart(false) // 重新加载数据时不保持选中状态
            }
        } else {
            ElMessage.error(response.message || '加载股票价格数据失败')
            stockPriceHistory.value = []
            chartDisplayData.value = []
        }

    } catch (error) {
        console.error('加载股票价格数据失败:', error)
        ElMessage.error('加载股票价格数据失败，请稍后再试')
        stockPriceHistory.value = []
        chartDisplayData.value = []
    } finally {
        chartLoading.value = false
    }
}

// 加载股票价格分页数据（如果需要表格显示）
const loadStockPriceData = async () => {
    try {
        stockPriceLoading.value = true

        // 构建查询参数
        const params = {
            pageNum: stockPricePagination.page,
            pageSize: stockPricePagination.size
        }

        // 添加搜索条件
        if (stockPriceSearch.dateRange && stockPriceSearch.dateRange.length === 2) {
            params.startDate = stockPriceSearch.dateRange[0]
            params.endDate = stockPriceSearch.dateRange[1]
        }

        if (stockPriceSearch.remark) {
            params.remark = stockPriceSearch.remark
        }

        // 调用API
        const response = await getStockPricePage(params)

        if (response.code === 200) {
            // 分页数据主要用于搜索和其他用途，不影响图表
            stockPricePageData.value = response.data.list || []
            stockPricePagination.total = response.data.total || 0
        } else {
            ElMessage.error(response.message || '加载股票价格数据失败')
            stockPricePageData.value = []
            stockPricePagination.total = 0
        }

    } catch (error) {
        console.error('加载股票价格数据失败:', error)
        ElMessage.error('加载股票价格数据失败，请稍后再试')
        stockPricePageData.value = []
        stockPricePagination.total = 0
    } finally {
        stockPriceLoading.value = false
    }
}

// 加载员工股票数据
const loadEmployeeStockData = async () => {
    try {
        employeeStockLoading.value = true

        // 构建查询参数
        const params = {
            pageNum: employeeStockPagination.page,
            pageSize: employeeStockPagination.size
        }

        // 添加搜索条件
        if (employeeStockSearch.employeeName) {
            params.employeeName = employeeStockSearch.employeeName
        }

        if (employeeStockSearch.departmentId) {
            params.departmentId = employeeStockSearch.departmentId
        }

        if (employeeStockSearch.dateRange && employeeStockSearch.dateRange.length === 2) {
            params.acquisitionStartTime = employeeStockSearch.dateRange[0] + ' 00:00:00'
            params.acquisitionEndTime = employeeStockSearch.dateRange[1] + ' 23:59:59'
        }

        // 调用API
        const response = await getEmployeeStockPage(params)

        if (response.code === 200) {
            employeeStocks.value = response.data.list || []
            employeeStockPagination.total = response.data.total || 0
        } else {
            ElMessage.error(response.message || '加载员工股票数据失败')
            employeeStocks.value = []
            employeeStockPagination.total = 0
        }

    } catch (error) {
        console.error('加载员工股票数据失败:', error)
        ElMessage.error('加载员工股票数据失败，请稍后再试')
        employeeStocks.value = []
        employeeStockPagination.total = 0
    } finally {
        employeeStockLoading.value = false
    }
}

// 格式化货币
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00'
    return '¥' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
}

// 股票价格分页变化
const handleStockPriceCurrentChange = (page) => {
    stockPricePagination.page = page
    loadStockPriceData()
}

const handleStockPriceSizeChange = (size) => {
    stockPricePagination.size = size
    stockPricePagination.page = 1
    loadStockPriceData()
}

// 员工股票分页变化
const handleEmployeeStockCurrentChange = (page) => {
    employeeStockPagination.page = page
    loadEmployeeStockData()
}

const handleEmployeeStockSizeChange = (size) => {
    employeeStockPagination.size = size
    employeeStockPagination.page = 1
    loadEmployeeStockData()
}

// 获取解禁状态
const getUnlockStatus = (unlockTime) => {
    if (!unlockTime) return { type: 'info', text: '无限制' }
    const now = new Date()
    const unlock = new Date(unlockTime)
    if (now >= unlock) {
        return { type: 'success', text: '已解禁' }
    } else {
        return { type: 'warning', text: '未解禁' }
    }
}

// 搜索股票价格数据（同时更新图表）
const searchStockPriceData = async () => {
    try {
        chartLoading.value = true

        // 如果有搜索条件，使用分页接口进行搜索
        if (stockPriceSearch.dateRange || stockPriceSearch.remark) {
            const params = {}

            if (stockPriceSearch.dateRange && stockPriceSearch.dateRange.length === 2) {
                params.startDate = stockPriceSearch.dateRange[0]
                params.endDate = stockPriceSearch.dateRange[1]
            }

            if (stockPriceSearch.remark) {
                params.remark = stockPriceSearch.remark
            }

            // 获取搜索结果（不分页，获取所有匹配的数据）
            const response = await getStockPricePage({ ...params, pageSize: 1000 })

            if (response.code === 200) {
                const rawData = response.data.list || []
                stockPriceHistory.value = rawData.sort((a, b) => {
                    return new Date(a.time) - new Date(b.time)
                })

                // 初始化范围到最新数据
                initializeRangeToLatest()

                if (chartInstance) {
                    updateChart(false)
                }
            } else {
                ElMessage.error(response.message || '搜索失败')
            }
        } else {
            // 没有搜索条件时，加载所有数据
            await loadAllStockPriceData()
        }
    } catch (error) {
        console.error('搜索股票价格数据失败:', error)
        ElMessage.error('搜索失败，请稍后再试')
    } finally {
        chartLoading.value = false
    }
}

// 重置搜索条件
const resetStockPriceSearch = () => {
    stockPriceSearch.dateRange = ''
    stockPriceSearch.remark = ''
    loadAllStockPriceData() // 重置时重新加载所有数据
}

// 范围滑块控制函数
const handleRangeSliderChange = (value) => {
    handleRangeChange(value)
}

// 触摸事件处理
const handleTouchStart = (event) => {
    if (event.touches.length === 1) {
        touchStartX.value = event.touches[0].clientX
        touchStartY.value = event.touches[0].clientY
        isSwiping.value = false
    }
}

const handleTouchMove = (event) => {
    if (event.touches.length === 1) {
        const deltaX = Math.abs(event.touches[0].clientX - touchStartX.value)
        const deltaY = Math.abs(event.touches[0].clientY - touchStartY.value)

        // 如果水平滑动距离大于垂直滑动距离，认为是水平滑动
        if (deltaX > deltaY && deltaX > 10) {
            isSwiping.value = true
            event.preventDefault() // 阻止页面滚动
        }
    }
}

const handleTouchEnd = (event) => {
    if (isSwiping.value && event.changedTouches.length === 1) {
        const deltaX = event.changedTouches[0].clientX - touchStartX.value
        const deltaY = Math.abs(event.changedTouches[0].clientY - touchStartY.value)

        // 确保是水平滑动且滑动距离足够
        if (Math.abs(deltaX) > 50 && Math.abs(deltaX) > deltaY) {
            const currentSize = chartRange.range[1] - chartRange.range[0] + 1
            const moveStep = Math.ceil(currentSize * 0.3) // 移动30%的当前窗口大小

            if (deltaX > 0) {
                // 向右滑动，查看更早的数据（向左移动范围）
                const newStart = Math.max(0, chartRange.range[0] - moveStep)
                const newEnd = Math.max(newStart, chartRange.range[1] - moveStep)
                chartRange.range = [newStart, newEnd]
            } else {
                // 向左滑动，查看更新的数据（向右移动范围）
                const newEnd = Math.min(chartRange.maxIndex, chartRange.range[1] + moveStep)
                const newStart = Math.min(newEnd, chartRange.range[0] + moveStep)
                chartRange.range = [newStart, newEnd]
            }

            updateChartDisplayData()
            if (chartInstance) {
                updateChart(false)
            }
        }
    }
    isSwiping.value = false
}

const resetEmployeeStockSearch = () => {
    employeeStockSearch.employeeName = ''
    employeeStockSearch.departmentId = ''
    employeeStockSearch.dateRange = ''
    loadEmployeeStockData()
}

// 打开对话框
const openStockPriceDialog = (type, row = null) => {
    stockPriceDialogType.value = type
    if (type === 'add') {
        Object.assign(stockPriceForm, {
            id: null,
            time: '',
            unitPrice: '',
            remark: ''
        })
    } else {
        Object.assign(stockPriceForm, { ...row })
    }
    stockPriceDialogVisible.value = true
}

const openEmployeeStockDialog = (type, row = null) => {
    employeeStockDialogType.value = type

    // 清除之前的表单验证状态
    nextTick(() => {
        if (employeeStockFormRef.value) {
            employeeStockFormRef.value.clearValidate()
        }
    })

    if (type === 'add') {
        // 始终使用最新的股票价格ID（员工股票不使用历史价格）
        let latestStockId = null
        if (stockPriceHistory.value.length > 0) {
            latestStockId = stockPriceHistory.value[stockPriceHistory.value.length - 1].id
        }

        Object.assign(employeeStockForm, {
            id: null,
            stockId: latestStockId, // 设置最新股票价格ID
            employeeId: '',
            employeeIds: [], // 添加多选员工数组
            employeeName: '',
            quantity: '',
            acquisitionTime: '',
            unlockTime: '',
            remark: ''
        })
        // 重置部门筛选
        selectedDepartmentId.value = ''
        formDepartmentId.value = ''
        // 重置员工列表
        employeeList.value = []
        employeeSearchQuery.value = ''
        employeeCurrentPage.value = 1
        employeeHasMore.value = true
    } else {
        Object.assign(employeeStockForm, { ...row })
        // 编辑时，将单个员工ID转换为数组形式
        if (row.employeeId) {
            employeeStockForm.employeeIds = [row.employeeId]
        } else {
            employeeStockForm.employeeIds = []
        }
        // 编辑时，加载员工数据
        loadEmployees('', 1, false)
    }
    employeeStockDialogVisible.value = true
}

// 删除操作
const deleteStockPrice = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除这条股价记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        const response = await deleteStockPriceAPI(row.id)
        if (response.code === 200) {
            ElMessage.success('删除成功')
            // 重新加载图表数据（删除后自动选择最新数据点）
            await loadAllStockPriceData()
        } else {
            ElMessage.error(response.message || '删除失败')
        }
    } catch (error) {
        if (error !== 'cancel') { // 用户取消删除时不显示错误
            console.error('删除股票价格失败:', error)
            ElMessage.error('删除失败，请稍后再试')
        }
    }
}

const deleteEmployeeStock = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除这条员工股票记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        const response = await deleteEmployeeStockAPI(row.id)
        if (response.code === 200) {
            ElMessage.success('删除成功')
            await loadEmployeeStockData()
        } else {
            ElMessage.error(response.message || '删除失败')
        }
    } catch (error) {
        if (error !== 'cancel') { // 用户取消删除时不显示错误
            console.error('删除员工股票失败:', error)
            ElMessage.error('删除失败，请稍后再试')
        }
    }
}

// 保存股票价格
const saveStockPrice = async () => {
    if (!stockPriceFormRef.value) return

    stockPriceFormLoading.value = true
    try {
        await stockPriceFormRef.value.validate()

        if (stockPriceDialogType.value === 'add') {
            // 添加股票价格
            const response = await addStockPrice({
                time: stockPriceForm.time,
                unitPrice: parseFloat(stockPriceForm.unitPrice),
                remark: stockPriceForm.remark || ''
            })

            if (response.code === 200) {
                ElMessage.success('添加成功')
                stockPriceDialogVisible.value = false
                // 重新加载图表数据
                await loadAllStockPriceData()
                // 刷新员工股票列表以更新当前单价和总价值
                await loadEmployeeStockData()
            } else {
                ElMessage.error(response.message || '添加失败')
            }
        } else {
            // 编辑股票价格
            const response = await updateStockPrice({
                id: stockPriceForm.id,
                time: stockPriceForm.time,
                unitPrice: parseFloat(stockPriceForm.unitPrice),
                remark: stockPriceForm.remark || ''
            })

            if (response.code === 200) {
                ElMessage.success('编辑成功')
                stockPriceDialogVisible.value = false
                // 重新加载图表数据
                await loadAllStockPriceData()
                // 刷新员工股票列表以更新当前单价和总价值
                await loadEmployeeStockData()
            } else {
                ElMessage.error(response.message || '编辑失败')
            }
        }
    } catch (error) {
        if (error !== false) { // 验证失败时error为false
            console.error('保存股票价格失败:', error)
            ElMessage.error('操作失败，请稍后再试')
        }
    } finally {
        stockPriceFormLoading.value = false
    }
}

// 保存员工股票
const saveEmployeeStock = async () => {
    if (!employeeStockFormRef.value) return

    employeeStockFormLoading.value = true
    try {
        // 表单验证
        const isValid = await employeeStockFormRef.value.validate().catch(() => false)
        if (!isValid) {
            // 验证失败时，检查具体的验证错误并显示友好提示
            const firstErrorField = getFirstValidationError()
            if (firstErrorField) {
                ElMessage.error(firstErrorField)
            }
            return
        }

        if (employeeStockDialogType.value === 'add') {
            // 批量添加员工股票
            if (!employeeStockForm.employeeIds || employeeStockForm.employeeIds.length === 0) {
                ElMessage.error('请至少选择一个员工')
                return
            }

            // 验证是否有股票价格记录（后端会自动使用最新价格）
            if (stockPriceHistory.value.length === 0) {
                ElMessage.error('请先添加股票价格记录')
                return
            }

            const response = await addEmployeeStockBatch({
                stockId: employeeStockForm.stockId, // 添加stockId参数
                employeeIds: employeeStockForm.employeeIds,
                quantity: parseInt(employeeStockForm.quantity),
                acquisitionTime: employeeStockForm.acquisitionTime,
                unlockTime: employeeStockForm.unlockTime,
                remark: employeeStockForm.remark || ''
            })

            if (response.code === 200) {
                ElMessage.success(`成功为 ${employeeStockForm.employeeIds.length} 名员工添加股票`)
                employeeStockDialogVisible.value = false
                await loadEmployeeStockData()
            } else {
                ElMessage.error(response.message || '添加失败')
            }
        } else {
            // 编辑员工股票 - 现在也支持多选
            if (!employeeStockForm.employeeIds || employeeStockForm.employeeIds.length === 0) {
                ElMessage.error('请至少选择一个员工')
                return
            }

            if (employeeStockForm.employeeIds.length === 1) {
                // 验证是否有股票价格记录（后端会自动使用最新价格）
                if (stockPriceHistory.value.length === 0) {
                    ElMessage.error('请先添加股票价格记录')
                    return
                }

                // 单个员工编辑，使用原有的更新接口
                const response = await updateEmployeeStock({
                    id: employeeStockForm.id,
                    stockId: employeeStockForm.stockId, // 添加stockId参数
                    employeeId: employeeStockForm.employeeIds[0], // 取第一个员工ID
                    quantity: parseInt(employeeStockForm.quantity),
                    acquisitionTime: employeeStockForm.acquisitionTime,
                    unlockTime: employeeStockForm.unlockTime,
                    remark: employeeStockForm.remark || ''
                })

                if (response.code === 200) {
                    ElMessage.success('编辑成功')
                    employeeStockDialogVisible.value = false
                    await loadEmployeeStockData()
                } else {
                    ElMessage.error(response.message || '编辑失败')
                }
            } else {
                // 多个员工编辑，提示用户
                ElMessage.warning('编辑模式下只能选择一个员工，请重新选择')
                return
            }
        }
    } catch (error) {
        // 处理表单验证错误
        if (error && typeof error === 'object' && error.message) {
            // 如果是验证错误对象，显示具体的验证信息
            ElMessage.error(error.message)
        } else if (error !== false) {
            // 其他类型的错误
            console.error('保存员工股票失败:', error)
            ElMessage.error('操作失败，请稍后再试')
        }
        // 注意：当 error === false 时，表示表单验证失败，此时不显示错误消息
        // 因为 Element Plus 会自动显示字段级别的验证错误
    } finally {
        employeeStockFormLoading.value = false
    }
}

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true
    try {
        const res = await getDepartmentList()
        if (res.code === 200) {
            departmentList.value = res.data || []
        } else {
            ElMessage.error(res.message || '获取部门列表失败')
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'))
    } finally {
        loadingDepartments.value = false
    }
}

// 加载员工数据（支持分页和搜索，支持部门过滤）
const loadEmployees = async (query = '', page = 1, append = false) => {
    // 确保部门列表已加载
    if (departmentList.value.length === 0) {
        await loadDepartmentList()
    }

    loadingEmployees.value = true
    try {
        // 根据是否选择了部门来决定使用哪个API
        let res
        if (formDepartmentId.value) {
            // 如果选择了部门，使用分页API并传入部门ID
            res = await getEmployeePage({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined,
                departmentId: formDepartmentId.value
            })
        } else {
            // 如果没有选择部门，使用新的员工列表API获取所有员工
            res = await getEmployeeList({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined
            })
        }

        if (res.code === 200) {
            let employees = []
            let total = 0
            let hasMore = false

            if (formDepartmentId.value) {
                // 部门过滤模式：使用分页API的返回格式
                employees = res.data.list || []
                total = res.data.total || 0
                hasMore = page * employeePageSize.value < total
            } else {
                // 全员工模式：使用新API的返回格式
                const result = res.data
                employees = result.employees || []
                total = result.total || 0
                hasMore = result.hasMore
            }

            // 为每个员工添加部门名称和离职状态
            employees.forEach(emp => {
                if (emp.departmentId) {
                    const dept = departmentList.value.find(d =>
                        d.departmentId === emp.departmentId ||
                        d.departmentId === parseInt(emp.departmentId, 10))
                    emp.departmentName = dept ? dept.departmentName : '未知部门'
                } else {
                    emp.departmentName = '未知部门'
                }

                // 检查是否已离职
                emp.isResigned = !!(emp.exitDate)
            })

            if (append) {
                employeeList.value = [...employeeList.value, ...employees]
            } else {
                employeeList.value = employees
            }

            employeeHasMore.value = hasMore
            employeeCurrentPage.value = page
        } else {
            ElMessage.error(res.message || '获取员工列表失败')
            if (!append) {
                employeeList.value = []
            }
        }
    } catch (error) {
        ElMessage.error('获取员工列表失败: ' + (error.message || '未知错误'))
        if (!append) {
            employeeList.value = []
        }
    } finally {
        loadingEmployees.value = false
    }
}

// 远程搜索员工
const handleEmployeeRemoteSearch = async (query) => {
    employeeSearchQuery.value = query
    employeeCurrentPage.value = 1
    employeeHasMore.value = true

    if (!query && !formDepartmentId.value) {
        // 如果没有查询内容且没有选择部门，清空选项
        employeeList.value = []
        return
    }

    await loadEmployees(query, 1, false)
}

// 处理部门筛选变化（新的筛选逻辑）
const handleDepartmentFilterChange = (departmentId) => {
    // 设置部门筛选ID
    selectedDepartmentId.value = departmentId
    // 同步设置 formDepartmentId 以兼容现有的员工加载逻辑
    formDepartmentId.value = departmentId

    // 重置员工选择相关状态
    employeeList.value = []
    employeeSearchQuery.value = ''
    employeeCurrentPage.value = 1
    employeeHasMore.value = true

    // 保持已选择的员工，不清空
    // 注释：部门筛选时保持用户已选择的员工

    // 如果选择了部门，自动加载该部门的员工；否则加载所有员工
    loadEmployees('', 1, false)
}

// 处理部门选择变化（保留用于兼容性）
const handleDepartmentChange = (departmentId) => {
    handleDepartmentFilterChange(departmentId)
}

// 加载更多员工
const loadMoreEmployees = async () => {
    if (!employeeHasMore.value || loadingEmployees.value) {
        return
    }

    const nextPage = employeeCurrentPage.value + 1
    await loadEmployees(employeeSearchQuery.value, nextPage, true)
}

// 处理员工选择滚动
const handleEmployeeScroll = (event) => {
    const { target } = event
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
        loadMoreEmployees()
    }
}

// 删除：handleEmployeeChange - 现在统一使用多选模式

// 员工选择框可见性变化
const handleEmployeeSelectVisibleChange = (visible) => {
    if (visible && employeeList.value.length === 0 && !employeeSearchQuery.value) {
        // 当下拉框打开且没有数据时，加载初始数据
        loadEmployees('', 1, false)
    }
}

// 获取员工显示标签（用于多选时的标签显示）
const getEmployeeDisplayLabel = (employee) => {
    const deptPos = getEmployeeDepartmentPosition(employee)
    const resignedText = employee.isResigned ? '（已离职）' : ''
    return `${employee.name} (${deptPos})${resignedText}`
}

// 获取员工部门-职位信息
const getEmployeeDepartmentPosition = (employee) => {
    const department = employee.departmentName || '未知部门'
    const position = employee.positionName || ''

    if (position) {
        return `${department}-${position}`
    } else {
        return department
    }
}

// 获取第一个表单验证错误信息
const getFirstValidationError = () => {
    const form = employeeStockForm

    // 检查员工选择
    if (!form.employeeIds || form.employeeIds.length === 0) {
        return '请至少选择一个员工'
    }

    // 检查股票数量
    if (!form.quantity || form.quantity.toString().trim() === '') {
        return '请输入股票数量'
    }

    // 检查数量格式
    if (!/^\d+$/.test(form.quantity.toString())) {
        return '请输入有效的数量（正整数）'
    }

    // 检查获取时间
    if (!form.acquisitionTime) {
        return '请选择获取时间'
    }

    return null
}

// 处理员工股票对话框关闭事件
const handleEmployeeStockDialogClose = () => {
    // 清除表单验证状态
    if (employeeStockFormRef.value) {
        employeeStockFormRef.value.clearValidate()
    }
}

// 初始化图表
const initChart = async () => {
    try {
        chartLoading.value = true

        // {{CHENGQI: 使用按需引入的 echarts 配置}}
        // {{CHENGQI: 优化时间: 2025-07-09 11:30:00 +08:00}}
        // {{CHENGQI: 修复时间: 2025-07-09 12:00:00 +08:00}}
        const echartsModule = await import('@/utils/echarts')
        const echarts = echartsModule.default

        if (!chartRef.value) return

        chartInstance = echarts.init(chartRef.value)

        // 使用图表显示数据而不是全部历史数据
        const dates = chartDisplayData.value.map(item => {
            const date = new Date(item.time)
            return `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月${date.getDate().toString().padStart(2, '0')}日`
        })

        // 为每个数据点创建配置，支持单独的样式控制
        const seriesData = chartDisplayData.value.map((item, index) => ({
            value: item.unitPrice,
            itemStyle: {
                color: '#409EFF',
                borderColor: '#fff',
                borderWidth: 2
            },
            symbolSize: 8
        }))

        const option = {
            title: {
                text: '股票价格历史走势',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#303133'
                }
            },

            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const dataIndex = params[0].dataIndex
                    const data = chartDisplayData.value[dataIndex]
                    return `
                        <div style="padding: 8px;">
                            <div style="font-weight: bold; margin-bottom: 4px;">${params[0].name}</div>
                            <div style="color: #409EFF;">股价: ¥${params[0].value}</div>
                            <div style="color: #909399; font-size: 12px; margin-top: 4px;">${data.remark || '无备注'}</div>
                        </div>
                    `
                }
            },
            legend: {
                data: ['股票价格'],
                top: 35
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dates,
                axisLabel: {
                    rotate: 45,
                    fontSize: 12
                }
            },
            yAxis: {
                type: 'value',
                name: '价格 (¥)',
                axisLabel: {
                    formatter: '¥{value}'
                }
            },
            series: [
                {
                    name: '股票价格',
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 3,
                        color: '#409EFF'
                    },
                    itemStyle: {
                        color: '#409EFF',
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    emphasis: {
                        focus: 'self',
                        itemStyle: {
                            color: '#FF6B6B',
                            borderColor: '#fff',
                            borderWidth: 3,
                            shadowBlur: 10,
                            shadowColor: 'rgba(255, 107, 107, 0.5)'
                        },
                        symbolSize: 14
                    },
                    select: {
                        itemStyle: {
                            color: '#FF6B6B',
                            borderColor: '#fff',
                            borderWidth: 3,
                            shadowBlur: 10,
                            shadowColor: 'rgba(255, 107, 107, 0.5)'
                        },
                        symbolSize: 14
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
                            ]
                        }
                    },
                    data: seriesData
                }
            ]
        }

        chartInstance.setOption(option)

        // 添加点击事件监听
        chartInstance.on('click', (params) => {
            if (params.componentType === 'series') {
                const dataIndex = params.dataIndex
                selectDataPoint(dataIndex)
            }
        })

        // 自动选择最新数据点
        autoSelectLatestDataPoint()

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            if (chartInstance) {
                chartInstance.resize()
            }
        })

    } catch (error) {
        console.error('初始化图表失败:', error)
        ElMessage.error('图表初始化失败')
    } finally {
        chartLoading.value = false
    }
}

// 选择数据点
const selectDataPoint = (dataIndex) => {
    if (dataIndex >= 0 && dataIndex < chartDisplayData.value.length) {
        selectedDataIndex.value = dataIndex
        selectedDataPoint.value = chartDisplayData.value[dataIndex]

        if (chartInstance) {
            // 更新图表数据，为选中的数据点设置特殊样式
            updateChartWithSelection(dataIndex)
        }
    }
}

// 更新图表并设置选中样式
const updateChartWithSelection = (selectedIndex = -1) => {
    if (!chartInstance) return

    const dates = chartDisplayData.value.map(item => {
        const date = new Date(item.time)
        return `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月${date.getDate().toString().padStart(2, '0')}日`
    })

    // 为每个数据点创建配置，选中的数据点使用特殊样式
    const seriesData = chartDisplayData.value.map((item, index) => {
        const isSelected = index === selectedIndex
        return {
            value: item.unitPrice,
            itemStyle: {
                color: isSelected ? '#FF6B6B' : '#409EFF',
                borderColor: '#fff',
                borderWidth: isSelected ? 3 : 2,
                shadowBlur: isSelected ? 10 : 0,
                shadowColor: isSelected ? 'rgba(255, 107, 107, 0.5)' : 'transparent'
            },
            symbolSize: isSelected ? 14 : 8
        }
    })

    chartInstance.setOption({
        xAxis: {
            data: dates
        },
        series: [{
            data: seriesData
        }]
    })
}

// 自动选择最新数据点
const autoSelectLatestDataPoint = () => {
    if (chartDisplayData.value.length > 0) {
        // 在当前显示的数据中找到最新日期的数据点索引
        let latestIndex = 0
        let latestDate = new Date(chartDisplayData.value[0].time)

        for (let i = 1; i < chartDisplayData.value.length; i++) {
            const currentDate = new Date(chartDisplayData.value[i].time)
            if (currentDate > latestDate) {
                latestDate = currentDate
                latestIndex = i
            }
        }

        // 选择最新数据点
        selectDataPoint(latestIndex)
    }
}

// 清除选择
const clearSelection = () => {
    selectedDataPoint.value = null
    selectedDataIndex.value = -1

    if (chartInstance) {
        // 更新图表，移除所有选中样式
        updateChartWithSelection(-1)
    }
}

// 更新图表数据
const updateChart = (preserveSelection = false) => {
    if (!chartInstance) return

    // 保存当前选中的数据点信息
    const currentSelectedData = selectedDataPoint.value
    let targetIndex = -1

    // 如果需要保持选中状态且有选中的数据
    if (preserveSelection && currentSelectedData) {
        // 尝试在当前显示的数据中找到相同的数据点
        targetIndex = chartDisplayData.value.findIndex(item =>
            item.time === currentSelectedData.time &&
            item.unitPrice === currentSelectedData.unitPrice
        )

        if (targetIndex >= 0) {
            // 找到了相同的数据点，保持选中
            selectedDataIndex.value = targetIndex
            selectedDataPoint.value = chartDisplayData.value[targetIndex]
        } else {
            // 没找到相同的数据点，自动选择最新数据点
            autoSelectLatestDataPoint()
            targetIndex = selectedDataIndex.value
        }
    } else {
        // 自动选择最新数据点
        autoSelectLatestDataPoint()
        targetIndex = selectedDataIndex.value
    }

    // 使用新的选中样式更新图表
    updateChartWithSelection(targetIndex)
}

// 组件挂载
onMounted(async () => {
    await Promise.all([
        loadAllStockPriceData(), // 加载所有股票价格数据用于图表
        loadEmployeeStockData(),
        loadDepartmentList(),
        loadPendingWithdrawalCount() // 加载待审核提现数量
    ])

    // 等待DOM更新后初始化图表
    await nextTick()

    // 确保图表显示数据已更新
    if (stockPriceHistory.value.length > 0) {
        // 初始化范围到最新数据
        initializeRangeToLatest()
    }

    await initChart()
})

// 组件卸载时清理图表
onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
    }
    window.removeEventListener('resize', () => {})
})

// ==================== 股票提现审核相关方法 ====================

// 打开提现审核弹窗
const openWithdrawalAuditDialog = async () => {
    withdrawalAuditDialogVisible.value = true
    // 只查询待审核的申请
    withdrawalQueryForm.status = 'PENDING'
    await loadWithdrawalList()
}

// 打开员工提现记录弹窗
const openWithdrawalRecordsDialog = async () => {
    withdrawalRecordsDialogVisible.value = true
    // 重置查询条件
    Object.assign(withdrawalRecordsQueryForm, {
        status: '',
        employeeName: '',
        startDate: '',
        endDate: ''
    })
    await loadWithdrawalRecordsList()
}

// 加载提现申请列表
const loadWithdrawalList = async () => {
    try {
        withdrawalAuditLoading.value = true

        const params = {
            page: withdrawalPagination.page,
            size: withdrawalPagination.size,
            status: withdrawalQueryForm.status,
            employeeName: withdrawalQueryForm.employeeName,
            startDate: withdrawalQueryForm.startDate,
            endDate: withdrawalQueryForm.endDate
        }

        const result = await getStockWithdrawalPage(params)
        if (result.code === 200) {
            withdrawalList.value = result.data.list || result.data.records || []
            withdrawalPagination.total = result.data.total || 0
        } else {
            ElMessage.error(result.message || '加载提现申请列表失败')
        }
    } catch (error) {
        console.error('加载提现申请列表失败:', error)
        ElMessage.error('加载失败，请稍后再试')
    } finally {
        withdrawalAuditLoading.value = false
    }
}

// 搜索提现申请
const searchWithdrawals = () => {
    withdrawalPagination.page = 1
    loadWithdrawalList()
}

// 重置搜索条件
const resetWithdrawalSearch = () => {
    Object.assign(withdrawalQueryForm, {
        status: 'PENDING', // 审核弹窗中始终只显示待审核状态
        employeeName: '',
        startDate: '',
        endDate: ''
    })
    withdrawalPagination.page = 1
    loadWithdrawalList()
}

// 处理提现申请分页变化
const handleWithdrawalPageChange = (page) => {
    withdrawalPagination.page = page
    loadWithdrawalList()
}

// 处理提现申请每页大小变化
const handleWithdrawalSizeChange = (size) => {
    withdrawalPagination.size = size
    withdrawalPagination.page = 1
    loadWithdrawalList()
}

// 处理选择变化
const handleWithdrawalSelectionChange = (selection) => {
    selectedWithdrawals.value = selection
}

// 单个审核
const auditSingleWithdrawal = (row, action) => {
    selectedWithdrawals.value = [row]
    auditForm.action = action
    auditForm.rejectReason = ''
    auditDialogVisible.value = true
}

// 批量审核
const batchAuditWithdrawals = (action) => {
    if (selectedWithdrawals.value.length === 0) {
        ElMessage.warning('请选择要审核的申请')
        return
    }

    auditForm.action = action
    auditForm.rejectReason = ''
    auditDialogVisible.value = true
}

// 提交审核
const submitAudit = async () => {
    if (!auditFormRef.value) return

    try {
        await auditFormRef.value.validate()

        const ids = selectedWithdrawals.value.map(item => item.id)
        const auditData = {
            action: auditForm.action,
            rejectReason: auditForm.rejectReason
        }

        if (ids.length === 1) {
            // 单个审核
            const result = await auditStockWithdrawal(ids[0], auditData)
            if (result.code === 200) {
                ElMessage.success('审核成功')
                auditDialogVisible.value = false
                await loadWithdrawalList()
                await loadPendingWithdrawalCount() // 重新加载待审核数量
            } else {
                ElMessage.error(result.message || '审核失败')
            }
        } else {
            // 批量审核
            const result = await batchAuditStockWithdrawal({
                ids: ids,
                ...auditData
            })
            if (result.code === 200) {
                const { successCount, failCount } = result.data
                ElMessage.success(`批量审核完成：成功${successCount}个，失败${failCount}个`)
                auditDialogVisible.value = false
                await loadWithdrawalList()
                await loadPendingWithdrawalCount() // 重新加载待审核数量
            } else {
                ElMessage.error(result.message || '批量审核失败')
            }
        }
    } catch (error) {
        console.error('审核失败:', error)
        ElMessage.error('审核失败，请稍后再试')
    }
}

// 格式化提现状态
const formatWithdrawalStatus = (status) => {
    const statusMap = {
        'PENDING': { text: '待审核', type: 'warning' },
        'APPROVED': { text: '已批准', type: 'success' },
        'REJECTED': { text: '已拒绝', type: 'danger' },
        'CANCELLED': { text: '已取消', type: 'info' }
    }
    return statusMap[status] || { text: '未知', type: 'info' }
}

// 加载员工提现记录列表
const loadWithdrawalRecordsList = async () => {
    try {
        withdrawalRecordsLoading.value = true

        const params = {
            page: withdrawalRecordsPagination.page,
            size: withdrawalRecordsPagination.size,
            status: withdrawalRecordsQueryForm.status,
            employeeName: withdrawalRecordsQueryForm.employeeName,
            startDate: withdrawalRecordsQueryForm.startDate,
            endDate: withdrawalRecordsQueryForm.endDate
        }

        const result = await getStockWithdrawalPage(params)
        if (result.code === 200) {
            withdrawalRecordsList.value = result.data.list || result.data.records || []
            withdrawalRecordsPagination.total = result.data.total || 0
        } else {
            ElMessage.error(result.message || '加载提现记录失败')
        }
    } catch (error) {
        console.error('加载提现记录失败:', error)
        ElMessage.error('加载失败，请稍后再试')
    } finally {
        withdrawalRecordsLoading.value = false
    }
}

// 搜索提现记录
const searchWithdrawalRecords = () => {
    withdrawalRecordsPagination.page = 1
    loadWithdrawalRecordsList()
}

// 重置提现记录搜索条件
const resetWithdrawalRecordsSearch = () => {
    Object.assign(withdrawalRecordsQueryForm, {
        status: '',
        employeeName: '',
        startDate: '',
        endDate: ''
    })
    withdrawalRecordsPagination.page = 1
    loadWithdrawalRecordsList()
}

// 处理提现记录分页变化
const handleWithdrawalRecordsPageChange = (page) => {
    withdrawalRecordsPagination.page = page
    loadWithdrawalRecordsList()
}

// 处理提现记录每页大小变化
const handleWithdrawalRecordsSizeChange = (size) => {
    withdrawalRecordsPagination.size = size
    withdrawalRecordsPagination.page = 1
    loadWithdrawalRecordsList()
}

// 加载待审核提现数量
const loadPendingWithdrawalCount = async () => {
    try {
        const result = await getStockWithdrawalPage({
            page: 1,
            size: 1,
            status: 'PENDING'
        })
        if (result.code === 200) {
            pendingWithdrawalCount.value = result.data.total || 0

            // 如果有待审核的提现申请，显示通知
            if (pendingWithdrawalCount.value > 0) {
                ElNotification({
                    title: '待审批提醒',
                    message: `您有 ${pendingWithdrawalCount.value} 条股票提现申请待审批`,
                    type: 'info',
                    duration: 5000,
                    position: 'top-right'
                })
            }
        }
    } catch (error) {
        console.error('加载待审核提现数量失败:', error)
    }
}

// 审核表单验证规则
const auditRules = {
    rejectReason: [
        {
            validator: (rule, value, callback) => {
                if (auditForm.action === 'REJECT' && (!value || value.trim() === '')) {
                    callback(new Error('拒绝时必须填写拒绝理由'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ]
}

// ==================== 持股总览相关方法 ====================

// 打开持股总览弹窗
const openStockOverviewDialog = async () => {
    stockOverviewDialogVisible.value = true
    // 重置查询条件
    Object.assign(stockOverviewQueryForm, {
        employeeName: '',
        departmentId: ''
    })
    await loadStockOverviewList()
}

// 加载持股总览列表
const loadStockOverviewList = async () => {
    try {
        stockOverviewLoading.value = true

        const params = {
            pageNum: stockOverviewPagination.page,
            pageSize: stockOverviewPagination.size,
            employeeName: stockOverviewQueryForm.employeeName,
            departmentId: stockOverviewQueryForm.departmentId
        }

        const result = await getStockOverviewPage(params)
        if (result.code === 200) {
            stockOverviewList.value = result.data.list || result.data.records || []
            stockOverviewPagination.total = result.data.total || 0
        } else {
            ElMessage.error(result.message || '加载持股总览失败')
        }
    } catch (error) {
        console.error('加载持股总览失败:', error)
        ElMessage.error('加载失败，请稍后再试')
    } finally {
        stockOverviewLoading.value = false
    }
}

// 搜索持股总览
const searchStockOverview = () => {
    stockOverviewPagination.page = 1
    loadStockOverviewList()
}

// 重置持股总览搜索条件
const resetStockOverviewSearch = () => {
    Object.assign(stockOverviewQueryForm, {
        employeeName: '',
        departmentId: ''
    })
    stockOverviewPagination.page = 1
    loadStockOverviewList()
}

// 处理持股总览分页变化
const handleStockOverviewPageChange = (page) => {
    stockOverviewPagination.page = page
    loadStockOverviewList()
}

// 处理持股总览每页大小变化
const handleStockOverviewSizeChange = (size) => {
    stockOverviewPagination.size = size
    stockOverviewPagination.page = 1
    loadStockOverviewList()
}
</script>

<template>
    <div class="stock-management-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-content">
                <el-icon class="header-icon">
                    <TrendCharts />
                </el-icon>
                <h2 class="page-title">股票管理</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-label">最新股价:</span>
                        <span class="stat-value">{{ formatCurrency(latestPrice) }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">股票历史单价记录:</span>
                        <span class="stat-value">{{ stockPriceHistory.length }} 条</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">员工持股:</span>
                        <span class="stat-value">{{ employeeStockPagination.total }} 条记录</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 上半部分：股票价格管理 -->
        <div class="stock-price-section">
            <el-card class="section-card" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="header-left">
                            <el-icon>
                                <TrendCharts />
                            </el-icon>
                            <span>股票价格管理</span>
                        </div>
                        <div class="header-right">
                            <el-button type="primary" :icon="Plus" @click="openStockPriceDialog('add')">
                                添加股价记录
                            </el-button>
                        </div>
                    </div>
                </template>

                <!-- 搜索区域 -->
                <div class="search-container">
                    <el-form :model="stockPriceSearch" class="search-form">
                        <div class="search-row">
                            <el-form-item label="日期范围" class="search-item">
                                <el-date-picker
                                    v-model="stockPriceSearch.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    size="default"
                                />
                            </el-form-item>
                            <el-form-item label="备注" class="search-item">
                                <el-input
                                    v-model="stockPriceSearch.remark"
                                    placeholder="请输入备注关键词"
                                    clearable
                                    size="default"
                                    style="width: 200px"
                                />
                            </el-form-item>
                            <el-form-item class="search-item search-buttons">
                                <el-button type="primary" :icon="Search" @click="searchStockPriceData" size="default">
                                    搜索
                                </el-button>
                                <el-button :icon="RefreshRight" @click="resetStockPriceSearch" size="default">
                                    重置
                                </el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>

                <!-- 股票价格折线图 -->
                <div
                    ref="chartRef"
                    class="chart-container"
                    v-loading="chartLoading"
                    element-loading-text="图表加载中..."
                    @touchstart="handleTouchStart"
                    @touchmove="handleTouchMove"
                    @touchend="handleTouchEnd"
                ></div>

                <!-- 图表范围控制 -->
                <div class="chart-range-container" v-if="stockPriceHistory.length > 0">
                    <!-- 范围信息和快速操作 -->
                    <div class="range-header">
                        <div class="range-info">
                            <span class="range-text">
                                显示范围：{{ chartDisplayData.length }} / {{ stockPriceHistory.length }} 条记录
                                <span class="range-detail">
                                    ({{ stockPriceHistory[chartRange.range[0]]?.time || '' }} ~ {{ stockPriceHistory[chartRange.range[1]]?.time || '' }})
                                </span>
                            </span>
                            <span class="swipe-hint">
                                💡 拖动滑块两端调整显示范围，或滑动图表浏览数据
                            </span>
                        </div>

                        <div class="quick-actions">
                            <el-button-group>
                                <el-button
                                    size="small"
                                    @click="setToEarliest"
                                    title="显示最早的15条数据"
                                >
                                    <el-icon><DArrowLeft /></el-icon>
                                    最早
                                </el-button>
                                <el-button
                                    size="small"
                                    @click="setToLatest"
                                    title="显示最新的15条数据"
                                >
                                    最新
                                    <el-icon><DArrowRight /></el-icon>
                                </el-button>
                            </el-button-group>
                        </div>
                    </div>

                    <!-- 范围滑块 -->
                    <div class="range-slider-container">
                        <div class="range-slider-wrapper">
                            <span class="range-label">{{ stockPriceHistory[0]?.time || '' }}</span>
                            <el-slider
                                v-model="chartRange.range"
                                :min="0"
                                :max="chartRange.maxIndex"
                                :step="1"
                                range
                                :show-tooltip="true"
                                :format-tooltip="(val) => stockPriceHistory[val]?.time || ''"
                                @change="handleRangeSliderChange"
                                class="range-slider"
                            />
                            <span class="range-label">{{ stockPriceHistory[stockPriceHistory.length - 1]?.time || '' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 选中数据详情区域 -->
                <div class="data-detail-section" v-if="selectedDataPoint">
                    <el-card class="detail-card" shadow="never">
                        <template #header>
                            <div class="detail-header">
                                <span>选中数据详情</span>
                                <el-button
                                    link
                                    size="small"
                                    @click="clearSelection"
                                    style="color: #909399;"
                                >
                                    清除选择
                                </el-button>
                            </div>
                        </template>
                        <div class="detail-content">
                            <div class="detail-info">
                                <div class="info-item">
                                    <span class="info-label">日期：</span>
                                    <span class="info-value">{{ selectedDataPoint.time }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">股票单价：</span>
                                    <span class="info-value price-highlight">{{ formatCurrency(selectedDataPoint.unitPrice) }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">备注：</span>
                                    <span class="info-value">{{ selectedDataPoint.remark || '无' }}</span>
                                </div>
                            </div>
                            <div class="detail-actions">
                                <el-button
                                    type="primary"
                                    :icon="Edit"
                                    @click="openStockPriceDialog('edit', selectedDataPoint)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    type="danger"
                                    :icon="Delete"
                                    @click="deleteStockPrice(selectedDataPoint)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 无选中数据提示 -->
                <div class="no-selection-tip" v-else>
                    <el-empty
                        description="点击图表中的数据点查看详细信息"
                        :image-size="80"
                    />
                </div>
            </el-card>
        </div>

        <!-- 下半部分：员工股票管理 -->
        <div class="employee-stock-section">
            <el-card class="section-card" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="header-left">
                            <el-icon>
                                <Coin />
                            </el-icon>
                            <span>员工股票管理</span>
                        </div>
                        <div class="header-right">
                            <el-button type="primary" :icon="Plus" @click="openEmployeeStockDialog('add')">
                                添加员工股票
                            </el-button>
                            <el-button
                                type="warning"
                                class="withdrawal-approval-btn"
                                @click="openWithdrawalAuditDialog"
                            >
                                <el-icon>
                                    <Check />
                                </el-icon>审批股票提现
                                <el-badge v-if="pendingWithdrawalCount > 0" :value="pendingWithdrawalCount" class="approval-badge" />
                            </el-button>
                            <el-button type="info" :icon="Document" @click="openWithdrawalRecordsDialog">
                                员工提现记录
                            </el-button>
                            <el-button type="success" :icon="DataAnalysis" @click="openStockOverviewDialog">
                                持股总览
                            </el-button>
                        </div>
                    </div>
                </template>

                <!-- 搜索区域 -->
                <div class="search-container">
                    <el-form :model="employeeStockSearch" class="search-form">
                        <div class="search-row">
                            <el-form-item label="员工姓名" class="search-item">
                                <el-input
                                    v-model="employeeStockSearch.employeeName"
                                    placeholder="请输入员工姓名"
                                    clearable
                                    size="default"
                                    style="width: 180px"
                                />
                            </el-form-item>
                            <el-form-item label="部门" class="search-item">
                                <el-select
                                    v-model="employeeStockSearch.departmentId"
                                    placeholder="请选择部门"
                                    clearable
                                    size="default"
                                    style="width: 180px"
                                    :loading="loadingDepartments"
                                >
                                    <el-option
                                        v-for="dept in departmentList"
                                        :key="dept.departmentId"
                                        :label="dept.departmentName"
                                        :value="dept.departmentId"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="获取时间" class="search-item">
                                <el-date-picker
                                    v-model="employeeStockSearch.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    size="default"
                                />
                            </el-form-item>
                            <el-form-item class="search-item search-buttons">
                                <el-button type="primary" :icon="Search" @click="loadEmployeeStockData" size="default">
                                    搜索
                                </el-button>
                                <el-button :icon="RefreshRight" @click="resetEmployeeStockSearch" size="default">
                                    重置
                                </el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>

                <!-- 员工股票表格 -->
                <el-table
                    :data="employeeStocks"
                    border
                    stripe
                    style="width: 100%"
                    v-loading="employeeStockLoading"
                    row-key="id"
                >
                    <el-table-column label="序号" type="index" width="60" align="center" />
                    <el-table-column label="员工姓名" prop="employeeName" width="120" align="center" />
                    <el-table-column label="部门" prop="departmentName" width="120" align="center" />
                    <el-table-column label="股票数量" prop="quantity" width="120" align="center">
                        <template #default="{ row }">
                            <el-tag type="primary" size="large">
                                {{ row.quantity.toLocaleString() }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="当前单价" prop="currentPrice" width="120" align="center">
                        <template #default="{ row }">
                            <span class="price-text">{{ formatCurrency(row.currentPrice) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="总价值" prop="totalValue" width="140" align="center">
                        <template #default="{ row }">
                            <span class="total-value">{{ formatCurrency(row.totalValue) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="获取时间" prop="acquisitionTime" width="160" align="center">
                        <template #default="{ row }">
                            {{ formatDateTime(row.acquisitionTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="解禁时间" prop="unlockTime" width="160" align="center">
                        <template #default="{ row }">
                            {{ formatDateTime(row.unlockTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="解禁状态" width="100" align="center">
                        <template #default="{ row }">
                            <el-tag
                                :type="getUnlockStatus(row.unlockTime).type"
                                size="small"
                            >
                                {{ getUnlockStatus(row.unlockTime).text }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip />
                    <el-table-column label="操作" width="140" align="center" fixed="right">
                        <template #default="{ row }">
                            <div style="display: flex; gap: 4px; justify-content: center; align-items: center;">
                                <el-button
                                    type="primary"
                                    size="small"
                                    :icon="Edit"
                                    @click="openEmployeeStockDialog('edit', row)"
                                    style="padding: 4px 8px; font-size: 12px;"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    type="danger"
                                    size="small"
                                    :icon="Delete"
                                    @click="deleteEmployeeStock(row)"
                                    style="padding: 4px 8px; font-size: 12px;"
                                >
                                    删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        background
                        layout="total, sizes, prev, pager, next, jumper"
                        :current-page="employeeStockPagination.page"
                        :page-size="employeeStockPagination.size"
                        :total="employeeStockPagination.total"
                        :page-sizes="[10, 20, 50, 100]"
                        @size-change="handleEmployeeStockSizeChange"
                        @current-change="handleEmployeeStockCurrentChange"
                    />
                </div>
            </el-card>
        </div>

        <!-- 股票价格对话框 -->
        <el-dialog
            v-model="stockPriceDialogVisible"
            :title="stockPriceDialogType === 'add' ? '添加股价记录' : '编辑股价记录'"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-form
                ref="stockPriceFormRef"
                :model="stockPriceForm"
                :rules="stockPriceRules"
                label-width="100px"
            >
                <el-form-item label="日期" prop="time">
                    <el-date-picker
                        v-model="stockPriceForm.time"
                        type="date"
                        placeholder="选择日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="股票单价" prop="unitPrice">
                    <el-input
                        v-model="stockPriceForm.unitPrice"
                        placeholder="请输入股票单价"
                        type="number"
                        step="0.01"
                    >
                        <template #prepend>¥</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="stockPriceForm.remark"
                        placeholder="请输入备注"
                        type="textarea"
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="stockPriceDialogVisible = false" :disabled="stockPriceFormLoading">取消</el-button>
                    <el-button type="primary" @click="saveStockPrice" :loading="stockPriceFormLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 员工股票对话框 -->
        <el-dialog
            v-model="employeeStockDialogVisible"
            :title="employeeStockDialogType === 'add' ? '添加员工股票' : '编辑员工股票'"
            width="600px"
            :close-on-click-modal="false"
            @close="handleEmployeeStockDialogClose"
        >
            <el-form
                ref="employeeStockFormRef"
                :model="employeeStockForm"
                :rules="employeeStockRules"
                label-width="100px"
            >
                <el-form-item label="部门筛选">
                    <el-select
                        v-model="selectedDepartmentId"
                        placeholder="选择部门筛选员工（可选）"
                        clearable
                        style="width: 100%"
                        :loading="loadingDepartments"
                        @change="handleDepartmentFilterChange"
                    >
                        <el-option
                            v-for="dept in departmentList"
                            :key="dept.departmentId"
                            :label="dept.departmentName"
                            :value="dept.departmentId"
                        />
                    </el-select>
                </el-form-item>

                <!-- 显示当前使用的股票价格信息 -->
                <el-form-item label="股票价格">
                    <div v-if="stockPriceHistory.length > 0" class="stock-price-info">
                        <el-tag type="primary" size="small">
                            {{ stockPriceHistory[stockPriceHistory.length - 1].time }} - {{ formatCurrency(stockPriceHistory[stockPriceHistory.length - 1].unitPrice) }}
                        </el-tag>
                        <el-text type="info" size="small" style="margin-left: 8px;">
                            (自动使用最新股票价格)
                        </el-text>
                    </div>
                    <div v-else class="stock-price-info">
                        <el-text type="warning" size="small">
                            请先添加股票价格记录
                        </el-text>
                    </div>
                </el-form-item>

                <!-- 员工选择：统一使用多选模式 -->
                <el-form-item label="员工" prop="employeeIds">
                    <div v-if="employeeStockDialogType === 'edit'" class="form-tip">
                        <el-text type="info" size="small"></el-text>
                    </div>
                    <el-select
                        v-model="employeeStockForm.employeeIds"
                        :placeholder="selectedDepartmentId ? '点击下拉选择部门员工或输入姓名搜索' : '点击下拉选择员工或输入姓名搜索'"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        :max-collapse-tags="3"
                        :multiple-limit="employeeStockDialogType === 'edit' ? 1 : 0"
                        remote
                        filterable
                        :remote-method="handleEmployeeRemoteSearch"
                        :loading="loadingEmployees"
                        clearable
                        style="width: 100%"
                        @visible-change="handleEmployeeSelectVisibleChange"
                    >
                        <template #empty>
                            <div class="empty-text">
                                <p v-if="!employeeSearchQuery && employeeList.length === 0">
                                    {{ selectedDepartmentId ? '点击下拉加载部门员工列表' : '点击下拉加载员工列表' }}
                                </p>
                                <p v-else-if="employeeSearchQuery && employeeList.length === 0">
                                    {{ selectedDepartmentId ? '该部门未找到匹配的员工' : '未找到匹配的员工' }}
                                </p>
                                <p v-else>暂无数据</p>
                            </div>
                        </template>
                        <!-- 员工选项列表 -->
                        <div
                            v-if="employeeList.length > 0"
                            class="employee-options-container"
                            @scroll="handleEmployeeScroll"
                        >
                            <el-option
                                v-for="employee in employeeList"
                                :key="employee.employeeId"
                                :label="getEmployeeDisplayLabel(employee)"
                                :value="employee.employeeId"
                                :class="{ 'resigned-employee': employee.isResigned }"
                            >
                                <span style="float: left">{{ employee.name }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">
                                    {{ getEmployeeDepartmentPosition(employee) }}
                                    <el-tag v-if="employee.isResigned" type="danger" size="small" style="margin-left: 5px">已离职</el-tag>
                                </span>
                            </el-option>

                            <!-- 加载更多提示 -->
                            <div
                                v-if="employeeHasMore"
                                class="load-more-option"
                                @click="loadMoreEmployees"
                            >
                                <el-icon v-if="loadingEmployees" class="is-loading">
                                    <Loading />
                                </el-icon>
                                <span>{{ loadingEmployees ? '加载中...' : '滚动或点击加载更多' }}</span>
                            </div>
                        </div>
                    </el-select>
                </el-form-item>
                <el-form-item label="股票数量" prop="quantity">
                    <el-input
                        v-model="employeeStockForm.quantity"
                        placeholder="请输入股票数量"
                        type="number"
                    />
                </el-form-item>
                <el-form-item label="获取时间" prop="acquisitionTime">
                    <el-date-picker
                        v-model="employeeStockForm.acquisitionTime"
                        type="datetime"
                        placeholder="选择获取时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="解禁时间" prop="unlockTime">
                    <el-date-picker
                        v-model="employeeStockForm.unlockTime"
                        type="datetime"
                        placeholder="选择解禁时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="employeeStockForm.remark"
                        placeholder="请输入备注"
                        type="textarea"
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="employeeStockDialogVisible = false" :disabled="employeeStockFormLoading">取消</el-button>
                    <el-button type="primary" @click="saveEmployeeStock" :loading="employeeStockFormLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 股票提现审核弹窗 -->
        <el-dialog
            v-model="withdrawalAuditDialogVisible"
            title="股票提现审核"
            width="1200px"
            :close-on-click-modal="false"
        >
            <!-- 搜索区域 -->
            <div class="search-container" style="margin-bottom: 20px;">
                <el-form :model="withdrawalQueryForm" class="search-form">
                    <div class="search-row" style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <el-form-item label="员工姓名">
                                <el-input v-model="withdrawalQueryForm.employeeName" placeholder="请输入员工姓名" style="width: 150px;" />
                            </el-form-item>
                            <el-form-item label="申请日期">
                                <el-date-picker
                                    v-model="withdrawalQueryForm.startDate"
                                    type="date"
                                    placeholder="开始日期"
                                    style="width: 140px;"
                                />
                                <span style="margin: 0 8px;">至</span>
                                <el-date-picker
                                    v-model="withdrawalQueryForm.endDate"
                                    type="date"
                                    placeholder="结束日期"
                                    style="width: 140px;"
                                />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" :icon="Search" @click="searchWithdrawals">搜索</el-button>
                                <el-button :icon="RefreshRight" @click="resetWithdrawalSearch">重置</el-button>
                            </el-form-item>
                        </div>

                        <!-- 批量操作按钮移到右边 -->
                        <div style="display: flex; gap: 8px;">
                            <el-button
                                type="success"
                                :disabled="selectedWithdrawals.length === 0"
                                @click="batchAuditWithdrawals('APPROVE')"
                            >
                                批量批准 ({{ selectedWithdrawals.length }})
                            </el-button>
                            <el-button
                                type="danger"
                                :disabled="selectedWithdrawals.length === 0"
                                @click="batchAuditWithdrawals('REJECT')"
                            >
                                批量拒绝 ({{ selectedWithdrawals.length }})
                            </el-button>
                        </div>
                    </div>
                </el-form>
            </div>

            <!-- 提现申请列表 -->
            <el-table
                :data="withdrawalList"
                v-loading="withdrawalAuditLoading"
                @selection-change="handleWithdrawalSelectionChange"
                style="width: 100%"
            >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="employeeName" label="员工姓名" width="100" align="center" />
                <el-table-column prop="departmentName" label="部门" width="120" align="center">
                    <template #default="{ row }">
                        {{ row.departmentName || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" label="提现数量" width="100" align="center">
                    <template #default="{ row }">
                        {{ row.quantity }} 股
                    </template>
                </el-table-column>
                <el-table-column prop="unitPrice" label="申请时股价" width="120" align="center">
                    <template #default="{ row }">
                        {{ formatCurrency(row.unitPrice) }}
                    </template>
                </el-table-column>
                <el-table-column prop="totalAmount" label="申请金额" width="120" align="center">
                    <template #default="{ row }">
                        {{ formatCurrency(row.totalAmount) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="formatWithdrawalStatus(row.status).type">
                            {{ formatWithdrawalStatus(row.status).text }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="applyTime" label="申请时间" width="150" align="center">
                    <template #default="{ row }">
                        {{ formatDateTime(row.applyTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
                <el-table-column label="操作" width="160" align="center" fixed="right">
                    <template #default="{ row }">
                        <div v-if="row.status === 'PENDING'" style="display: flex; gap: 4px; justify-content: center;">
                            <el-button
                                type="success"
                                size="small"
                                @click="auditSingleWithdrawal(row, 'APPROVE')"
                            >
                                批准
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click="auditSingleWithdrawal(row, 'REJECT')"
                            >
                                拒绝
                            </el-button>
                        </div>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" style="margin-top: 20px;">
                <el-pagination
                    v-model:current-page="withdrawalPagination.page"
                    v-model:page-size="withdrawalPagination.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="withdrawalPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleWithdrawalSizeChange"
                    @current-change="handleWithdrawalPageChange"
                />
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="withdrawalAuditDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 审核确认弹窗 -->
        <el-dialog
            v-model="auditDialogVisible"
            :title="auditForm.action === 'APPROVE' ? '确认批准' : '确认拒绝'"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="80px">
                <div style="margin-bottom: 16px;">
                    <p>您确定要{{ auditForm.action === 'APPROVE' ? '批准' : '拒绝' }}以下提现申请吗？</p>
                    <p style="color: #666; font-size: 14px;">
                        共选择了 {{ selectedWithdrawals.length }} 个申请
                    </p>
                </div>

                <el-form-item
                    v-if="auditForm.action === 'REJECT'"
                    label="拒绝理由"
                    prop="rejectReason"
                >
                    <el-input
                        v-model="auditForm.rejectReason"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入拒绝理由"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="auditDialogVisible = false">取消</el-button>
                    <el-button
                        :type="auditForm.action === 'APPROVE' ? 'success' : 'danger'"
                        @click="submitAudit"
                    >
                        确认{{ auditForm.action === 'APPROVE' ? '批准' : '拒绝' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 员工提现记录弹窗 -->
        <el-dialog
            v-model="withdrawalRecordsDialogVisible"
            title="员工提现记录"
            width="1400px"
            :close-on-click-modal="false"
        >
            <!-- 搜索区域 -->
            <div class="search-container" style="margin-bottom: 20px;">
                <el-form :model="withdrawalRecordsQueryForm" class="search-form">
                    <div class="search-row">
                        <el-form-item label="申请状态">
                            <el-select v-model="withdrawalRecordsQueryForm.status" placeholder="请选择状态" clearable style="width: 150px;">
                                <el-option label="待审核" value="PENDING" />
                                <el-option label="已批准" value="APPROVED" />
                                <el-option label="已拒绝" value="REJECTED" />
                                <el-option label="已取消" value="CANCELLED" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="员工姓名">
                            <el-input v-model="withdrawalRecordsQueryForm.employeeName" placeholder="请输入员工姓名" style="width: 150px;" />
                        </el-form-item>
                        <el-form-item label="申请日期">
                            <el-date-picker
                                v-model="withdrawalRecordsQueryForm.startDate"
                                type="date"
                                placeholder="开始日期"
                                style="width: 140px;"
                            />
                            <span style="margin: 0 8px;">至</span>
                            <el-date-picker
                                v-model="withdrawalRecordsQueryForm.endDate"
                                type="date"
                                placeholder="结束日期"
                                style="width: 140px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" :icon="Search" @click="searchWithdrawalRecords">搜索</el-button>
                            <el-button :icon="RefreshRight" @click="resetWithdrawalRecordsSearch">重置</el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </div>

            <!-- 提现记录列表 -->
            <el-table
                :data="withdrawalRecordsList"
                v-loading="withdrawalRecordsLoading"
                style="width: 100%"
            >
                <el-table-column prop="employeeName" label="员工姓名" width="100" align="center" />
                <el-table-column prop="departmentName" label="部门" width="120" align="center">
                    <template #default="{ row }">
                        {{ row.departmentName || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="quantity" label="提现数量" width="100" align="center">
                    <template #default="{ row }">
                        {{ row.quantity }} 股
                    </template>
                </el-table-column>
                <el-table-column prop="unitPrice" label="申请时股价" width="120" align="center">
                    <template #default="{ row }">
                        {{ formatCurrency(row.unitPrice) }}
                    </template>
                </el-table-column>
                <el-table-column prop="totalAmount" label="申请金额" width="120" align="center">
                    <template #default="{ row }">
                        {{ formatCurrency(row.totalAmount) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="formatWithdrawalStatus(row.status).type">
                            {{ formatWithdrawalStatus(row.status).text }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="applyTime" label="申请时间" width="150" align="center">
                    <template #default="{ row }">
                        {{ formatDateTime(row.applyTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="150" align="center">
                    <template #default="{ row }">
                        {{ row.auditTime ? formatDateTime(row.auditTime) : '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="auditorName" label="审核人" width="100" align="center">
                    <template #default="{ row }">
                        {{ row.auditorName || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
                <el-table-column prop="rejectReason" label="拒绝理由" min-width="150" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.rejectReason || '-' }}
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" style="margin-top: 20px;">
                <el-pagination
                    v-model:current-page="withdrawalRecordsPagination.page"
                    v-model:page-size="withdrawalRecordsPagination.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="withdrawalRecordsPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleWithdrawalRecordsSizeChange"
                    @current-change="handleWithdrawalRecordsPageChange"
                />
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="withdrawalRecordsDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 持股总览弹窗 -->
        <el-dialog
            v-model="stockOverviewDialogVisible"
            title="持股总览"
            width="1300px"
            :close-on-click-modal="false"
            top="5vh"
            :destroy-on-close="true"
            class="stock-overview-dialog"
        >
            <!-- 搜索区域 -->
            <div class="search-container">
                <el-form :model="stockOverviewQueryForm" class="search-form" inline label-width="80px">
                    <el-form-item label="员工姓名" class="search-item">
                        <el-input
                            v-model="stockOverviewQueryForm.employeeName"
                            placeholder="请输入员工姓名"
                            clearable
                            style="width: 200px;"
                            @keyup.enter="searchStockOverview"
                        />
                    </el-form-item>
                    <el-form-item label="部门" class="search-item">
                        <el-select
                            v-model="stockOverviewQueryForm.departmentId"
                            placeholder="请选择部门"
                            clearable
                            style="width: 180px;"
                            :loading="loadingDepartments"
                        >
                            <el-option
                                v-for="dept in departmentList"
                                :key="dept.departmentId"
                                :label="dept.departmentName"
                                :value="dept.departmentId"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="search-buttons">
                        <el-button type="primary" :icon="Search" @click="searchStockOverview" :loading="stockOverviewLoading">
                            搜索
                        </el-button>
                        <el-button :icon="RefreshRight" @click="resetStockOverviewSearch" :disabled="stockOverviewLoading">
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 持股总览表格 -->
            <el-table
                :data="stockOverviewList"
                v-loading="stockOverviewLoading"
                style="width: 100%"
                border
                stripe
                :header-cell-style="{ background: '#f8f9fa', fontWeight: '600', textAlign: 'center', height: '40px' }"
                :row-style="{ height: '50px' }"
                class="stock-overview-table"
            >
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="employeeName" label="员工姓名" width="100" align="center">
                    <template #default="{ row }">
                        <span class="employee-name">{{ row.employeeName || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="departmentName" label="部门" width="120" align="center">
                    <template #default="{ row }">
                        <span class="department-name">{{ row.departmentName || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="已解禁股票" width="200" align="center">
                    <template #default="{ row }">
                        <div class="stock-data-cell unlocked">
                            <span class="stock-quantity">{{ (row.unlockedQuantity || 0).toLocaleString() }} 股</span>
                            <span class="separator">|</span>
                            <span class="stock-value">{{ formatCurrency((row.unlockedQuantity || 0) * (latestPrice || 0)) }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="未解禁股票" width="200" align="center">
                    <template #default="{ row }">
                        <div class="stock-data-cell locked">
                            <span class="stock-quantity">{{ (row.lockedQuantity || 0).toLocaleString() }} 股</span>
                            <span class="separator">|</span>
                            <span class="stock-value">{{ formatCurrency((row.lockedQuantity || 0) * (latestPrice || 0)) }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="剩余可提现" width="200" align="center">
                    <template #default="{ row }">
                        <div class="stock-data-cell available">
                            <span class="stock-quantity">{{ (row.availableQuantity || 0).toLocaleString() }} 股</span>
                            <span class="separator">|</span>
                            <span class="stock-value">{{ formatCurrency((row.availableQuantity || 0) * (latestPrice || 0)) }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="已提现" width="200" align="center">
                    <template #default="{ row }">
                        <div class="stock-data-cell withdrawn">
                            <span class="stock-quantity">{{ (row.withdrawnQuantity || 0).toLocaleString() }} 股</span>
                            <span class="separator">|</span>
                            <span class="stock-value">{{ formatCurrency(row.withdrawnTotalValue || 0) }}</span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                <el-pagination
                    v-model:current-page="stockOverviewPagination.page"
                    v-model:page-size="stockOverviewPagination.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="stockOverviewPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleStockOverviewSizeChange"
                    @current-change="handleStockOverviewPageChange"
                />
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="stockOverviewDialogVisible = false" size="default">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.stock-management-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 100px);
}

/* 页面标题 */
.page-header {
    margin-bottom: 20px;
}

.header-content {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-icon {
    font-size: 28px;
    margin-right: 15px;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.stats-container {
    display: flex;
    gap: 15px;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    min-width: 140px;
}

.stat-label {
    font-size: 14px;
    margin-right: 8px;
    opacity: 0.9;
    white-space: nowrap;
}

.stat-value {
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    white-space: nowrap;
}

/* 区域样式 */
.stock-price-section,
.employee-stock-section {
    margin-bottom: 20px;
}

.section-card {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.header-left .el-icon {
    margin-right: 8px;
    font-size: 18px;
    color: #409eff;
}

/* 搜索区域 */
.search-container {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.search-form {
    margin: 0;
}

.search-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.search-item {
    margin-bottom: 0 !important;
    margin-right: 0 !important;
}

.search-item :deep(.el-form-item__label) {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.search-item :deep(.el-form-item__content) {
    line-height: 32px;
}

.search-buttons .el-button {
    margin-left: 8px;
}

.search-buttons .el-button:first-child {
    margin-left: 0;
}

/* 图表样式 */
.chart-container {
    width: 100%;
    height: 400px;
    background-color: #fff;
    margin-bottom: 16px;
}

/* 图表范围控制样式 */
.chart-range-container {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    padding: 16px;
}

.range-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;
}

.range-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.range-text {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.range-detail {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
}

.swipe-hint {
    font-size: 12px;
    color: #909399;
    font-style: italic;
    opacity: 0.8;
}

.quick-actions .el-button {
    padding: 6px 12px;
    font-size: 13px;
}

.quick-actions .el-button:hover {
    background-color: #409eff;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.range-slider-container {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.range-slider-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.range-label {
    font-size: 12px;
    color: #909399;
    white-space: nowrap;
    min-width: 80px;
    text-align: center;
}

.range-slider {
    flex: 1;
}

/* 范围滑块样式优化 */
.range-slider :deep(.el-slider__runway) {
    background-color: #e4e7ed;
    height: 8px;
    border-radius: 4px;
}

.range-slider :deep(.el-slider__bar) {
    background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
    height: 8px;
    border-radius: 4px;
}

.range-slider :deep(.el-slider__button) {
    border: 3px solid #409eff;
    background-color: #fff;
    width: 18px;
    height: 18px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.range-slider :deep(.el-slider__button:hover) {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.range-slider :deep(.el-slider__button-wrapper:nth-child(2) .el-slider__button) {
    border-color: #67c23a;
}

.range-slider :deep(.el-slider__button-wrapper:nth-child(2) .el-slider__button:hover) {
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

/* 数据详情区域样式 */
.data-detail-section {
    margin-top: 20px;
}

.detail-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;
}

.detail-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.detail-info {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
}

.info-label {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
    min-width: 80px;
}

.info-value {
    color: #303133;
    font-weight: 400;
}

.price-highlight {
    color: #409eff;
    font-weight: 600;
    font-size: 16px;
}

.detail-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.no-selection-tip {
    margin-top: 20px;
    padding: 40px 0;
    text-align: center;
    background-color: #fafbfc;
    border-radius: 8px;
    border: 1px dashed #e4e7ed;
}

/* 表格样式 */
.price-text {
    font-weight: 600;
    color: #409eff;
}

.total-value {
    font-weight: 700;
    color: #000000;
    font-size: 16px;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #f0f9ff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格头部样式 */
:deep(.el-table__header-wrapper th) {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #303133;
    border-bottom: 2px solid #e9ecef;
}

/* 分页 */
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

/* 卡片样式优化 */
:deep(.el-card__header) {
    background-color: #fafbfc;
    border-bottom: 1px solid #e9ecef;
    padding: 16px 20px;
}

:deep(.el-card__body) {
    padding: 20px;
}

/* 员工选择下拉框样式 */
.empty-text {
    padding: 10px;
    text-align: center;
    color: #909399;
    font-size: 14px;
}

.empty-text p {
    margin: 0;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stock-management-container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        padding: 15px 20px;
    }

    .stats-container {
        margin-top: 15px;
        align-self: stretch;
        flex-direction: column;
        gap: 10px;
    }

    .stat-item {
        justify-content: space-between;
        min-width: auto;
        width: 100%;
    }

    .search-container {
        padding: 15px;
    }

    .search-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .search-item {
        width: 100%;
    }

    .search-item :deep(.el-form-item__content) {
        width: 100%;
    }

    .search-item .el-input,
    .search-item .el-select,
    .search-item .el-date-editor {
        width: 100% !important;
    }

    .search-buttons {
        display: flex;
        gap: 8px;
    }

    .search-buttons .el-button {
        flex: 1;
        margin-left: 0;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .header-right {
        width: 100%;
    }

    .header-right .el-button {
        width: 100%;
    }

    .chart-container {
        height: 300px;
    }

    .chart-range-container {
        padding: 12px;
    }

    .range-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .range-info {
        text-align: center;
    }

    .quick-actions {
        display: flex;
        justify-content: center;
    }

    .range-label {
        font-size: 11px;
        min-width: 60px;
    }

    .detail-content {
        flex-direction: column;
        gap: 15px;
    }

    .detail-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .detail-actions {
        width: 100%;
        justify-content: center;
    }

    .detail-actions .el-button {
        flex: 1;
    }
}

/* 平板尺寸响应式 */
@media (max-width: 1024px) and (min-width: 769px) {
    .search-row {
        gap: 12px;
    }

    .search-item .el-input,
    .search-item .el-select {
        width: 160px !important;
    }

    .search-buttons {
        margin-top: 8px;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 20px;
    }

    .header-icon {
        font-size: 24px;
    }

    .stat-label {
        font-size: 13px;
    }

    .stat-value {
        font-size: 14px;
    }

    .stat-item {
        padding: 6px 12px;
    }

    :deep(.el-table) {
        font-size: 12px;
    }

    .pagination-container {
        overflow-x: auto;
    }

    .chart-container {
        height: 250px;
    }

    .chart-range-container {
        padding: 8px 12px;
    }

    .range-header {
        gap: 8px;
    }

    .quick-actions .el-button {
        padding: 4px 8px;
        font-size: 11px;
    }

    .range-text {
        font-size: 12px;
    }

    .range-detail {
        font-size: 11px;
    }

    .range-label {
        font-size: 10px;
        min-width: 50px;
    }

    .swipe-hint {
        font-size: 11px;
    }

    .no-selection-tip {
        padding: 20px 0;
    }

    /* 员工选择下拉框样式 */
    :deep(.employee-select-dropdown) {
        max-height: 300px;
    }

    .employee-options-container {
        max-height: 250px;
        overflow-y: auto;
    }

    .load-more-option {
        text-align: center;
        padding: 10px;
        color: #409eff;
        cursor: pointer;
        border-top: 1px solid #ebeef5;
        font-size: 12px;
    }

    /* 股票价格信息样式 */
    .stock-price-info {
        display: flex;
        align-items: center;
        padding: 8px 0;
    }

    .load-more-option:hover {
        background-color: #f5f7fa;
    }

    .resigned-employee {
        color: #f56c6c;
    }

    .empty-text {
        text-align: center;
        padding: 10px;
        margin: 0;
    }

    .form-tip {
        margin-bottom: 8px;
    }
}

/* 提现审批按钮样式 - 与备用金审批按钮保持一致的结构 */
.withdrawal-approval-btn {
    background-color: #e6a23c;
    border-color: #e6a23c;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible; /* 确保徽章不被裁剪 */
}

.withdrawal-approval-btn:hover {
    background-color: #ebb563;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 审批徽章样式 - 与备用金审批徽章完全一致 */
.approval-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 10; /* 确保徽章在最上层 */
}

/* 确保按钮容器不会裁剪徽章 */
.header-right {
    overflow: visible;
}

.header-right .el-button {
    overflow: visible;
}

/* 持股总览相关样式 */
.stock-overview-dialog {
    border-radius: 8px;
}

.stock-overview-dialog .el-dialog__body {
    padding: 20px;
}

/* 搜索区域样式 */
.search-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.search-form {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.search-item {
    margin-bottom: 0 !important;
    margin-right: 0 !important;
}

.search-buttons {
    margin-bottom: 0 !important;
    margin-left: auto;
}

/* 表格样式优化 */
.stock-overview-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    table-layout: fixed;
}

/* 强制表格列宽对齐 */
:deep(.stock-overview-table .el-table__header-wrapper) {
    table-layout: fixed;
}

:deep(.stock-overview-table .el-table__body-wrapper) {
    table-layout: fixed;
}

:deep(.stock-overview-table .el-table__header) {
    table-layout: fixed;
    width: 100% !important;
}

:deep(.stock-overview-table .el-table__body) {
    table-layout: fixed;
    width: 100% !important;
}

/* 员工信息样式 */
.employee-name {
    font-weight: 600;
    color: #303133;
}

.department-name {
    color: #606266;
    font-size: 14px;
}

/* 股票数据单元格样式 */
.stock-data-cell {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 0;
    background: none;
    border: none;
    font-size: 14px;
    white-space: nowrap;
    line-height: 1.4;
}

.stock-quantity {
    font-weight: 600;
    color: #409eff;
    font-size: 14px;
}

.separator {
    color: #909399;
    font-weight: normal;
    font-size: 14px;
}

.stock-value {
    font-weight: 600;
    color: #67c23a;
    font-size: 14px;
}

/* 不同类型股票的颜色区分 */
.stock-data-cell.unlocked .stock-quantity {
    color: #0369a1;
}

.stock-data-cell.unlocked .stock-value {
    color: #059669;
}

.stock-data-cell.locked .stock-quantity {
    color: #ca8a04;
}

.stock-data-cell.locked .stock-value {
    color: #eab308;
}

.stock-data-cell.available .stock-quantity {
    color: #2563eb;
}

.stock-data-cell.available .stock-value {
    color: #16a34a;
}

.stock-data-cell.withdrawn .stock-quantity {
    color: #dc2626;
}

.stock-data-cell.withdrawn .stock-value {
    color: #ef4444;
}

/* 分页样式 */
.pagination-container {
    margin-top: 20px;
    text-align: right;
    padding: 16px 0;
    border-top: 1px solid #e9ecef;
}

/* 弹窗底部样式 */
.dialog-footer {
    text-align: right;
    padding: 16px 0;
    border-top: 1px solid #e9ecef;
}

/* 表格行悬停效果 */
:deep(.stock-overview-table .el-table__row:hover) {
    background-color: #f0f9ff !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .stock-overview-dialog {
        width: 95% !important;
        max-width: 1200px;
    }

    .stock-data-cell {
        font-size: 13px;
        gap: 5px;
    }

    .stock-quantity, .stock-value {
        font-size: 13px;
    }

    .separator {
        font-size: 13px;
    }
}

@media (max-width: 1200px) {
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-buttons {
        margin-left: 0;
        text-align: center;
    }

    .pagination-container {
        text-align: center;
    }

    .stock-data-cell {
        flex-direction: column;
        gap: 4px;
        font-size: 12px;
    }

    .separator {
        display: none;
    }

    .stock-quantity, .stock-value {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .stock-overview-dialog {
        width: 95% !important;
        margin: 0 auto;
    }

    .search-container {
        padding: 16px;
    }

    .stock-data-cell {
        font-size: 11px;
    }

    .stock-quantity, .stock-value {
        font-size: 11px;
    }
}
</style>
