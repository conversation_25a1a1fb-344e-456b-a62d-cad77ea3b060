package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.PromotionAttachment;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * {{CHENGQI: 推广附件数据访问层接口}}
 * {{CHENGQI: 任务ID: P4-LD-007}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 17:10:00 +08:00}}
 * {{CHENGQI: 描述: 推广附件数据访问层接口，提供附件的CRUD操作}}
 */
@Mapper
public interface PromotionAttachmentMapper {

    /**
     * 插入附件记录
     *
     * @param attachment 附件信息
     * @return 影响行数
     */
    int insert(PromotionAttachment attachment);

    /**
     * 根据ID更新附件记录
     *
     * @param attachment 附件信息
     * @return 影响行数
     */
    int updateById(PromotionAttachment attachment);

    /**
     * 逻辑删除附件
     *
     * @param id 附件ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 物理删除附件（谨慎使用）
     *
     * @param id 附件ID
     * @return 影响行数
     */
    int physicalDeleteById(@Param("id") Long id);

    /**
     * 根据ID查询附件记录（包含关联信息）
     *
     * @param id 附件ID
     * @return 附件信息
     */
    PromotionAttachment selectById(@Param("id") Long id);

    /**
     * 根据推广ID查询附件列表
     *
     * @param promotionId 推广ID
     * @return 附件列表
     */
    List<PromotionAttachment> selectByPromotionId(@Param("promotionId") Long promotionId);

    // {{FUTURE_EXTENSION: 分页查询附件列表方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要复杂的附件分页查询功能}}
    // 已删除 - 附件查询通过推广详情接口实现

    /**
     * 更新下载次数
     *
     * @param id 附件ID
     * @return 影响行数
     */
    int incrementDownloadCount(@Param("id") Long id);

    // {{FUTURE_EXTENSION: 附件数量统计方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要附件统计功能}}
    // 已删除 - 统计功能可能在管理后台实现

    // {{FUTURE_EXTENSION: 附件大小统计方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要附件统计功能}}
    // 已删除 - 统计功能可能在管理后台实现

    // {{FUTURE_EXTENSION: 用户附件列表查询方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要按用户查询附件功能}}
    // 已删除 - 附件管理通过推广维度进行

    /**
     * 批量删除推广的所有附件（推广删除时使用）
     *
     * @param promotionId 推广ID
     * @return 影响行数
     */
    int deleteByPromotionId(@Param("promotionId") Long promotionId);
}

// {{CHENGQI: 推广附件数据访问层接口创建完成}}
