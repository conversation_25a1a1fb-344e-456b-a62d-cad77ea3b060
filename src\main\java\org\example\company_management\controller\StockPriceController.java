package org.example.company_management.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.StockPriceDTO;
import org.example.company_management.dto.StockPriceQueryDTO;
import org.example.company_management.service.StockPriceService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 股票价格控制器
 */
@Slf4j
@RestController
@RequestMapping("/stock-price")
@RequiredArgsConstructor
public class StockPriceController {

    private final StockPriceService stockPriceService;

    @PostMapping
    public Result<StockPriceDTO> addStockPrice(
            @RequestBody @Validated(ValidationGroups.Add.class) StockPriceDTO stockPriceDTO) {
        log.info("添加股票价格记录: {}", stockPriceDTO);

        StockPriceDTO result = stockPriceService.addStockPrice(stockPriceDTO);
        return Result.success(result);
    }

    @DeleteMapping("/{id}")
    public Result<Boolean> deleteStockPrice(@PathVariable Long id) {
        log.info("删除股票价格记录: {}", id);

        boolean result = stockPriceService.deleteStockPrice(id);
        return Result.success(result);
    }

    @PutMapping
    public Result<StockPriceDTO> updateStockPrice(
            @RequestBody @Validated(ValidationGroups.Update.class) StockPriceDTO stockPriceDTO) {
        log.info("更新股票价格记录: {}", stockPriceDTO);

        StockPriceDTO result = stockPriceService.updateStockPrice(stockPriceDTO);
        return Result.success(result);
    }

    // 删除：getStockPriceById - 前端不需要单独查看详情

    @GetMapping("/page")
    public Result<PageResult<StockPriceDTO>> getStockPriceByPage(@ModelAttribute StockPriceQueryDTO queryDTO) {
        log.info("分页查询股票价格记录: {}", queryDTO);

        PageResult<StockPriceDTO> result = stockPriceService.getStockPriceByPage(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/all")
    public Result<List<StockPriceDTO>> getAllStockPrices() {
        log.info("查询所有股票价格记录");

        List<StockPriceDTO> result = stockPriceService.getAllStockPrices();
        return Result.success(result);
    }

    @GetMapping("/date-range")
    public Result<List<StockPriceDTO>> getStockPricesByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("查询日期范围内的股票价格记录: {} - {}", startDate, endDate);

        List<StockPriceDTO> result = stockPriceService.getStockPricesByDateRange(startDate, endDate);
        return Result.success(result);
    }

    @GetMapping("/latest")
    public Result<StockPriceDTO> getLatestStockPrice() {
        log.info("查询最新股票价格记录");

        StockPriceDTO result = stockPriceService.getLatestStockPrice();
        return Result.success(result);
    }

    // 删除：getStockPricesByRemark - 通过分页查询的remark参数实现
    // 删除：getStockPriceByTime - 功能重复，可用日期范围查询替代
    // 删除：checkTimeExists - 后端内部验证，前端不需要直接调用
}
