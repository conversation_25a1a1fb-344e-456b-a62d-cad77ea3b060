package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 员工股票实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeStock {
    /**
     * 员工股票记录ID
     */
    private Long id;

    /**
     * 股票价格表ID（外键）
     */
    private Long stockId;

    /**
     * 员工ID（外键）
     */
    private Integer employeeId;

    /**
     * 股票数量
     */
    private Integer quantity;

    /**
     * 获取时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime acquisitionTime;

    /**
     * 解禁时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unlockTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // 以下为非数据库字段，用于关联查询结果
    
    /**
     * 员工姓名（非数据库字段）
     */
    private String employeeName;

    /**
     * 部门名称（非数据库字段）
     */
    private String departmentName;

    /**
     * 当前股票单价（非数据库字段）
     */
    private BigDecimal currentPrice;

    /**
     * 股票总价值（非数据库字段）
     */
    private BigDecimal totalValue;

    /**
     * 是否已解禁（非数据库字段）
     */
    private Boolean isUnlocked;
}
