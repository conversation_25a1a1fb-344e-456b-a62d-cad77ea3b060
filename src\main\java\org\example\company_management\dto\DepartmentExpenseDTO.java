package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.Data;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 部门日常开销数据传输对象
 */
@Data
public class DepartmentExpenseDTO {
    @NotNull(message = "更新时ID不能为空", groups = ValidationGroups.Update.class)
    private Long id;

    // --- Fields for Single Record Operations ---
    @NotNull(message = "部门ID不能为空", groups = {ValidationGroups.Update.class})
    private Long departmentId;

    @NotNull(message = "开销月份不能为空", groups = {ValidationGroups.Update.class})
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate expenseDate;

    // --- Fields for Batch Record Operations ---
    @NotEmpty(message = "批量添加时部门ID列表不能为空", groups = ValidationGroups.CreateBatch.class)
    private List<Long> departmentIds;

    @NotEmpty(message = "批量添加时至少选择一个开销月份", groups = ValidationGroups.CreateBatch.class)
    private List<String> expenseMonths;

    // --- Common Fields for All Operations ---
    @NotBlank(message = "项目名称不能为空", groups = {ValidationGroups.Update.class, ValidationGroups.CreateBatch.class})
    @Size(max = 255, message = "项目名称长度不能超过255个字符", groups = {ValidationGroups.Update.class, ValidationGroups.CreateBatch.class})
    private String itemName;

    @NotNull(message = "金额不能为空", groups = {ValidationGroups.Update.class, ValidationGroups.CreateBatch.class})
    private BigDecimal amount;

    @Size(max = 255, message = "备注长度不能超过255个字符", groups = {ValidationGroups.Update.class, ValidationGroups.CreateBatch.class})
    private String remark;

    // --- Field for Display (populated by service/mapper for response) ---
    private String departmentName;
} 