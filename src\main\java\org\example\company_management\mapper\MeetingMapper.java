package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Meeting;
import org.example.company_management.entity.MeetingParticipant;
import org.example.company_management.entity.MeetingSummary;

import java.util.List;
import java.util.Map;

/**
 * 会议数据访问层接口
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Mapper
public interface MeetingMapper {
    
    /**
     * 插入会议
     * 
     * @param meeting 会议信息
     * @return 影响行数
     */
    int insert(Meeting meeting);
    
    /**
     * 根据ID删除会议
     * 
     * @param id 会议ID
     * @return 影响行数
     */
    int deleteById(Long id);
    
    /**
     * 更新会议信息
     *
     * @param meeting 会议信息
     * @return 影响行数
     */
    int update(Meeting meeting);

    // 状态更新方法已移除，现在由系统定时任务根据时间自动管理

    // 状态更新方法已移除，现在状态由查询时实时计算
    
    /**
     * 根据ID查询会议
     * 
     * @param id 会议ID
     * @return 会议信息
     */
    Meeting selectById(Long id);
    
    /**
     * 分页查询会议列表
     * 
     * @param params 查询参数
     * @return 会议列表
     */
    List<Meeting> selectByPage(Map<String, Object> params);
    
    /**
     * 查询会议总数
     * 
     * @param params 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> params);
    
    /**
     * 查询员工相关的会议列表（分页）
     * 
     * @param params 查询参数
     * @return 会议列表
     */
    List<Meeting> selectEmployeeMeetingsByPage(Map<String, Object> params);
    
    /**
     * 查询员工相关的会议总数
     * 
     * @param params 查询参数
     * @return 总数
     */
    int countEmployeeMeetingsTotal(Map<String, Object> params);
    
    /**
     * 批量插入会议参与者
     * 
     * @param participants 参与者列表
     * @return 影响行数
     */
    int insertParticipants(@Param("participants") List<MeetingParticipant> participants);
    
    /**
     * 根据会议ID删除所有参与者
     * 
     * @param meetingId 会议ID
     * @return 影响行数
     */
    int deleteParticipantsByMeetingId(Long meetingId);
    
    /**
     * 根据会议ID查询参与者列表
     * 
     * @param meetingId 会议ID
     * @return 参与者列表
     */
    List<MeetingParticipant> selectParticipantsByMeetingId(Long meetingId);
    
    /**
     * 插入会议总结
     * 
     * @param summary 会议总结
     * @return 影响行数
     */
    int insertSummary(MeetingSummary summary);
    
    /**
     * 更新会议总结
     * 
     * @param summary 会议总结
     * @return 影响行数
     */
    int updateSummary(MeetingSummary summary);
    
    /**
     * 根据ID删除会议总结
     * 
     * @param id 总结ID
     * @return 影响行数
     */
    int deleteSummaryById(Long id);
    
    /**
     * 根据会议ID查询总结列表
     * 
     * @param meetingId 会议ID
     * @return 总结列表
     */
    List<MeetingSummary> selectSummariesByMeetingId(Long meetingId);
    
    /**
     * 根据会议ID和员工ID查询总结
     * 
     * @param meetingId 会议ID
     * @param employeeId 员工ID
     * @return 会议总结
     */
    MeetingSummary selectSummaryByMeetingIdAndEmployeeId(@Param("meetingId") Long meetingId, 
                                                         @Param("employeeId") Integer employeeId);
}
