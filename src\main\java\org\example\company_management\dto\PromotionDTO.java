package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 推广管理统一数据传输对象}}
 * {{CHENGQI: 任务ID: P4-LD-002}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 更新时间: 2025-01-27 17:30:00 +08:00}}
 * {{CHENGQI: 描述: 推广管理统一DTO，使用验证分组处理不同业务场景}}
 * {{CHENGQI: 更新内容: 添加富文本支持字段(contentType, attachments, contentSummary)}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionDTO {

    /**
     * 推广ID（更新和审核时必需）
     */
    @NotNull(message = "推广ID不能为空", groups = {ValidationGroups.Update.class, ValidationGroups.PromotionAudit.class})
    private Long id;

    /**
     * 推广标题（创建和更新时必需）
     */
    @NotBlank(message = "推广标题不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(min = 2, max = 100, message = "标题长度在2到100个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String title;

    /**
     * 推广内容（创建和更新时必需，支持富文本HTML）
     */
    @NotBlank(message = "推广内容不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(min = 10, max = 50000, message = "内容长度在10到50000个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String content;

    /**
     * 内容类型：TEXT-纯文本，HTML-富文本
     */
    @Pattern(regexp = "^(TEXT|HTML)$", message = "内容类型必须为TEXT或HTML", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String contentType = "TEXT";

    /**
     * 附件文件信息列表
     */
    private List<Object> attachments;

    /**
     * 内容摘要（从富文本中提取的纯文本，用于搜索和列表展示）
     */
    @Size(max = 500, message = "内容摘要长度不能超过500个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String contentSummary;

    /**
     * 图片URL列表（可选，保持向后兼容）
     */
    private List<String> images;

    /**
     * 临时附件路径列表（用于新建推广时的临时附件转换）
     */
    private List<String> tempAttachmentPaths;

    // ==================== 审核相关字段（仅审核时使用） ====================

    /**
     * 审核状态（审核时必需）
     */
    @NotBlank(message = "审核状态不能为空", groups = {ValidationGroups.PromotionAudit.class})
    @Pattern(regexp = "^(已通过|已拒绝)$", message = "审核状态只能是已通过或已拒绝", groups = {ValidationGroups.PromotionAudit.class})
    private String auditStatus;

    /**
     * 拒绝理由（审核拒绝时必需）
     */
    @Size(max = 500, message = "拒绝理由不能超过500字符", groups = {ValidationGroups.PromotionAudit.class})
    private String rejectReason;

    // ==================== 查询响应字段（不需要验证） ====================

    /**
     * 发布人员工ID
     */
    private Integer authorId;

    /**
     * 发布人姓名
     */
    private String authorName;

    /**
     * 发布人所属部门名称（通过author_id动态关联获取）
     */
    private String departmentName;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 提交审核时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitTime;

    /**
     * 审核完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    /**
     * 审核人员工ID
     */
    private Integer auditorId;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // ==================== 分页查询参数字段 ====================

    /**
     * 部门ID列表（用于查询筛选）
     */
    private List<Integer> departmentIds;

    /**
     * 标题关键词（用于模糊搜索）
     */
    private String titleKeyword;

    /**
     * 创建时间开始（用于时间范围筛选）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束（用于时间范围筛选）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime createTimeEnd;

    /**
     * 状态筛选
     */
    private String statusFilter;
}

// {{CHENGQI: 推广管理统一DTO创建完成}}
