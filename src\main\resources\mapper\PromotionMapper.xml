<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.PromotionMapper">

    <!-- {{CHENGQI: 推广管理MyBatis映射文件}} -->
    <!-- {{CHENGQI: 任务ID: P4-LD-005}} -->
    <!-- {{CHENGQI: 负责人: LD}} -->
    <!-- {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}} -->
    <!-- {{CHENGQI: 更新时间: 2025-01-27 17:15:00 +08:00}} -->
    <!-- {{CHENGQI: 描述: 推广管理数据访问层SQL映射}} -->
    <!-- {{CHENGQI: 更新内容: 添加富文本支持字段映射(content_type, attachments, content_summary)}} -->

    <!-- 结果映射 -->
    <resultMap id="PromotionResultMap" type="org.example.company_management.entity.Promotion">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="contentType" column="content_type"/>
        <result property="attachments" column="attachments"/>
        <result property="contentSummary" column="content_summary"/>
        <result property="images" column="images"/>
        <result property="authorId" column="author_id"/>
        <result property="status" column="status"/>
        <result property="submitTime" column="submit_time"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 关联查询字段 -->
        <result property="authorName" column="author_name"/>
        <result property="departmentName" column="department_name"/>
        <result property="auditorName" column="auditor_name"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        p.id, p.title, p.content, p.content_type, p.attachments, p.content_summary, p.images,
        p.author_id, p.status, p.submit_time, p.audit_time, p.auditor_id,
        p.reject_reason, p.create_time, p.update_time
    </sql>

    <!-- 关联查询字段 -->
    <sql id="Join_Column_List">
        <include refid="Base_Column_List"/>,
        e1.name as author_name,
        d.department_name as department_name,
        e2.name as auditor_name
    </sql>

    <!-- 基础关联查询 - 通过author_id动态关联部门信息 -->
    <sql id="Base_Join">
        FROM promotion p
        LEFT JOIN employee e1 ON p.author_id = e1.employee_id
        LEFT JOIN department d ON e1.department_id = d.department_id
        LEFT JOIN employee e2 ON p.auditor_id = e2.employee_id
    </sql>

    <!-- 动态查询条件 - 部门筛选通过员工的department_id进行 -->
    <sql id="Dynamic_Where">
        <where>
            <if test="authorId != null">
                AND p.author_id = #{authorId}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND e1.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                AND p.status = #{status}
            </if>
            <if test="titleKeyword != null and titleKeyword != ''">
                AND p.title LIKE CONCAT('%', #{titleKeyword}, '%')
            </if>
            <if test="createTimeStart != null">
                AND p.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND p.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 插入推广记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Promotion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO promotion (
            title, content, content_type, attachments, content_summary, images,
            author_id, status, submit_time, audit_time, auditor_id,
            reject_reason, create_time, update_time
        ) VALUES (
            #{title}, #{content}, #{contentType}, #{attachments}, #{contentSummary}, #{images},
            #{authorId}, #{status}, #{submitTime}, #{auditTime}, #{auditorId},
            #{rejectReason}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID更新推广记录 -->
    <update id="updateById" parameterType="org.example.company_management.entity.Promotion">
        UPDATE promotion
        <set>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="contentType != null and contentType != ''">content_type = #{contentType},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="contentSummary != null">content_summary = #{contentSummary},</if>
            <if test="images != null">images = #{images},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除推广记录 -->
    <delete id="deleteById">
        DELETE FROM promotion WHERE id = #{id}
    </delete>

    <!-- 根据ID查询推广记录（包含关联信息） -->
    <select id="selectById" resultMap="PromotionResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        WHERE p.id = #{id}
    </select>

    <!-- 分页查询推广列表（支持多条件筛选） -->
    <select id="selectPage" resultMap="PromotionResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        <include refid="Dynamic_Where"/>
        ORDER BY p.create_time DESC
    </select>



    <!-- 修改推广状态 -->
    <update id="updatePromotionStatus">
        UPDATE promotion
        <set>
            status = #{status},
            update_time = #{updateTime},
            <if test="status == '审核中'">
                submit_time = #{updateTime},
            </if>
            <if test="status == '待审核'">
                submit_time = NULL,
                audit_time = NULL,
                auditor_id = NULL,
                reject_reason = NULL,
            </if>
            <if test="status == '已通过' or status == '已拒绝'">
                audit_time = #{updateTime},
                auditor_id = #{operatorId},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 审核推广（支持设置拒绝理由） -->
    <update id="auditPromotionStatus">
        UPDATE promotion
        SET status = #{status},
            audit_time = #{auditTime},
            auditor_id = #{auditorId},
            <if test="rejectReason != null">
                reject_reason = #{rejectReason},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>



    <!-- 查询待审核的推广列表（按条件筛选） -->
    <select id="selectPendingAuditByConditions" resultMap="PromotionResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        WHERE 1=1
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
        <if test="status == null or status == ''">
            AND p.status IN ('待审核', '审核中', '已通过', '已拒绝')
        </if>
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND e1.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="titleKeyword != null and titleKeyword != ''">
            AND p.title LIKE CONCAT('%', #{titleKeyword}, '%')
        </if>
        <if test="createTimeStart != null">
            AND p.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND p.create_time &lt;= #{createTimeEnd}
        </if>
        ORDER BY p.submit_time ASC, p.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计待审核推广总数（按条件筛选） -->
    <select id="countPendingAuditByConditions" resultType="int">
        SELECT COUNT(1)
        <include refid="Base_Join"/>
        WHERE 1=1
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
        <if test="status == null or status == ''">
            AND p.status IN ('待审核', '审核中', '已通过', '已拒绝')
        </if>
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND e1.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="titleKeyword != null and titleKeyword != ''">
            AND p.title LIKE CONCAT('%', #{titleKeyword}, '%')
        </if>
        <if test="createTimeStart != null">
            AND p.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND p.create_time &lt;= #{createTimeEnd}
        </if>
    </select>

    <!-- 查询已通过的推广列表（用于公开展示） -->
    <select id="selectApprovedPromotions" resultMap="PromotionResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        WHERE p.status = '已通过'
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND e1.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="titleKeyword != null and titleKeyword != ''">
            AND p.title LIKE CONCAT('%', #{titleKeyword}, '%')
        </if>
        <if test="createTimeStart != null">
            AND p.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND p.create_time &lt;= #{createTimeEnd}
        </if>
        ORDER BY p.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计已通过推广总数（用于公开展示） -->
    <select id="countApprovedPromotions" resultType="int">
        SELECT COUNT(1)
        <include refid="Base_Join"/>
        WHERE p.status = '已通过'
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND e1.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="titleKeyword != null and titleKeyword != ''">
            AND p.title LIKE CONCAT('%', #{titleKeyword}, '%')
        </if>
        <if test="createTimeStart != null">
            AND p.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND p.create_time &lt;= #{createTimeEnd}
        </if>
    </select>

    <!-- 统计推广数量（按状态分组） -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT p.status, COUNT(*) as count
        FROM promotion p
        LEFT JOIN employee e1 ON p.author_id = e1.employee_id
        <where>
            <if test="authorId != null">
                AND p.author_id = #{authorId}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND e1.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
        GROUP BY p.status
    </select>



</mapper>

<!-- {{CHENGQI: 推广管理MyBatis映射文件创建完成}} -->
