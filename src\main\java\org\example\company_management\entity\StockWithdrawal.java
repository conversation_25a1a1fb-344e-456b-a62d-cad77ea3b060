package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 股票提现申请实体类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockWithdrawal {
    
    /**
     * 提现申请ID
     */
    private Long id;
    
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 提现股票数量
     */
    private Integer quantity;
    
    /**
     * 申请时股票单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 提现总金额
     */
    private BigDecimal totalAmount;

    /**
     * 申请状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝
     */
    private String status;
    
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;
    
    /**
     * 审核人ID
     */
    private Integer auditorId;
    
    /**
     * 拒绝理由
     */
    private String rejectReason;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 扩展字段（用于查询时关联显示）
    
    /**
     * 员工姓名（关联查询）
     */
    private String employeeName;

    /**
     * 员工部门名称（关联查询）
     */
    private String departmentName;

    /**
     * 审核人姓名（关联查询）
     */
    private String auditorName;

    /**
     * 员工手机号（关联查询）
     */
    private String employeePhone;
    
    /**
     * 状态枚举
     */
    public enum Status {
        PENDING("PENDING", "待审核"),
        APPROVED("APPROVED", "已批准"),
        REJECTED("REJECTED", "已拒绝"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status code: " + code);
        }
    }
}
