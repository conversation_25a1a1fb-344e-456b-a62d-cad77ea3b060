package org.example.company_management.utils;

import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Bean拷贝工具类
 */
public class BeanCopyUtils {

    /**
     * 单个对象拷贝
     *
     * @param source 源对象
     * @param clazz  目标类型
     * @param <T>    目标类型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Class<T> clazz) {
        if (source == null) {
            return null;
        }

        try {
            T target = clazz.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Bean拷贝失败", e);
        }
    }

    /**
     * 列表对象拷贝
     *
     * @param sourceList 源列表
     * @param clazz      目标类型
     * @param <T>        目标类型
     * @return 目标列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Class<T> clazz) {
        if (sourceList == null || sourceList.isEmpty()) {
            return List.of();
        }

        return sourceList.stream()
                .map(source -> copyBean(source, clazz))
                .collect(Collectors.toList());
    }
}
