package org.example.company_management.service;

import org.example.company_management.dto.MeetingLocationDTO;
import org.example.company_management.dto.LocationAvailabilityDTO;
import org.example.company_management.entity.MeetingLocation;
import org.example.company_management.utils.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议地点服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface MeetingLocationService {
    
    /**
     * 创建会议地点
     * 
     * @param meetingLocationDTO 会议地点信息
     */
    void createMeetingLocation(MeetingLocationDTO meetingLocationDTO);
    
    /**
     * 根据ID删除会议地点
     * 
     * @param id 地点ID
     */
    void deleteMeetingLocation(Integer id);
    
    /**
     * 更新会议地点
     * 
     * @param id 地点ID
     * @param meetingLocationDTO 会议地点信息
     */
    void updateMeetingLocation(Integer id, MeetingLocationDTO meetingLocationDTO);
    
    /**
     * 根据ID获取会议地点详情
     * 
     * @param id 地点ID
     * @return 会议地点信息
     */
    MeetingLocation getMeetingLocationById(Integer id);
    
    /**
     * 分页查询会议地点列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 地点名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    PageResult<MeetingLocation> getMeetingLocationPage(int pageNum, int pageSize, String name, String status);
    
    /**
     * 获取所有启用的会议地点列表
     *
     * @return 启用的会议地点列表
     */
    List<MeetingLocation> getActiveLocations();

    /**
     * 获取所有会议地点列表（包括禁用的）
     *
     * @return 所有会议地点列表
     */
    List<MeetingLocation> getAllLocations();

    /**
     * 查询地点可用性信息
     *
     * @param locationId 地点ID
     * @param startDate 查询开始日期
     * @param endDate 查询结束日期
     * @param excludeMeetingId 排除的会议ID（编辑模式下使用）
     * @return 地点可用性信息
     */
    LocationAvailabilityDTO getLocationAvailability(Integer locationId, LocalDateTime startDate, LocalDateTime endDate, Long excludeMeetingId);
}
