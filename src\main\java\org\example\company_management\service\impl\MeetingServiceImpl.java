package org.example.company_management.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.LocationAvailabilityDTO;
import org.example.company_management.dto.MeetingDTO;
import org.example.company_management.dto.MeetingParticipantDTO;
import org.example.company_management.dto.MeetingSummaryDTO;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.Meeting;
import org.example.company_management.entity.MeetingParticipant;
import org.example.company_management.entity.MeetingSummary;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.MeetingMapper;
import org.example.company_management.service.MeetingService;
import org.example.company_management.service.MeetingLocationService;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.utils.ThreadLocalUtil;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.Map;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 会议服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Service
public class MeetingServiceImpl implements MeetingService {
    
    @Autowired
    private MeetingMapper meetingMapper;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private MeetingLocationService meetingLocationService;
    
    @Override
    @Transactional
    public void createMeeting(MeetingDTO meetingDTO) {
        // 获取当前登录的管理员信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }
        
        // 验证管理员权限
        if (!"admin".equals(currentEmployee.getRole()) && !"manager".equals(currentEmployee.getRole())) {
            throw new BusinessException("只有管理员或部门负责人才能创建会议");
        }

        // 验证时间冲突
        validateTimeConflict(meetingDTO, null);

        // 创建会议实体
        Meeting meeting = new Meeting();
        meeting.setTitle(meetingDTO.getTitle());
        meeting.setContent(meetingDTO.getContent());
        meeting.setStartTime(meetingDTO.getStartTime());
        meeting.setEndTime(meetingDTO.getEndTime());
        meeting.setLocation(meetingDTO.getLocation());
        meeting.setLocationId(meetingDTO.getLocationId());
        meeting.setCreatorId(currentEmployee.getEmployeeId());
        meeting.setResponsibleId(meetingDTO.getResponsibleId());
        // 状态由查询时实时计算，不需要设置
        meeting.setCreateTime(LocalDateTime.now());
        meeting.setUpdateTime(LocalDateTime.now());
        
        // 插入会议
        int result = meetingMapper.insert(meeting);
        if (result <= 0) {
            throw new BusinessException("创建会议失败");
        }
        
        // 插入参与者
        if (meetingDTO.getParticipants() != null && !meetingDTO.getParticipants().isEmpty()) {
            List<MeetingParticipant> participants = processParticipants(meeting.getId(), meetingDTO.getParticipants());

            int participantResult = meetingMapper.insertParticipants(participants);
            if (participantResult <= 0) {
                throw new BusinessException("添加会议参与者失败");
            }
        }
        
        log.info("管理员 {} 创建了会议: {}", currentEmployee.getName(), meeting.getTitle());
    }

    @Override
    @Transactional
    public void createEmployeeMeeting(MeetingDTO meetingDTO) {
        // 获取当前登录的员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }

        // 员工可以创建会议，不需要特殊权限验证

        // 验证时间冲突
        validateTimeConflict(meetingDTO, null);

        // 创建会议实体
        Meeting meeting = new Meeting();
        meeting.setTitle(meetingDTO.getTitle());
        meeting.setContent(meetingDTO.getContent());
        meeting.setStartTime(meetingDTO.getStartTime());
        meeting.setEndTime(meetingDTO.getEndTime());
        meeting.setLocation(meetingDTO.getLocation());
        meeting.setLocationId(meetingDTO.getLocationId());
        meeting.setCreatorId(currentEmployee.getEmployeeId());
        // 会议负责人自动设置为当前登录员工
        meeting.setResponsibleId(currentEmployee.getEmployeeId());
        // 状态由查询时实时计算，不需要设置
        meeting.setCreateTime(LocalDateTime.now());
        meeting.setUpdateTime(LocalDateTime.now());

        // 插入会议
        int result = meetingMapper.insert(meeting);
        if (result <= 0) {
            throw new BusinessException("创建会议失败");
        }

        // 插入参与者
        if (meetingDTO.getParticipants() != null && !meetingDTO.getParticipants().isEmpty()) {
            List<MeetingParticipant> participants = processParticipants(meeting.getId(), meetingDTO.getParticipants());

            int participantResult = meetingMapper.insertParticipants(participants);
            if (participantResult <= 0) {
                throw new BusinessException("添加会议参与者失败");
            }
        }

        log.info("员工 {} 创建了会议: {}", currentEmployee.getName(), meeting.getTitle());
    }
    
    @Override
    @Transactional
    public void updateMeeting(MeetingDTO meetingDTO) {
        // 获取当前登录的管理员信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }
        
        // 验证会议是否存在
        Meeting existingMeeting = meetingMapper.selectById(meetingDTO.getId());
        if (existingMeeting == null) {
            throw new BusinessException("会议不存在");
        }
        
        // 验证权限：只有创建者或管理员可以修改
        if (!existingMeeting.getCreatorId().equals(currentEmployee.getEmployeeId())
            && !"admin".equals(currentEmployee.getRole())) {
            throw new BusinessException("只有会议创建者或管理员才能修改会议");
        }

        // 验证时间冲突（排除当前会议）
        validateTimeConflict(meetingDTO, meetingDTO.getId());

        // 更新会议信息
        Meeting meeting = new Meeting();
        meeting.setId(meetingDTO.getId());
        meeting.setTitle(meetingDTO.getTitle());
        meeting.setContent(meetingDTO.getContent());
        meeting.setStartTime(meetingDTO.getStartTime());
        meeting.setEndTime(meetingDTO.getEndTime());
        meeting.setLocation(meetingDTO.getLocation());
        meeting.setLocationId(meetingDTO.getLocationId());
        meeting.setResponsibleId(meetingDTO.getResponsibleId());
        // 状态字段已移除，现在由系统根据时间自动管理
        meeting.setUpdateTime(LocalDateTime.now());
        
        int result = meetingMapper.update(meeting);
        if (result <= 0) {
            throw new BusinessException("更新会议失败");
        }
        
        // 更新参与者（先删除再插入）
        if (meetingDTO.getParticipants() != null) {
            meetingMapper.deleteParticipantsByMeetingId(meetingDTO.getId());
            
            if (!meetingDTO.getParticipants().isEmpty()) {
                List<MeetingParticipant> participants = processParticipants(meetingDTO.getId(), meetingDTO.getParticipants());
                meetingMapper.insertParticipants(participants);
            }
        }
        
        log.info("管理员 {} 更新了会议: {}", currentEmployee.getName(), meeting.getTitle());
    }

    @Override
    @Transactional
    public void updateEmployeeMeeting(MeetingDTO meetingDTO, Integer currentEmployeeId) {
        // 获取当前登录的员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }

        // 验证会议是否存在
        Meeting existingMeeting = meetingMapper.selectById(meetingDTO.getId());
        if (existingMeeting == null) {
            throw new BusinessException("会议不存在");
        }

        // 验证权限：只有会议创建者或负责人可以修改
        if (!existingMeeting.getCreatorId().equals(currentEmployee.getEmployeeId())
            && !existingMeeting.getResponsibleId().equals(currentEmployee.getEmployeeId())) {
            throw new BusinessException("只有会议创建者或负责人才能修改会议");
        }

        // 验证时间冲突（排除当前会议）
        validateTimeConflict(meetingDTO, meetingDTO.getId());

        // 更新会议信息
        Meeting meeting = new Meeting();
        meeting.setId(meetingDTO.getId());
        meeting.setTitle(meetingDTO.getTitle());
        meeting.setContent(meetingDTO.getContent());
        meeting.setStartTime(meetingDTO.getStartTime());
        meeting.setEndTime(meetingDTO.getEndTime());
        meeting.setLocation(meetingDTO.getLocation());
        meeting.setLocationId(meetingDTO.getLocationId());
        // 保持原有的创建者和负责人不变
        meeting.setCreatorId(existingMeeting.getCreatorId());
        meeting.setResponsibleId(existingMeeting.getResponsibleId());
        // 状态由查询时实时计算，不需要设置
        meeting.setUpdateTime(LocalDateTime.now());

        int result = meetingMapper.update(meeting);
        if (result <= 0) {
            throw new BusinessException("更新会议失败");
        }

        // 删除原有参与者
        meetingMapper.deleteParticipantsByMeetingId(meetingDTO.getId());

        // 插入新的参与者
        if (meetingDTO.getParticipants() != null && !meetingDTO.getParticipants().isEmpty()) {
            List<MeetingParticipant> participants = processParticipants(meetingDTO.getId(), meetingDTO.getParticipants());

            int participantResult = meetingMapper.insertParticipants(participants);
            if (participantResult <= 0) {
                throw new BusinessException("更新会议参与者失败");
            }
        }

        log.info("员工 {} 更新了会议: {}", currentEmployee.getName(), meeting.getTitle());
    }
    
    @Override
    @Transactional
    public void deleteMeeting(Long id) {
        // 获取当前登录的管理员信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }
        
        // 验证会议是否存在
        Meeting existingMeeting = meetingMapper.selectById(id);
        if (existingMeeting == null) {
            throw new BusinessException("会议不存在");
        }
        
        // 验证权限：只有创建者或管理员可以删除
        if (!existingMeeting.getCreatorId().equals(currentEmployee.getEmployeeId()) 
            && !"admin".equals(currentEmployee.getRole())) {
            throw new BusinessException("只有会议创建者或管理员才能删除会议");
        }
        
        // 删除会议（级联删除参与者和总结）
        int result = meetingMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除会议失败");
        }
        
        log.info("管理员 {} 删除了会议: {}", currentEmployee.getName(), existingMeeting.getTitle());
    }
    
    @Override
    public Meeting getMeetingById(Long id) {
        Meeting meeting = meetingMapper.selectById(id);
        if (meeting == null) {
            throw new BusinessException("会议不存在");
        }

        // 查询参与者列表
        meeting.setParticipants(meetingMapper.selectParticipantsByMeetingId(id));

        // 查询总结列表
        meeting.setSummaries(meetingMapper.selectSummariesByMeetingId(id));

        return meeting;
    }

    @Override
    public PageResult<Meeting> getMeetingPage(int pageNum, int pageSize, String title, String status,
                                              Integer creatorId, String startTime, String endTime) {
        // 状态由查询时实时计算，无需预先更新

        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        if (StringUtils.hasText(title)) {
            params.put("title", title.trim());
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }
        if (creatorId != null) {
            params.put("creatorId", creatorId);
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }

        // 查询总数
        int total = meetingMapper.countTotal(params);

        // 查询当前页数据
        List<Meeting> meetings = meetingMapper.selectByPage(params);

        return new PageResult<>(pageNum, pageSize, total, meetings);
    }

    @Override
    public PageResult<Meeting> getEmployeeMeetingPage(int pageNum, int pageSize, Integer employeeId,
                                                      String status, String title) {
        // 状态由查询时实时计算，无需预先更新

        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("employeeId", employeeId);

        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }
        if (StringUtils.hasText(title)) {
            params.put("title", title.trim());
        }

        // 查询总数
        int total = meetingMapper.countEmployeeMeetingsTotal(params);

        // 查询当前页数据
        List<Meeting> meetings = meetingMapper.selectEmployeeMeetingsByPage(params);

        return new PageResult<>(pageNum, pageSize, total, meetings);
    }

    @Override
    @Transactional
    public void addMeetingSummary(MeetingSummaryDTO summaryDTO) {
        // 获取当前登录员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }

        // 验证会议是否存在
        Meeting meeting = meetingMapper.selectById(summaryDTO.getMeetingId());
        if (meeting == null) {
            throw new BusinessException("会议不存在");
        }

        // 验证会议是否已结束
        if (!"FINISHED".equals(meeting.getStatus())) {
            throw new BusinessException("只有已结束的会议才能添加总结");
        }

        // 检查是否已经添加过总结
        MeetingSummary existingSummary = meetingMapper.selectSummaryByMeetingIdAndEmployeeId(
            summaryDTO.getMeetingId(), currentEmployee.getEmployeeId());
        if (existingSummary != null) {
            throw new BusinessException("您已经为此会议添加过总结，请使用编辑功能");
        }

        // 创建总结实体
        MeetingSummary summary = new MeetingSummary();
        summary.setMeetingId(summaryDTO.getMeetingId());
        summary.setEmployeeId(currentEmployee.getEmployeeId());
        summary.setSummaryContent(summaryDTO.getSummaryContent());
        summary.setCreateTime(LocalDateTime.now());
        summary.setUpdateTime(LocalDateTime.now());

        int result = meetingMapper.insertSummary(summary);
        if (result <= 0) {
            throw new BusinessException("添加会议总结失败");
        }

        log.info("员工 {} 为会议 {} 添加了总结", currentEmployee.getName(), meeting.getTitle());
    }

    @Override
    @Transactional
    public void updateMeetingSummary(MeetingSummaryDTO summaryDTO) {
        // 获取当前登录员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }

        // 验证总结是否存在
        MeetingSummary existingSummary = meetingMapper.selectSummaryByMeetingIdAndEmployeeId(
            summaryDTO.getMeetingId(), currentEmployee.getEmployeeId());
        if (existingSummary == null) {
            throw new BusinessException("总结不存在或您没有权限修改");
        }

        // 更新总结
        MeetingSummary summary = new MeetingSummary();
        summary.setId(existingSummary.getId());
        summary.setSummaryContent(summaryDTO.getSummaryContent());
        summary.setUpdateTime(LocalDateTime.now());

        int result = meetingMapper.updateSummary(summary);
        if (result <= 0) {
            throw new BusinessException("更新会议总结失败");
        }

        log.info("员工 {} 更新了会议总结", currentEmployee.getName());
    }

    @Override
    @Transactional
    public void deleteMeetingSummary(Long id) {
        // 获取当前登录员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            throw new BusinessException("未获取到当前登录用户信息");
        }

        // 验证总结是否存在且属于当前用户
        MeetingSummary existingSummary = meetingMapper.selectSummaryByMeetingIdAndEmployeeId(
            id, currentEmployee.getEmployeeId());
        if (existingSummary == null) {
            throw new BusinessException("总结不存在或您没有权限删除");
        }

        int result = meetingMapper.deleteSummaryById(id);
        if (result <= 0) {
            throw new BusinessException("删除会议总结失败");
        }

        log.info("员工 {} 删除了会议总结", currentEmployee.getName());
    }

    @Override
    public MeetingSummary getMeetingSummaryByMeetingIdAndEmployeeId(Long meetingId, Integer employeeId) {
        return meetingMapper.selectSummaryByMeetingIdAndEmployeeId(meetingId, employeeId);
    }

    // 手动状态更新方法已移除，现在由系统定时任务根据时间自动管理

    /**
     * 处理参与者列表，将部门参与者展开为部门下的所有员工
     *
     * @param meetingId 会议ID
     * @param participantDTOs 参与者DTO列表
     * @return 处理后的参与者列表
     */
    private List<MeetingParticipant> processParticipants(Long meetingId, List<MeetingParticipantDTO> participantDTOs) {
        List<MeetingParticipant> participants = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 使用Set来跟踪已添加的员工，避免重复
        Set<Integer> addedEmployeeIds = new HashSet<>();

        for (MeetingParticipantDTO participantDTO : participantDTOs) {
            if ("DEPARTMENT".equals(participantDTO.getParticipantType())) {
                // 部门参与者：获取部门下的所有员工
                List<Employee> departmentEmployees = employeeService.getByDepartmentId(participantDTO.getParticipantId());
                for (Employee employee : departmentEmployees) {
                    // 检查员工是否已经被添加过
                    if (!addedEmployeeIds.contains(employee.getEmployeeId())) {
                        MeetingParticipant participant = new MeetingParticipant();
                        participant.setMeetingId(meetingId);
                        participant.setParticipantType("EMPLOYEE");
                        participant.setParticipantId(employee.getEmployeeId());
                        participant.setCreateTime(now);
                        participants.add(participant);
                        addedEmployeeIds.add(employee.getEmployeeId());
                    }
                }

                // 同时保留部门参与者记录（用于显示）
                MeetingParticipant deptParticipant = new MeetingParticipant();
                deptParticipant.setMeetingId(meetingId);
                deptParticipant.setParticipantType(participantDTO.getParticipantType());
                deptParticipant.setParticipantId(participantDTO.getParticipantId());
                deptParticipant.setCreateTime(now);
                participants.add(deptParticipant);
            } else {
                // 员工参与者：检查是否已经被添加过
                if (!addedEmployeeIds.contains(participantDTO.getParticipantId())) {
                    MeetingParticipant participant = new MeetingParticipant();
                    participant.setMeetingId(meetingId);
                    participant.setParticipantType(participantDTO.getParticipantType());
                    participant.setParticipantId(participantDTO.getParticipantId());
                    participant.setCreateTime(now);
                    participants.add(participant);
                    addedEmployeeIds.add(participantDTO.getParticipantId());
                }
            }
        }

        return participants;
    }

    // 状态更新方法已移除，现在状态由查询时实时计算

    /**
     * 获取当前登录员工信息
     *
     * @return 当前登录员工信息，如果未登录则返回null
     */
    private Employee getCurrentEmployee() {
        try {
            // 从ThreadLocal中获取员工信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return null;
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return null;
            }

            // 查询员工详细信息
            return employeeService.getById(employeeId);
        } catch (Exception e) {
            log.error("获取当前员工信息失败", e);
            return null;
        }
    }

    /**
     * 验证会议时间冲突
     *
     * @param meetingDTO 会议信息
     * @param excludeMeetingId 排除的会议ID（编辑时使用）
     */
    private void validateTimeConflict(MeetingDTO meetingDTO, Long excludeMeetingId) {
        // 检查必要参数
        if (meetingDTO.getLocationId() == null || meetingDTO.getStartTime() == null) {
            return; // 基本验证由其他验证器处理
        }

        try {
            // 获取地点可用性信息，包括已预订时间段
            LocalDateTime startDate = meetingDTO.getStartTime().toLocalDate().atStartOfDay();
            LocalDateTime endDate = meetingDTO.getEndTime() != null ?
                meetingDTO.getEndTime().toLocalDate().atTime(23, 59, 59) :
                meetingDTO.getStartTime().toLocalDate().atTime(23, 59, 59);

            LocationAvailabilityDTO availability = meetingLocationService.getLocationAvailability(
                meetingDTO.getLocationId(), startDate, endDate, excludeMeetingId);

            // 检查会议日期是否在地点的可用日期范围内
            validateMeetingDateRange(meetingDTO, availability);

            // 检查与已预订时间段的冲突
            List<LocationAvailabilityDTO.BookedTimeSlot> bookedSlots = availability.getBookedTimeSlots();
            if (bookedSlots != null && !bookedSlots.isEmpty()) {
                for (LocationAvailabilityDTO.BookedTimeSlot slot : bookedSlots) {
                    // 跳过当前编辑的会议（编辑模式下）
                    if (Boolean.TRUE.equals(slot.getIsCurrentMeeting())) {
                        continue;
                    }

                    if (isTimeConflict(meetingDTO.getStartTime(), meetingDTO.getEndTime(),
                                     slot.getStartTime(), slot.getEndTime())) {
                        String conflictInfo = String.format("时间段 %s-%s 已被会议「%s」占用",
                            slot.getStartTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")),
                            slot.getEndTime() != null ?
                                slot.getEndTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")) : "未设定结束时间",
                            slot.getBookedByName() != null ? slot.getBookedByName() : "未知");
                        throw new BusinessException("会议时间冲突：" + conflictInfo);
                    }
                }
            }

        } catch (BusinessException e) {
            // 如果是时间冲突的业务异常，重新抛出
            if (e.getMessage().contains("会议时间冲突")) {
                throw e;
            }
            // 其他业务异常（如地点不存在、已禁用等）记录日志但不阻止创建
            log.warn("获取地点可用性信息失败: {}", e.getMessage());
        } catch (Exception e) {
            // 其他异常记录日志但不阻止创建
            log.error("时间冲突检测失败", e);
        }
    }

    /**
     * 检查两个时间段是否冲突
     *
     * @param start1 时间段1开始时间
     * @param end1 时间段1结束时间（可为null）
     * @param start2 时间段2开始时间
     * @param end2 时间段2结束时间（可为null）
     * @return 是否冲突
     */
    private boolean isTimeConflict(LocalDateTime start1, LocalDateTime end1,
                                 LocalDateTime start2, LocalDateTime end2) {
        // 如果任一开始时间为null，不检查冲突
        if (start1 == null || start2 == null) {
            return false;
        }

        // 处理结束时间为null的情况（假设持续1小时）
        LocalDateTime effectiveEnd1 = end1 != null ? end1 : start1.plusHours(1);
        LocalDateTime effectiveEnd2 = end2 != null ? end2 : start2.plusHours(1);

        // 两个时间段冲突的条件：start1 < end2 && start2 < end1
        return start1.isBefore(effectiveEnd2) && start2.isBefore(effectiveEnd1);
    }

    /**
     * 验证会议日期是否在地点的可用日期范围内
     *
     * @param meetingDTO 会议信息
     * @param availability 地点可用性信息
     */
    private void validateMeetingDateRange(MeetingDTO meetingDTO, LocationAvailabilityDTO availability) {
        if (meetingDTO.getStartTime() == null || availability == null) {
            return;
        }

        LocalDate availableStartDate = availability.getAvailableStartDate();
        LocalDate availableEndDate = availability.getAvailableEndDate();

        // 检查开始时间的日期范围
        LocalDate startDate = meetingDTO.getStartTime().toLocalDate();

        // 检查开始日期限制
        if (availableStartDate != null && startDate.isBefore(availableStartDate)) {
            throw new BusinessException(String.format("会议开始日期 %s 早于地点开放开始日期 %s",
                startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                availableStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        }

        // 检查开始日期的结束限制
        if (availableEndDate != null && startDate.isAfter(availableEndDate)) {
            throw new BusinessException(String.format("会议开始日期 %s 晚于地点开放结束日期 %s",
                startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                availableEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        }

        // 如果有结束时间，也需要检查结束时间的日期范围
        if (meetingDTO.getEndTime() != null) {
            LocalDate endDate = meetingDTO.getEndTime().toLocalDate();

            // 检查结束日期的开始限制
            if (availableStartDate != null && endDate.isBefore(availableStartDate)) {
                throw new BusinessException(String.format("会议结束日期 %s 早于地点开放开始日期 %s",
                    endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    availableStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
            }

            // 检查结束日期的结束限制
            if (availableEndDate != null && endDate.isAfter(availableEndDate)) {
                throw new BusinessException(String.format("会议结束日期 %s 晚于地点开放结束日期 %s",
                    endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    availableEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
            }
        }
    }
}
