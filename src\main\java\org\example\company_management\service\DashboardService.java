package org.example.company_management.service;

import org.example.company_management.dto.AchievementDTO;

import java.util.List;
import java.util.Map;

/**
 * 仪表盘服务接口
 */
public interface DashboardService {

    /**
     * 获取首页仪表盘统计数据
     *
     * @return 统计数据集合
     */
    Map<String, Object> getDashboardStats();

    /**
     * 获取当前登录员工的成就列表
     *
     * @param employeeId 员工ID
     * @return 成就列表
     */
    List<AchievementDTO> getEmployeeAchievements(Integer employeeId);
}