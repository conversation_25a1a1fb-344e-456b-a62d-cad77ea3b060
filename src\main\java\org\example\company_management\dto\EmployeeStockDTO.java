package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工股票DTO，用于前端和后端之间的数据传输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeStockDTO {
    /**
     * 员工股票记录ID
     * 添加时不需要，修改时必须
     */
    @NotNull(message = "员工股票记录ID不能为空", groups = {ValidationGroups.Update.class})
    private Long id;

    /**
     * 股票价格表ID（外键）
     * 添加和修改时都必须
     */
    @NotNull(message = "股票价格ID不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private Long stockId;

    /**
     * 员工ID（外键）
     * 单个操作时使用
     */
    @NotNull(message = "员工ID不能为空", groups = {ValidationGroups.Update.class})
    private Integer employeeId;

    /**
     * 员工ID列表（用于批量添加）
     * 批量添加时使用
     */
    @NotEmpty(message = "员工ID列表不能为空", groups = {ValidationGroups.Add.class})
    private List<Integer> employeeIds;

    /**
     * 股票数量
     * 添加和修改时都必须
     */
    @NotNull(message = "股票数量不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Min(value = 1, message = "股票数量必须大于0", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private Integer quantity;

    /**
     * 获取时间
     * 添加和修改时都必须
     */
    @NotNull(message = "获取时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime acquisitionTime;

    /**
     * 解禁时间
     * 添加和修改时都必须
     */
    @NotNull(message = "解禁时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unlockTime;

    /**
     * 备注
     * 可选字段
     */
    @Size(max = 255, message = "备注长度不能超过255个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String remark;

    /**
     * 创建时间（查询时返回）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间（查询时返回）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // 以下为非数据库字段，用于前端显示
    
    /**
     * 员工姓名（查询时返回）
     */
    private String employeeName;

    /**
     * 部门名称（查询时返回）
     */
    private String departmentName;

    /**
     * 当前股票单价（查询时返回）
     */
    private BigDecimal currentPrice;

    /**
     * 股票总价值（查询时返回）
     */
    private BigDecimal totalValue;

    /**
     * 是否已解禁（查询时返回）
     */
    private Boolean isUnlocked;
}
