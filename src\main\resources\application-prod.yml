# =====================================================
# 推广管理系统 - 生产环境配置文件
# 创建时间: 2025-06-21 11:02:34 +08:00
# 版本: v2.0 - 完整独立配置
# 说明: 此配置文件完全独立，不依赖主配置文件
# =====================================================

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    multipart:
      max-file-size: 50MB      # 单个文件最大大小
      max-request-size: 50MB   # 整个请求最大大小
      enabled: true
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Spring框架配置
spring:
  application:
    name: company-management-system
  profiles:
    active: prod

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************************
    username: root
    password: cllcn@123  # 请根据实际情况修改数据库密码
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接池配置 - 生产环境优化
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-Production
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000

  # SQL初始化配置
  sql:
    init:
      mode: never
      continue-on-error: false

  # Web配置
  web:
    resources:
      # 禁用默认静态资源处理，避免与API路径冲突
      add-mappings: false
      cache:
        period: 31536000  # 1年缓存

  # Jackson配置
  jackson:
    # 移除严格的日期格式限制，允许灵活的日期解析（与开发环境保持一致）
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: org.example.company_management.entity
  configuration:
    map-underscore-to-camel-case: true
    # 生产环境不打印SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString

# PageHelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  auto-runtime-dialect: false
  close-conn: true
  # 禁用自动拦截，只对使用PageHelper.startPage()的查询进行分页
  params: count=countSql

# 自定义应用配置
app:
  upload:
    # 物理存储根路径 - 生产环境绝对路径
    path: D:/wwwroot/ManaSysSer/uploads
    # URL访问前缀
    url-prefix: /uploads
    # 应用基础URL - 生产环境地址
    base-url: http://manage.cllcn.net:5174

# 文件上传配置（保持与app.upload.path一致）
file:
  upload:
    path: D:/wwwroot/ManaSysSer/uploads  # 文件上传根路径，使用绝对路径
  access:
    url-prefix: /uploads  # 文件访问URL前缀

# JWT配置
jwt:
  secret: company_management_system_prod_secret_key_2025_secure
  expiration: 86400000  # 24小时 (24 * 60 * 60 * 1000)
  header: Authorization
  token-prefix: Bearer

# 日志配置
logging:
  level:
    root: INFO
    org.example.company_management: INFO
    org.example.company_management.controller: INFO
    org.example.company_management.service: INFO
    org.example.company_management.mapper: WARN
    org.example.company_management.aspect.ApiLogAspect: INFO
    org.springframework: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.mybatis: WARN
    com.zaxxer.hikari: WARN
    org.apache.ibatis: WARN
    org.hibernate: WARN
    com.mysql: WARN
    # 第三方库日志级别
    org.apache.tomcat: WARN
    org.apache.catalina: WARN
    org.apache.coyote: WARN
    org.apache.http: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/company-management-system.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 3GB
  logback:
    rollingpolicy:
      file-name-pattern: logs/company-management-system.%d{yyyy-MM-dd}.%i.log
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 3GB

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
    info:
      enabled: true
    metrics:
      enabled: true
    env:
      enabled: true
      show-values: when-authorized
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
  health:
    diskspace:
      enabled: true
      threshold: 10GB
    db:
      enabled: true

# 性能监控配置
spring.jpa:
  show-sql: false
  hibernate:
    ddl-auto: none
  properties:
    hibernate:
      format_sql: false
      use_sql_comments: false
      generate_statistics: false

# 缓存配置
spring.cache:
  type: simple
  cache-names:
    - userCache
    - departmentCache
    - promotionCache

# 国际化配置
spring.messages:
  basename: messages
  encoding: UTF-8
  cache-duration: 3600

# 安全配置
security:
  # CORS配置
  cors:
    allowed-origins:
      - http://manage.cllcn.net:5173
      - http://manage.cllcn.net:5174
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - "*"
    allow-credentials: true
    max-age: 3600

  # 文件上传安全配置
  file-upload:
    max-file-size: 50MB
    allowed-extensions:
      - jpg
      - jpeg
      - png
      - gif
      - webp
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - zip
      - rar
    blocked-extensions:
      - exe
      - bat
      - cmd
      - sh
      - jsp
      - php
      - asp
      - js
      - html
      - htm

# 系统配置
system:
  # 环境标识
  environment: production
  # 版本信息
  version: 2.0.0
  # 构建时间
  build-time: 2025-06-21T11:02:34+08:00
  # 维护模式
  maintenance-mode: false
  # 调试模式
  debug-mode: false