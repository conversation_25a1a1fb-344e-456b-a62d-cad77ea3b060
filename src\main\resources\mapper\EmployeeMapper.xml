<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.Employee">
        <id column="employee_id" property="employeeId"/>
        <result column="name" property="name"/>
        <result column="email" property="email"/>
        <result column="password" property="password"/>
        <result column="entry_date" property="entryDate"/>
        <result column="exit_date" property="exitDate"/>
        <result column="id_card" property="idCard"/>
        <result column="department_id" property="departmentId"/>
        <result column="position_id" property="positionId"/>
        <result column="logistics_route" property="logisticsRoute"/>
        <result column="status" property="status"/>
        <result column="role" property="role"/>
        <result column="accessible_menu_ids" property="accessibleMenuIdsJson"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <!-- 非数据库字段 -->
        <result column="department_name" property="departmentName"/>
        <result column="position_name" property="positionName"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        e.employee_id, e.name, e.email, e.password, e.entry_date, e.exit_date, e.id_card, 
        e.department_id, e.position_id, e.logistics_route, e.status, e.role, e.create_time, e.update_time,
        e.accessible_menu_ids
    </sql>
    
    <!-- 不包含密码的基础查询列 -->
    <sql id="Base_Column_List_No_Password">
        e.employee_id, e.name, e.phone, e.email, e.entry_date, e.exit_date, e.id_card, 
        e.department_id, e.position_id, e.logistics_route, e.status, e.role, e.create_time, e.update_time,
        e.accessible_menu_ids
    </sql>
    
    <!-- 关联查询列 -->
    <sql id="Join_Column_List">
        <include refid="Base_Column_List"/>,
        d.department_name, p.position_name
    </sql>
    
    <!-- 不包含密码的关联查询列 -->
    <sql id="Join_Column_List_No_Password">
        <include refid="Base_Column_List_No_Password"/>,
        d.department_name, p.position_name
    </sql>

    <!-- 根据ID查询员工 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT 
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.employee_id = #{employeeId} AND e.status != 'Deleted'
    </select>
    
    <!-- 查询所有员工 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.status != 'Deleted'
        ORDER BY e.employee_id
    </select>
    
    <!-- 分页查询员工 -->
    <select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            <if test="name != null and name != ''">
                e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="positionId != null">
                AND e.position_id = #{positionId}
            </if>
            <if test="status != null and status != ''">
                AND e.status = #{status}
            </if>
            AND e.status != 'Deleted'
        </where>
        ORDER BY e.employee_id
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 获取员工总数 -->
    <select id="countTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT COUNT(1) FROM employee e
        <where>
            <if test="name != null and name != ''">
                e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="positionId != null">
                AND e.position_id = #{positionId}
            </if>
            <if test="status != null and status != ''">
                AND e.status = #{status}
            </if>
            AND e.status != 'Deleted'
        </where>
    </select>
    
    <!-- 统计指定职位的员工数量 -->
    <select id="countByPosition" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(1) FROM employee WHERE position_id = #{positionId}
    </select>
    
    <!-- 根据职位查询员工 -->
    <select id="selectByPosition" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT 
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.position_id = #{positionId} AND e.status != 'Deleted'
        ORDER BY e.employee_id
    </select>
    
    <!-- 根据部门查询员工 -->
    <select id="selectByDepartment" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT 
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.department_id = #{departmentId} AND e.status != 'Deleted'
        ORDER BY e.employee_id
    </select>
    
    <!-- 新增员工 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Employee" useGeneratedKeys="true" keyProperty="employeeId">
        INSERT INTO employee (
            name, phone, email, password, entry_date, exit_date, id_card,
            department_id, position_id, logistics_route, role, accessible_menu_ids
        ) VALUES (
            #{name}, #{phone}, #{email}, #{password}, #{entryDate}, #{exitDate}, #{idCard},
            #{departmentId}, #{positionId}, #{logisticsRoute}, #{role}, #{accessibleMenuIdsJson, jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 修改员工 -->
    <update id="update" parameterType="org.example.company_management.entity.Employee">
        UPDATE employee
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="entryDate != null">
                entry_date = #{entryDate},
            </if>
            <if test="exitDate != null">
                exit_date = #{exitDate},
            </if>
            <if test="exitDate == null">
                exit_date = null,
            </if>
            <if test="idCard != null">
                id_card = #{idCard},
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId},
            </if>
            <if test="positionId != null">
                position_id = #{positionId},
            </if>
            <if test="logisticsRoute != null">
                logistics_route = #{logisticsRoute},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
            <if test="accessibleMenuIdsJson != null">
                accessible_menu_ids = #{accessibleMenuIdsJson, jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime}
            </if>
        </set>
        WHERE employee_id = #{employeeId}
    </update>
    
    <!-- 逻辑软删除员工 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE employee
        SET status = 'Deleted',
            exit_date = NOW(),
            update_time = NOW()
        WHERE employee_id = #{employeeId}
    </update>

    <!-- 批量更新员工部门 -->
    <update id="updateEmployeeDepartment">
        UPDATE employee
        SET department_id = #{newDepartmentId},
            update_time = NOW()
        WHERE department_id = #{departmentId}
    </update>
    
    <!-- 根据姓名查询员工 -->
    <select id="selectByName" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT 
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.name = #{name} AND e.status != 'Deleted'
        ORDER BY e.employee_id
    </select>

    <!-- 根据ID查询员工（包含密码，仅用于认证） -->
    <select id="selectByIdForAuth" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT 
            e.employee_id, e.name, e.phone, e.email, e.password, e.entry_date, e.exit_date, e.id_card, 
            e.department_id, e.position_id, e.logistics_route, e.status, e.role, e.create_time, e.update_time,
            e.accessible_menu_ids,
            d.department_name, p.position_name
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.employee_id = #{employeeId}
    </select>
    
    <!-- 分页查询部门下的员工 -->
    <select id="selectByPageAndDepartment" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT 
        e.employee_id, e.name, e.email, e.entry_date, e.exit_date, e.status, 
        d.department_name, p.position_name, e.logistics_route
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        <where>
            e.department_id = #{departmentId}
            <if test="name != null and name != ''">
                AND e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            AND e.status != 'Deleted'
        </where>
        ORDER BY e.employee_id
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 获取部门下员工总数 -->
    <select id="countTotalByDepartment" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT COUNT(1) FROM employee e
        <where>
            e.department_id = #{departmentId}
            <if test="name != null and name != ''">
                AND e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            AND e.status != 'Deleted'
        </where>
    </select>

    <!-- 根据手机号查询员工（包含密码，仅用于认证） -->
    <select id="selectByPhoneForAuth" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Join_Column_List"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.phone = #{phone}
    </select>

    <!-- 根据手机号查询员工 (不包含密码) -->
    <select id="selectByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.phone = #{phone} AND e.status != 'Deleted'
    </select>

    <!-- 根据邮箱查询员工 (主要用于邮箱唯一性校验) -->
    <select id="selectByEmail" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT 
            e.employee_id, e.email
        FROM employee e
        WHERE e.email = #{email}
        LIMIT 1;
    </select>

    <!-- 根据身份证号查询员工 -->
    <select id="selectByIdCard" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.id_card = #{idCard} AND e.status != 'Deleted'
        LIMIT 1 -- 假设身份证号是唯一的，或者业务上只取第一个匹配的
    </select>

    <!-- 根据部门ID列表查询员工详细信息 -->
    <select id="selectEmployeesWithDetailsByDepartmentIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Join_Column_List_No_Password"/>
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.department_id IN
        <foreach item="departmentId" collection="departmentIds" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        AND e.status != 'Deleted'
        ORDER BY e.employee_id
    </select>

    <select id="countActiveEmployeesByDepartmentId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee
        WHERE department_id = #{departmentId} AND status = 'Active'
    </select>

    <!-- New result map for findByIdWithPositionName -->
    <resultMap id="EmployeeWithPositionNameResultMap" type="org.example.company_management.entity.Employee" >
        <id property="employeeId" column="e_employee_id"/>
        <result property="name" column="e_name"/>
        <result property="email" column="e_email"/>
        <result property="entryDate" column="e_entry_date"/>
        <result property="exitDate" column="e_exit_date"/>
        <result property="idCard" column="e_id_card"/>
        <result property="departmentId" column="e_department_id"/>
        <result property="positionId" column="e_position_id"/>
        <result property="logisticsRoute" column="e_logistics_route"/>
        <result property="status" column="e_status"/>
        <result property="role" column="e_role"/>
        <result property="accessibleMenuIdsJson" column="e_accessible_menu_ids"/>
        <result property="phone" column="e_phone"/>
        <result property="createTime" column="e_create_time"/>
        <result property="updateTime" column="e_update_time"/>
        <result property="positionName" column="p_position_name"/>
    </resultMap>

    <select id="findByIdWithPositionName" resultMap="EmployeeWithPositionNameResultMap">
        SELECT
            e.employee_id as e_employee_id,
            e.name as e_name,
            e.email as e_email,
            e.entry_date as e_entry_date,
            e.exit_date as e_exit_date,
            e.id_card as e_id_card,
            e.department_id as e_department_id,
            e.position_id as e_position_id,
            e.logistics_route as e_logistics_route,
            e.status as e_status,
            e.role as e_role,
            e.accessible_menu_ids as e_accessible_menu_ids,
            e.phone as e_phone,
            e.create_time as e_create_time,
            e.update_time as e_update_time,
            p.position_name as p_position_name
        FROM employee e
        LEFT JOIN position p ON e.position_id = p.position_id
        WHERE e.employee_id = #{employeeId}
    </select>

    <select id="findEmployeeIdsByPositionName" resultType="java.lang.Integer">
        SELECT e.employee_id
        FROM employee e
        JOIN position p ON e.position_id = p.position_id
        WHERE p.position_name = #{positionName} AND e.status = 'Active' -- Assuming only active employees
    </select>
</mapper> 