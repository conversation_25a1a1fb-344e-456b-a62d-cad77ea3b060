package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 员工股票详细统计DTO
 * 用于前端和后端之间的数据传输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeStockDetailedStatisticsDTO {
    /**
     * 总股票数量
     */
    private Integer totalQuantity;
    
    /**
     * 总股票价值
     */
    private BigDecimal totalValue;
    
    /**
     * 已解禁股票数量
     */
    private Integer unlockedQuantity;
    
    /**
     * 已解禁股票价值
     */
    private BigDecimal unlockedValue;
    
    /**
     * 未解禁股票数量
     */
    private Integer lockedQuantity;
    
    /**
     * 未解禁股票价值
     */
    private BigDecimal lockedValue;
    
    /**
     * 可提现股票数量
     */
    private Integer availableQuantity;
    
    /**
     * 可提现股票价值
     */
    private BigDecimal availableValue;
    
    /**
     * 已提现股票数量
     */
    private Integer withdrawnQuantity;
    
    /**
     * 已提现股票价值
     */
    private BigDecimal withdrawnValue;
    
    /**
     * 最新股票价格
     */
    private BigDecimal latestStockPrice;
    
    /**
     * 最新股票价格时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate latestStockPriceTime;
    
    /**
     * 股票记录总数
     */
    private Integer totalRecords;
}
