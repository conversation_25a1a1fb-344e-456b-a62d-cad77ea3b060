package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 持股总览查询DTO，用于持股总览的查询条件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOverviewQueryDTO {
    
    /**
     * 页码，默认为1
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认为15
     */
    private Integer pageSize = 15;
    
    /**
     * 员工姓名（模糊搜索）
     */
    private String employeeName;
    
    /**
     * 部门ID（精确匹配）
     */
    private Long departmentId;
    
    /**
     * 排序字段
     * 可选值：employeeName, departmentName, unlockedQuantity, lockedQuantity, 
     *        availableQuantity, withdrawnQuantity, totalQuantity
     */
    private String orderBy;
    
    /**
     * 排序方向
     * 可选值：ASC, DESC
     */
    private String orderDirection = "ASC";
    
    /**
     * 是否只显示有持股的员工
     * 默认为true，只显示有股票记录的员工
     */
    private Boolean onlyWithStock = true;
    
    /**
     * 获取排序SQL片段
     */
    public String getOrderSql() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "e.employee_id ASC";
        }
        
        String column;
        switch (orderBy.toLowerCase()) {
            case "employeename":
                column = "e.name";
                break;
            case "departmentname":
                column = "d.department_name";
                break;
            case "unlockedquantity":
                column = "unlocked_quantity";
                break;
            case "lockedquantity":
                column = "locked_quantity";
                break;
            case "availablequantity":
                column = "available_quantity";
                break;
            case "withdrawnquantity":
                column = "withdrawn_quantity";
                break;
            case "totalquantity":
                column = "(COALESCE(unlocked_quantity, 0) + COALESCE(locked_quantity, 0))";
                break;
            default:
                column = "e.employee_id";
                break;
        }
        
        String direction = "DESC".equalsIgnoreCase(orderDirection) ? "DESC" : "ASC";
        return column + " " + direction;
    }
    
    /**
     * 验证分页参数
     */
    public void validatePagination() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 15;
        }
        if (pageSize > 100) {
            pageSize = 100; // 限制最大页面大小
        }
    }
    
    /**
     * 获取员工姓名的模糊查询条件
     */
    public String getEmployeeNameLike() {
        if (employeeName == null || employeeName.trim().isEmpty()) {
            return null;
        }
        return "%" + employeeName.trim() + "%";
    }
}
