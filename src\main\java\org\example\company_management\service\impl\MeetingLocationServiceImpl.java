package org.example.company_management.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.MeetingLocationDTO;
import org.example.company_management.dto.LocationAvailabilityDTO;
import org.example.company_management.entity.MeetingLocation;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.MeetingLocationMapper;
import org.example.company_management.service.MeetingLocationService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会议地点服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Service
public class MeetingLocationServiceImpl implements MeetingLocationService {
    
    @Autowired
    private MeetingLocationMapper meetingLocationMapper;
    
    @Override
    @Transactional
    public void createMeetingLocation(MeetingLocationDTO meetingLocationDTO) {
        // 检查地点名称是否重复
        MeetingLocation existingLocation = meetingLocationMapper.selectByName(meetingLocationDTO.getName(), null);
        if (existingLocation != null) {
            throw new BusinessException("地点名称已存在");
        }
        
        // 验证时间设置
        validateTimeSettings(meetingLocationDTO.getOpenTime(), meetingLocationDTO.getCloseTime());
        
        // 验证可用天数设置
        validateAvailableDays(meetingLocationDTO.getAvailableDays());

        // 验证日期范围设置
        validateDateRange(meetingLocationDTO.getAvailableStartDate(), meetingLocationDTO.getAvailableEndDate());

        MeetingLocation meetingLocation = new MeetingLocation();
        BeanUtils.copyProperties(meetingLocationDTO, meetingLocation);
        
        int result = meetingLocationMapper.insert(meetingLocation);
        if (result <= 0) {
            throw new BusinessException("创建会议地点失败");
        }
    }
    
    @Override
    @Transactional
    public void deleteMeetingLocation(Integer id) {
        MeetingLocation meetingLocation = meetingLocationMapper.selectById(id);
        if (meetingLocation == null) {
            throw new BusinessException("会议地点不存在");
        }
        
        int result = meetingLocationMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除会议地点失败");
        }
    }
    
    @Override
    @Transactional
    public void updateMeetingLocation(Integer id, MeetingLocationDTO meetingLocationDTO) {
        MeetingLocation existingLocation = meetingLocationMapper.selectById(id);
        if (existingLocation == null) {
            throw new BusinessException("会议地点不存在");
        }
        
        // 检查地点名称是否重复（排除当前记录）
        MeetingLocation duplicateLocation = meetingLocationMapper.selectByName(meetingLocationDTO.getName(), id);
        if (duplicateLocation != null) {
            throw new BusinessException("地点名称已存在");
        }
        
        // 验证时间设置
        validateTimeSettings(meetingLocationDTO.getOpenTime(), meetingLocationDTO.getCloseTime());
        
        // 验证可用天数设置
        validateAvailableDays(meetingLocationDTO.getAvailableDays());

        // 验证日期范围设置
        validateDateRange(meetingLocationDTO.getAvailableStartDate(), meetingLocationDTO.getAvailableEndDate());

        MeetingLocation meetingLocation = new MeetingLocation();
        BeanUtils.copyProperties(meetingLocationDTO, meetingLocation);
        meetingLocation.setId(id);
        
        int result = meetingLocationMapper.update(meetingLocation);
        if (result <= 0) {
            throw new BusinessException("更新会议地点失败");
        }
    }
    
    @Override
    public MeetingLocation getMeetingLocationById(Integer id) {
        MeetingLocation meetingLocation = meetingLocationMapper.selectById(id);
        if (meetingLocation == null) {
            throw new BusinessException("会议地点不存在");
        }
        return meetingLocation;
    }
    
    @Override
    public PageResult<MeetingLocation> getMeetingLocationPage(int pageNum, int pageSize, String name, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("status", status);
        params.put("offset", (pageNum - 1) * pageSize);
        params.put("pageSize", pageSize);
        
        List<MeetingLocation> list = meetingLocationMapper.selectByPage(params);
        int total = meetingLocationMapper.countByPage(params);
        
        return new PageResult<MeetingLocation>(pageNum, pageSize, total, list);
    }
    
    @Override
    public List<MeetingLocation> getActiveLocations() {
        return meetingLocationMapper.selectActiveLocations();
    }

    @Override
    public List<MeetingLocation> getAllLocations() {
        return meetingLocationMapper.selectAllLocations();
    }

    @Override
    public LocationAvailabilityDTO getLocationAvailability(Integer locationId, LocalDateTime startDate, LocalDateTime endDate, Long excludeMeetingId) {
        try {
            // 查询地点基本信息
            MeetingLocation location = meetingLocationMapper.selectById(locationId);
            if (location == null) {
                throw new BusinessException("地点不存在");
            }

            if (!"ACTIVE".equals(location.getStatus())) {
                throw new BusinessException("地点已禁用");
            }

            // 查询已预订的时间段
            List<LocationAvailabilityDTO.BookedTimeSlot> bookedTimeSlots =
                meetingLocationMapper.selectBookedTimeSlots(locationId, startDate, endDate, excludeMeetingId);

            // 解析可用星期
            List<Integer> availableDaysList = Arrays.stream(location.getAvailableDays().split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

            // 构建返回结果
            LocationAvailabilityDTO result = new LocationAvailabilityDTO();
            result.setLocationId(location.getId());
            result.setLocationName(location.getName());
            result.setStatus(location.getStatus());
            result.setOpenTime(location.getOpenTime());
            result.setCloseTime(location.getCloseTime());
            result.setAvailableDays(location.getAvailableDays());
            result.setAvailableDaysList(availableDaysList);
            result.setAvailableStartDate(location.getAvailableStartDate());
            result.setAvailableEndDate(location.getAvailableEndDate());
            result.setBookedTimeSlots(bookedTimeSlots);

            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询地点可用性失败", e);
            throw new BusinessException("查询地点可用性失败");
        }
    }
    
    /**
     * 验证时间设置
     */
    private void validateTimeSettings(LocalTime openTime, LocalTime closeTime) {
        if (openTime == null || closeTime == null) {
            throw new BusinessException("开放时间和关闭时间不能为空");
        }
        
        if (!openTime.isBefore(closeTime)) {
            throw new BusinessException("开放时间必须早于关闭时间");
        }
    }
    
    /**
     * 验证可用天数设置
     */
    private void validateAvailableDays(String availableDays) {
        if (availableDays == null || availableDays.trim().isEmpty()) {
            throw new BusinessException("可用天数不能为空");
        }

        String[] days = availableDays.split(",");
        for (String day : days) {
            try {
                int dayNum = Integer.parseInt(day.trim());
                if (dayNum < 1 || dayNum > 7) {
                    throw new BusinessException("可用天数必须在1-7之间");
                }
            } catch (NumberFormatException e) {
                throw new BusinessException("可用天数格式不正确");
            }
        }
    }

    /**
     * 验证日期范围设置
     */
    private void validateDateRange(LocalDate startDate, LocalDate endDate) {
        // 如果两个日期都为null，表示无日期限制，这是允许的
        if (startDate == null && endDate == null) {
            return;
        }

        // 如果两个日期都不为null，验证开始日期不能晚于结束日期
        if (startDate != null && endDate != null) {
            if (startDate.isAfter(endDate)) {
                throw new BusinessException("开放开始日期不能晚于结束日期");
            }
        }

        // 如果只有一个日期不为null，也是允许的
        // startDate不为null，endDate为null：表示从startDate开始永久开放
        // startDate为null，endDate不为null：表示开放到endDate为止
    }
}
