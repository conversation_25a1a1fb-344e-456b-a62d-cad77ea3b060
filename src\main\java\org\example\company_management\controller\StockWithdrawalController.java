package org.example.company_management.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.StockWithdrawalDTO;
import org.example.company_management.dto.StockWithdrawalQueryDTO;
import org.example.company_management.dto.StockWithdrawalSubmitDTO;
import org.example.company_management.service.StockWithdrawalService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 股票提现控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/stock-withdrawal")
@RequiredArgsConstructor
public class StockWithdrawalController {
    
    private final StockWithdrawalService stockWithdrawalService;
    
    // ==================== 用户端API ====================
    
    /**
     * 提交股票提现申请
     */
    @PostMapping
    public Result<StockWithdrawalDTO> submitWithdrawal(
            @RequestBody @Validated(ValidationGroups.Add.class) StockWithdrawalSubmitDTO submitDTO) {
        try {
            // 从ThreadLocal获取当前登录员工ID
            Integer employeeId = ThreadLocalUtil.getCurrentId();
            submitDTO.setEmployeeId(employeeId);
            
            StockWithdrawalDTO result = stockWithdrawalService.submitWithdrawal(submitDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("提交股票提现申请失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询当前员工的股票提现申请记录
     */
    @GetMapping("/my")
    public Result<PageResult<StockWithdrawalDTO>> getMyStockWithdrawals(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status) {
        try {
            // 从ThreadLocal获取当前登录员工ID
            Integer employeeId = ThreadLocalUtil.getCurrentId();
            
            PageResult<StockWithdrawalDTO> result = stockWithdrawalService.getMyStockWithdrawals(employeeId, page, size, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询个人股票提现申请失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询股票提现申请详情
     */
    @GetMapping("/{id}")
    public Result<StockWithdrawalDTO> getStockWithdrawalById(@PathVariable Long id) {
        try {
            StockWithdrawalDTO result = stockWithdrawalService.getStockWithdrawalById(id);
            
            // 权限检查：只能查看自己的申请或管理员可以查看所有
            Integer currentEmployeeId = ThreadLocalUtil.getCurrentId();
            String currentRole = ThreadLocalUtil.getCurrentRole();
            
            if (!result.getEmployeeId().equals(currentEmployeeId) && !"admin".equals(currentRole)) {
                return Result.error("无权查看此申请");
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询股票提现申请详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 取消股票提现申请（仅限待审核状态）
     */
    @PutMapping("/{id}/cancel")
    public Result<String> cancelStockWithdrawal(@PathVariable Long id) {
        try {
            // 从ThreadLocal获取当前登录员工ID
            Integer employeeId = ThreadLocalUtil.getCurrentId();
            
            boolean success = stockWithdrawalService.cancelStockWithdrawal(id, employeeId);
            if (success) {
                return Result.success("取消成功");
            } else {
                return Result.error("取消失败");
            }
        } catch (Exception e) {
            log.error("取消股票提现申请失败", e);
            return Result.error("取消失败：" + e.getMessage());
        }
    }
    
    // ==================== 管理员端API ====================
    
    /**
     * 分页查询股票提现申请记录（管理员）
     */
    @GetMapping("/page")
    public Result<PageResult<StockWithdrawalDTO>> getStockWithdrawalPage(StockWithdrawalQueryDTO queryDTO) {
        try {
            // 权限检查：只有管理员可以查看所有申请
            String currentRole = ThreadLocalUtil.getCurrentRole();
            if (!"admin".equals(currentRole)) {
                return Result.error("无权访问");
            }
            
            PageResult<StockWithdrawalDTO> result = stockWithdrawalService.getStockWithdrawalPage(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询股票提现申请失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 审核股票提现申请（管理员）
     */
    @PutMapping("/{id}/audit")
    public Result<StockWithdrawalDTO> auditStockWithdrawal(
            @PathVariable Long id,
            @RequestBody Map<String, String> auditData) {
        try {
            // 权限检查：只有管理员可以审核
            String currentRole = ThreadLocalUtil.getCurrentRole();
            if (!"admin".equals(currentRole)) {
                return Result.error("无权审核");
            }
            
            String action = auditData.get("action");
            String rejectReason = auditData.get("rejectReason");
            Integer auditorId = ThreadLocalUtil.getCurrentId();
            
            StockWithdrawalDTO result = stockWithdrawalService.auditStockWithdrawal(id, action, rejectReason, auditorId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("审核股票提现申请失败", e);
            return Result.error("审核失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量审核股票提现申请（管理员）
     */
    @PutMapping("/batch-audit")
    public Result<Map<String, Object>> batchAuditStockWithdrawal(@RequestBody Map<String, Object> batchData) {
        try {
            // 权限检查：只有管理员可以审核
            String currentRole = ThreadLocalUtil.getCurrentRole();
            if (!"admin".equals(currentRole)) {
                return Result.error("无权审核");
            }
            
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) batchData.get("ids");
            String action = (String) batchData.get("action");
            String rejectReason = (String) batchData.get("rejectReason");
            Integer auditorId = ThreadLocalUtil.getCurrentId();
            
            Map<String, Object> result = stockWithdrawalService.batchAuditStockWithdrawal(ids, action, rejectReason, auditorId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量审核股票提现申请失败", e);
            return Result.error("批量审核失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询股票提现统计信息（管理员）
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStockWithdrawalStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            // 权限检查：只有管理员可以查看统计
            String currentRole = ThreadLocalUtil.getCurrentRole();
            if (!"admin".equals(currentRole)) {
                return Result.error("无权访问");
            }
            
            Map<String, Object> result = stockWithdrawalService.getStockWithdrawalStatistics(startDate, endDate);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询股票提现统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询员工的股票提现统计信息
     */
    @GetMapping("/employee/{employeeId}/statistics")
    public Result<Map<String, Object>> getEmployeeWithdrawalStatistics(@PathVariable Integer employeeId) {
        try {
            // 权限检查：只能查看自己的统计或管理员可以查看所有
            Integer currentEmployeeId = ThreadLocalUtil.getCurrentId();
            String currentRole = ThreadLocalUtil.getCurrentRole();
            
            if (!employeeId.equals(currentEmployeeId) && !"admin".equals(currentRole)) {
                return Result.error("无权查看此员工的统计信息");
            }
            
            Map<String, Object> result = stockWithdrawalService.getEmployeeWithdrawalStatistics(employeeId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询员工股票提现统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
