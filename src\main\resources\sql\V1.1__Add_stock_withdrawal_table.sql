-- 股票提现功能数据库迁移脚本
-- 版本: V1.1
-- 创建时间: 2025-07-22
-- 描述: 创建股票提现申请表，支持员工股票提现申请和管理员审核功能

-- 创建股票提现申请表
CREATE TABLE `stock_withdrawal`
(
    `id`            BIGINT                                 NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
    `employee_id`   INT                                    NOT NULL COMMENT '员工ID',
    `quantity`      INT                                    NOT NULL COMMENT '提现股票数量',
    `unit_price`    DECIMAL(12, 2)                         NOT NULL COMMENT '申请时股票单价',
    `total_amount`  DECIMAL(12, 2)                         NOT NULL COMMENT '提现总金额',
    `status`        ENUM ('PENDING','APPROVED','REJECTED','CANCELLED') NOT NULL DEFAULT 'PENDING' COMMENT '申请状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，CANCELLED-已取消',
    `apply_time`    DATETIME                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    `audit_time`    DATETIME COMMENT '审核时间',
    `auditor_id`    INT COMMENT '审核人ID',
    `reject_reason` VARCHAR(500) COMMENT '拒绝理由',
    `remark`        VARCHAR(255) COMMENT '备注',
    `create_time`   DATETIME                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   DATETIME                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),

    -- 外键约束
    CONSTRAINT `fk_stock_withdrawal_employee`
        FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_stock_withdrawal_auditor`
        FOREIGN KEY (`auditor_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE SET NULL ON UPDATE CASCADE,

    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_apply_time` (`apply_time`),
    INDEX `idx_auditor_id` (`auditor_id`),
    INDEX `idx_employee_status` (`employee_id`, `status`),
    INDEX `idx_status_apply_time` (`status`, `apply_time`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '股票提现申请表';

-- 添加数据验证约束
ALTER TABLE `stock_withdrawal`
    ADD CONSTRAINT `chk_quantity_positive` CHECK (`quantity` > 0),
    ADD CONSTRAINT `chk_unit_price_positive` CHECK (`unit_price` > 0),
    ADD CONSTRAINT `chk_total_amount_positive` CHECK (`total_amount` > 0);

-- 插入测试数据（可选，用于开发测试）
-- INSERT INTO `stock_withdrawal` (`employee_id`, `quantity`, `unit_price`, `total_amount`, `reason`, `status`) 
-- VALUES 
-- (1, 100, 1000.00, 100000.00, '个人资金需求', 'PENDING'),
-- (2, 50, 1000.00, 50000.00, '投资其他项目', 'PENDING');

-- 创建员工股票汇总表（汇总+明细混合模式）
CREATE TABLE `employee_stock_summary`
(
    `employee_id`          INT      NOT NULL PRIMARY KEY COMMENT '员工ID',
    `total_quantity`       INT      NOT NULL DEFAULT 0 COMMENT '总股票数量',
    `unlocked_quantity`    INT      NOT NULL DEFAULT 0 COMMENT '已解禁数量',
    `withdrawn_quantity`   INT      NOT NULL DEFAULT 0 COMMENT '已提现数量',
    `available_quantity`   INT GENERATED ALWAYS AS (unlocked_quantity - withdrawn_quantity) STORED COMMENT '可提现数量（计算列）',
    `last_calculated_time` DATETIME          DEFAULT CURRENT_TIMESTAMP COMMENT '最后计算时间',
    `create_time`          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE ON UPDATE CASCADE,

    -- 索引优化
    INDEX `idx_total_quantity` (`total_quantity`),
    INDEX `idx_available_quantity` (`available_quantity`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '员工股票汇总表';

-- 创建视图：员工股票提现统计
CREATE VIEW `v_employee_stock_withdrawal_stats` AS
SELECT e.employee_id,
       e.name                                                                              as employee_name,
       COALESCE(SUM(CASE WHEN sw.status = 'PENDING' THEN sw.quantity ELSE 0 END), 0)       as pending_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'PENDING' THEN sw.total_amount ELSE 0 END), 0)   as pending_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'APPROVED' THEN sw.quantity ELSE 0 END), 0)      as approved_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'APPROVED' THEN sw.total_amount ELSE 0 END), 0)  as approved_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'REJECTED' THEN sw.quantity ELSE 0 END), 0)      as rejected_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'REJECTED' THEN sw.total_amount ELSE 0 END), 0)  as rejected_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'CANCELLED' THEN sw.quantity ELSE 0 END), 0)     as cancelled_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'CANCELLED' THEN sw.total_amount ELSE 0 END), 0) as cancelled_amount,
       COUNT(sw.id)                                                                        as total_applications
FROM employee e
         LEFT JOIN stock_withdrawal sw ON e.employee_id = sw.employee_id
GROUP BY e.employee_id, e.name;

-- 创建触发器：员工股票新增时自动更新汇总表
DELIMITER $$
CREATE TRIGGER `tr_employee_stock_insert_summary`
    AFTER INSERT
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE unlocked_qty INT DEFAULT 0;

    -- 计算是否已解禁
    IF NEW.unlock_time IS NULL OR NEW.unlock_time <= NOW() THEN
        SET unlocked_qty = NEW.quantity;
    END IF;

    -- 更新汇总表
    INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, last_calculated_time)
    VALUES (NEW.employee_id, NEW.quantity, unlocked_qty, NOW())
    ON DUPLICATE KEY UPDATE total_quantity       = total_quantity + NEW.quantity,
                            unlocked_quantity    = unlocked_quantity + unlocked_qty,
                            last_calculated_time = NOW();
END$$

-- 创建触发器：员工股票更新时自动更新汇总表
CREATE TRIGGER `tr_employee_stock_update_summary`
    AFTER UPDATE
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE old_unlocked_qty INT DEFAULT 0;
    DECLARE new_unlocked_qty INT DEFAULT 0;

    -- 计算旧的解禁数量
    IF OLD.unlock_time IS NULL OR OLD.unlock_time <= NOW() THEN
        SET old_unlocked_qty = OLD.quantity;
    END IF;

    -- 计算新的解禁数量
    IF NEW.unlock_time IS NULL OR NEW.unlock_time <= NOW() THEN
        SET new_unlocked_qty = NEW.quantity;
    END IF;

    -- 更新汇总表
    UPDATE employee_stock_summary
    SET total_quantity       = total_quantity - OLD.quantity + NEW.quantity,
        unlocked_quantity    = unlocked_quantity - old_unlocked_qty + new_unlocked_qty,
        last_calculated_time = NOW()
    WHERE employee_id = NEW.employee_id;
END$$

-- 创建触发器：员工股票删除时自动更新汇总表
CREATE TRIGGER `tr_employee_stock_delete_summary`
    AFTER DELETE
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE unlocked_qty INT DEFAULT 0;

    -- 计算被删除的解禁数量
    IF OLD.unlock_time IS NULL OR OLD.unlock_time <= NOW() THEN
        SET unlocked_qty = OLD.quantity;
    END IF;

    -- 更新汇总表
    UPDATE employee_stock_summary
    SET total_quantity       = total_quantity - OLD.quantity,
        unlocked_quantity    = unlocked_quantity - unlocked_qty,
        last_calculated_time = NOW()
    WHERE employee_id = OLD.employee_id;

    -- 如果总数量为0，删除汇总记录
    DELETE
    FROM employee_stock_summary
    WHERE employee_id = OLD.employee_id
      AND total_quantity <= 0;
END$$

-- 创建触发器：股票提现审核通过时更新汇总表
CREATE TRIGGER `tr_stock_withdrawal_approve_summary`
    AFTER UPDATE
    ON `stock_withdrawal`
    FOR EACH ROW
BEGIN
    -- 当状态从非APPROVED变为APPROVED时，增加已提现数量
    IF NEW.status = 'APPROVED' AND OLD.status != 'APPROVED' THEN
        UPDATE employee_stock_summary
        SET withdrawn_quantity   = withdrawn_quantity + NEW.quantity,
            last_calculated_time = NOW()
        WHERE employee_id = NEW.employee_id;
    END IF;

    -- 当状态从APPROVED变为非APPROVED时，减少已提现数量
    -- 注意：CANCELLED状态不应该影响已提现数量，因为CANCELLED是从PENDING状态转换而来
    IF OLD.status = 'APPROVED' AND NEW.status NOT IN ('APPROVED') THEN
        UPDATE employee_stock_summary
        SET withdrawn_quantity   = withdrawn_quantity - NEW.quantity,
            last_calculated_time = NOW()
        WHERE employee_id = NEW.employee_id;
    END IF;
END$$
DELIMITER ;

-- 初始化汇总表数据（基于现有employee_stock数据）
INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, withdrawn_quantity,
                                    last_calculated_time)
SELECT es.employee_id,
       COALESCE(SUM(es.quantity), 0)             as total_quantity,
       COALESCE(SUM(CASE
                        WHEN es.unlock_time IS NULL OR es.unlock_time <= NOW()
                            THEN es.quantity
                        ELSE 0 END), 0)          as unlocked_quantity,
       COALESCE(withdrawn.withdrawn_quantity, 0) as withdrawn_quantity,
       NOW()                                     as last_calculated_time
FROM employee_stock es
         LEFT JOIN (SELECT employee_id, SUM(quantity) as withdrawn_quantity
                    FROM stock_withdrawal
                    WHERE status = 'APPROVED'
                    GROUP BY employee_id) withdrawn ON es.employee_id = withdrawn.employee_id
GROUP BY es.employee_id, withdrawn.withdrawn_quantity
ON DUPLICATE KEY UPDATE total_quantity       = VALUES(total_quantity),
                        unlocked_quantity    = VALUES(unlocked_quantity),
                        withdrawn_quantity   = VALUES(withdrawn_quantity),
                        last_calculated_time = VALUES(last_calculated_time);

-- 添加额外索引优化（支持CANCELLED状态查询）
-- 为新的CANCELLED状态优化查询性能
DELIMITER $$
CREATE PROCEDURE CreateIndexIfNotExists()
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查复合索引是否存在
    SELECT COUNT(*)
    INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
      AND table_name = 'stock_withdrawal'
      AND index_name = 'idx_employee_status_time';

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        CREATE INDEX `idx_employee_status_time` ON `stock_withdrawal` (`employee_id`, `status`, `apply_time`);
    END IF;
END$$
DELIMITER ;

-- 调用存储过程创建索引
CALL CreateIndexIfNotExists();

-- 删除临时存储过程
DROP PROCEDURE CreateIndexIfNotExists;

-- 数据完整性检查
-- 确保所有现有数据的状态都是有效的
UPDATE `stock_withdrawal`
SET `status` = 'PENDING'
WHERE `status` NOT IN ('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED');

-- 添加注释说明
ALTER TABLE `stock_withdrawal`
    COMMENT = '股票提现申请表：记录员工股票提现申请和审核信息，支持完整的提现流程管理，包括用户主动取消功能';
ALTER TABLE `employee_stock_summary`
    COMMENT = '员工股票汇总表：采用汇总+明细混合模式，提供高性能的股票数量查询';
