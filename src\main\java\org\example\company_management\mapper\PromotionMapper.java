package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.Promotion;

import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 推广管理数据访问层接口}}
 * {{CHENGQI: 任务ID: P4-LD-004}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 描述: 推广管理Mapper接口，定义数据访问方法}}
 */
@Mapper
public interface PromotionMapper {

    /**
     * 插入推广记录
     *
     * @param promotion 推广实体
     * @return 影响行数
     */
    int insert(Promotion promotion);

    /**
     * 根据ID更新推广记录
     *
     * @param promotion 推广实体
     * @return 影响行数
     */
    int updateById(Promotion promotion);

    /**
     * 根据ID删除推广记录
     *
     * @param id 推广ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询推广记录（包含关联信息）
     *
     * @param id 推广ID
     * @return 推广实体
     */
    Promotion selectById(@Param("id") Long id);

    /**
     * 分页查询推广列表（支持多条件筛选）
     *
     * @param authorId 发布人ID（可选）
     * @param departmentIds 部门ID列表（可选）
     * @param status 状态（可选）
     * @param titleKeyword 标题关键词（可选）
     * @param createTimeStart 创建时间开始（可选）
     * @param createTimeEnd 创建时间结束（可选）
     * @return 推广列表
     */
    List<Promotion> selectPage(@Param("authorId") Integer authorId,
                              @Param("departmentIds") List<Integer> departmentIds,
                              @Param("status") String status,
                              @Param("titleKeyword") String titleKeyword,
                              @Param("createTimeStart") LocalDateTime createTimeStart,
                              @Param("createTimeEnd") LocalDateTime createTimeEnd);





    /**
     * 修改推广状态
     *
     * @param id 推广ID
     * @param status 新状态
     * @param operatorId 操作人ID
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updatePromotionStatus(@Param("id") Long id, @Param("status") String status,
                             @Param("operatorId") Integer operatorId, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 审核推广（支持设置拒绝理由）
     *
     * @param id 推广ID
     * @param status 审核状态（已通过/已拒绝）
     * @param auditorId 审核人ID
     * @param auditTime 审核时间
     * @param rejectReason 拒绝理由（可选）
     * @return 影响行数
     */
    int auditPromotionStatus(@Param("id") Long id, @Param("status") String status,
                            @Param("auditorId") Integer auditorId, @Param("auditTime") LocalDateTime auditTime,
                            @Param("rejectReason") String rejectReason);



    /**
     * 查询待审核的推广列表（按条件筛选）
     *
     * @param departmentIds 部门ID列表
     * @param titleKeyword 标题关键词
     * @param status 状态筛选
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 推广列表
     */
    List<Promotion> selectPendingAuditByConditions(
        @Param("departmentIds") List<Integer> departmentIds,
        @Param("titleKeyword") String titleKeyword,
        @Param("status") String status,
        @Param("createTimeStart") LocalDateTime createTimeStart,
        @Param("createTimeEnd") LocalDateTime createTimeEnd,
        @Param("offset") Integer offset,
        @Param("limit") Integer limit
    );

    /**
     * 统计待审核推广总数（按条件筛选）
     *
     * @param departmentIds 部门ID列表
     * @param titleKeyword 标题关键词
     * @param status 状态筛选
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @return 总记录数
     */
    int countPendingAuditByConditions(
        @Param("departmentIds") List<Integer> departmentIds,
        @Param("titleKeyword") String titleKeyword,
        @Param("status") String status,
        @Param("createTimeStart") LocalDateTime createTimeStart,
        @Param("createTimeEnd") LocalDateTime createTimeEnd
    );

    /**
     * 查询已通过的推广列表（用于公开展示）
     *
     * @param departmentIds 部门ID列表（可选，用于部门筛选）
     * @param titleKeyword 标题关键词（可选）
     * @param createTimeStart 创建时间开始（可选）
     * @param createTimeEnd 创建时间结束（可选）
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 推广列表
     */
    List<Promotion> selectApprovedPromotions(@Param("departmentIds") List<Integer> departmentIds,
                                           @Param("titleKeyword") String titleKeyword,
                                           @Param("createTimeStart") LocalDateTime createTimeStart,
                                           @Param("createTimeEnd") LocalDateTime createTimeEnd,
                                           @Param("offset") Integer offset,
                                           @Param("limit") Integer limit);

    /**
     * 统计已通过推广总数（用于公开展示）
     *
     * @param departmentIds 部门ID列表（可选，用于部门筛选）
     * @param titleKeyword 标题关键词（可选）
     * @param createTimeStart 创建时间开始（可选）
     * @param createTimeEnd 创建时间结束（可选）
     * @return 总记录数
     */
    int countApprovedPromotions(@Param("departmentIds") List<Integer> departmentIds,
                              @Param("titleKeyword") String titleKeyword,
                              @Param("createTimeStart") LocalDateTime createTimeStart,
                              @Param("createTimeEnd") LocalDateTime createTimeEnd);

    /**
     * 统计推广数量（按状态分组）
     *
     * @param authorId 作者ID（可选）
     * @param departmentIds 部门ID列表（可选）
     * @return 统计结果Map，key为状态，value为数量
     */
    List<java.util.Map<String, Object>> countByStatus(@Param("authorId") Integer authorId,
                                                      @Param("departmentIds") List<Integer> departmentIds);


}

// {{CHENGQI: 推广管理Mapper接口创建完成}}
