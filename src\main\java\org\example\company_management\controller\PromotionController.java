package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.PromotionDTO;
import org.example.company_management.service.PromotionService;
import org.example.company_management.utils.ImageUploadUtil;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * {{CHENGQI: 推广管理控制器}}
 * {{CHENGQI: 任务ID: P4-LD-008}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 描述: 推广管理REST API控制器，提供推广CRUD、审核、图片上传等功能}}
 */
@Slf4j
@RestController
@RequestMapping("/promotion")
public class PromotionController {

    @Autowired
    private PromotionService promotionService;

    /**
     * 创建推广
     * POST /promotion
     */
    @PostMapping
    public Result<PromotionDTO> createPromotion(@Validated(ValidationGroups.Add.class) @RequestBody PromotionDTO promotionDTO) {
        log.info("创建推广请求: {}", promotionDTO.getTitle());
        return promotionService.createPromotion(promotionDTO);
    }

    /**
     * 更新推广
     * PUT /promotion/{id}
     */
    @PutMapping("/{id}")
    public Result<PromotionDTO> updatePromotion(@PathVariable Long id, 
                                               @Validated(ValidationGroups.Update.class) @RequestBody PromotionDTO promotionDTO) {
        log.info("更新推广请求, ID: {}, 标题: {}", id, promotionDTO.getTitle());
        return promotionService.updatePromotion(id, promotionDTO);
    }

    /**
     * 删除推广
     * DELETE /promotion/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Void> deletePromotion(@PathVariable Long id) {
        log.info("删除推广请求, ID: {}", id);
        return promotionService.deletePromotion(id);
    }

    /**
     * 根据ID获取推广详情
     * GET /promotion/{id}
     */
    @GetMapping("/{id}")
    public Result<PromotionDTO> getPromotionById(@PathVariable Long id) {
        log.info("查询推广详情, ID: {}", id);
        return promotionService.getPromotionById(id);
    }



    /**
     * 获取我的推广列表
     * GET /promotion/my
     * 支持按状态、标题关键词、创建时间范围进行筛选
     */
    @GetMapping("/my")
    public Result<PageResult<PromotionDTO>> getMyPromotions(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String titleKeyword,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createTimeStart,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createTimeEnd) {

        log.info("查询我的推广列表, 页码: {}, 每页大小: {}, 状态: {}, 标题关键词: {}",
                pageNum, pageSize, status, titleKeyword);
        return promotionService.getMyPromotions(pageNum, pageSize, status, titleKeyword, createTimeStart, createTimeEnd);
    }

    /**
     * 获取已通过的推广列表（公开展示）
     * POST /promotion/approved
     * 支持按部门ID、标题关键词、创建时间范围进行筛选
     */
    @PostMapping("/approved")
    public Result<PageResult<PromotionDTO>> getApprovedPromotions(@RequestBody Map<String, Object> requestBody) {

        // 从请求体中提取参数
        Integer pageNum = (Integer) requestBody.getOrDefault("pageNum", 1);
        Integer pageSize = (Integer) requestBody.getOrDefault("pageSize", 10);
        @SuppressWarnings("unchecked")
        List<Integer> departmentIds = (List<Integer>) requestBody.get("departmentIds");
        String titleKeyword = (String) requestBody.get("titleKeyword");
        String createTimeStart = (String) requestBody.get("createTimeStart");
        String createTimeEnd = (String) requestBody.get("createTimeEnd");

        log.info("查询已通过推广列表, 页码: {}, 每页大小: {}, 部门IDs: {}, 标题关键词: {}",
                pageNum, pageSize, departmentIds, titleKeyword);

        // 构建查询条件
        PromotionDTO queryDTO = new PromotionDTO();
        queryDTO.setDepartmentIds(departmentIds);
        queryDTO.setTitleKeyword(titleKeyword);

        // 处理时间参数
        if (createTimeStart != null && !createTimeStart.trim().isEmpty()) {
            try {
                queryDTO.setCreateTimeStart(LocalDateTime.parse(createTimeStart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } catch (Exception e) {
                log.warn("解析创建开始时间失败: {}", createTimeStart, e);
            }
        }

        if (createTimeEnd != null && !createTimeEnd.trim().isEmpty()) {
            try {
                queryDTO.setCreateTimeEnd(LocalDateTime.parse(createTimeEnd, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } catch (Exception e) {
                log.warn("解析创建结束时间失败: {}", createTimeEnd, e);
            }
        }

        return promotionService.getApprovedPromotions(pageNum, pageSize, queryDTO);
    }

    /**
     * 获取待审核推广列表（部门主管使用）
     * POST /promotion/pending-audit
     * 支持按部门ID、标题关键词、创建时间范围、状态进行筛选
     */
    @PostMapping("/pending-audit")
    public Result<PageResult<PromotionDTO>> getPendingAuditPromotions(@RequestBody Map<String, Object> requestBody) {

        // 从请求体中提取参数
        Integer pageNum = (Integer) requestBody.getOrDefault("pageNum", 1);
        Integer pageSize = (Integer) requestBody.getOrDefault("pageSize", 10);
        @SuppressWarnings("unchecked")
        List<Integer> departmentIds = (List<Integer>) requestBody.get("departmentIds");
        String titleKeyword = (String) requestBody.get("titleKeyword");
        String status = (String) requestBody.get("status");
        String createTimeStart = (String) requestBody.get("createTimeStart");
        String createTimeEnd = (String) requestBody.get("createTimeEnd");

        log.info("查询待审核推广列表, 页码: {}, 每页大小: {}, 部门IDs: {}, 标题关键词: {}, 状态: {}",
                pageNum, pageSize, departmentIds, titleKeyword, status);

        // 构建查询条件
        PromotionDTO queryDTO = new PromotionDTO();
        queryDTO.setDepartmentIds(departmentIds);
        queryDTO.setTitleKeyword(titleKeyword);
        queryDTO.setStatusFilter(status);

        // 处理时间参数
        if (createTimeStart != null && !createTimeStart.trim().isEmpty()) {
            try {
                queryDTO.setCreateTimeStart(LocalDateTime.parse(createTimeStart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } catch (Exception e) {
                log.warn("解析创建开始时间失败: {}", createTimeStart, e);
            }
        }

        if (createTimeEnd != null && !createTimeEnd.trim().isEmpty()) {
            try {
                queryDTO.setCreateTimeEnd(LocalDateTime.parse(createTimeEnd, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } catch (Exception e) {
                log.warn("解析创建结束时间失败: {}", createTimeEnd, e);
            }
        }

        return promotionService.getPendingAuditPromotions(pageNum, pageSize, queryDTO);
    }

    // {{FUTURE_EXTENSION: 提交审核接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于工作流优化，当前通过auditPromotion实现审核}}
    /**
     * 提交审核
     * PUT /promotion/{id}/submit
     */
    @PutMapping("/{id}/submit")
    public Result<Void> submitForAudit(@PathVariable Long id) {
        log.info("提交推广审核, ID: {}", id);
        return promotionService.submitForAudit(id);
    }

    // {{FUTURE_EXTENSION: 推广状态修改接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于批量状态管理或管理员直接状态修改}}
    /**
     * 修改推广状态
     * PUT /promotion/{id}/status
     */
    @PutMapping("/{id}/status")
    public Result<Void> updatePromotionStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
        String status = request.get("status");
        log.info("修改推广状态, ID: {}, 新状态: {}", id, status);
        return promotionService.updatePromotionStatus(id, status);
    }

    /**
     * 审核推广
     * PUT /promotion/{id}/audit
     */
    @PutMapping("/{id}/audit")
    public Result<Void> auditPromotion(@PathVariable Long id, 
                                      @Validated(ValidationGroups.PromotionAudit.class) @RequestBody PromotionDTO promotionDTO) {
        log.info("审核推广, ID: {}, 审核状态: {}", id, promotionDTO.getAuditStatus());
        return promotionService.auditPromotion(id, promotionDTO);
    }

    /**
     * 上传单个图片
     * POST /promotion/upload-image
     */
    @PostMapping("/upload-image")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        log.info("上传推广图片, 文件名: {}", file.getOriginalFilename());
        return promotionService.uploadImage(file);
    }

    // {{FUTURE_EXTENSION: 批量图片上传接口 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前使用单图片上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用 uploadImage 进行单图片上传

    // {{FUTURE_EXTENSION: 图片删除接口 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前图片管理通过富文本编辑器处理，独立删除功能未使用}}
    // 已删除 - 图片删除通过富文本编辑器内部处理

    // {{FUTURE_EXTENSION: 推广统计接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于管理后台的数据统计和分析功能}}
    /**
     * 获取推广统计信息
     * GET /promotion/statistics
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getPromotionStatistics(@RequestParam(required = false) Integer authorId) {
        log.info("查询推广统计信息, 作者ID: {}", authorId);
        return promotionService.getPromotionStatistics(authorId);
    }

    // {{FUTURE_EXTENSION: 权限检查接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于复杂权限控制场景，当前通过前端计算属性实现}}
    /**
     * 检查权限
     * GET /promotion/{id}/permission
     */
    @GetMapping("/{id}/permission")
    public Result<Map<String, Boolean>> checkPermission(@PathVariable Long id) {
        log.info("检查推广权限, ID: {}", id);

        Map<String, Boolean> permissions = Map.of(
            "canView", promotionService.hasPermission(id, "view"),
            "canEdit", promotionService.hasPermission(id, "edit"),
            "canDelete", promotionService.hasPermission(id, "delete"),
            "canAudit", promotionService.hasPermission(id, "audit")
        );

        return Result.success(permissions);
    }


}

// {{CHENGQI: 推广管理控制器创建完成}}
