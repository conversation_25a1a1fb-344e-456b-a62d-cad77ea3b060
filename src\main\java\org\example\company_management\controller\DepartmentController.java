package org.example.company_management.controller;

import org.example.company_management.entity.Department;
import org.example.company_management.entity.DepartmentLeader;
import org.example.company_management.entity.Employee;
import org.example.company_management.service.DepartmentService;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 部门管理控制器
 * <p>
 * 提供部门的添加、修改、删除和查询功能。
 * 部门是公司的基本组织单位，每个员工都归属于某个部门。
 * 部门信息包含部门ID、部门名称和部门负责人。
 * </p>
 */
@RestController
@RequestMapping("/department")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private EmployeeService employeeService;

    /**
     * 查询所有部门
     * <p>
     * 获取系统中所有部门的列表。
     * 用于部门选择下拉框和部门管理页面。
     * 返回的每个部门都包含完整的部门信息。
     * </p>
     *
     * @return 部门列表
     */
    @GetMapping("/list")
    public Result<List<Department>> list() {
        try {
            List<Department> departments = departmentService.list();
            return Result.success(departments);
        } catch (Exception e) {
            return Result.error("获取部门列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询部门
     * <p>
     * 分页获取系统中的部门列表。
     * 用于部门管理页面。
     * </p>
     *
     * @param pageNum          页码，默认为1
     * @param pageSize         每页大小，默认为10
     * @param departmentName   部门名称(可选)
     * @param leaderId         部门负责人ID(可选)
     * @param leaderName       部门负责人姓名(可选，用于模糊匹配)
     * @param parentDepartmentId 父部门ID(可选)
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult<Department>> page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String departmentName,
            @RequestParam(required = false) Integer leaderId,
            @RequestParam(required = false) String leaderName,
            @RequestParam(required = false) Integer parentDepartmentId) {
        try {
            PageResult<Department> pageResult = departmentService.pageList(pageNum, pageSize, departmentName, leaderId, leaderName, parentDepartmentId);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("获取部门分页数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询部门
     * <p>
     * 获取指定ID的部门详细信息。
     * 用于部门编辑前的数据获取。
     * </p>
     *
     * @param id 部门ID
     * @param includeLeaders 是否包含负责人信息
     * @return 部门信息
     */
    @GetMapping("/{id}")
    public Result<Department> getById(@PathVariable("id") Integer id,
                                     @RequestParam(value = "includeLeaders", defaultValue = "true") Boolean includeLeaders) {
        try {
            Department department = departmentService.getByIdWithLeaders(id, includeLeaders);
            if (department == null) {
                return Result.error("部门不存在");
            }
            return Result.success(department);
        } catch (Exception e) {
            return Result.error("获取部门信息失败: " + e.getMessage());
        }
    }

    /**
     * 新增部门
     * <p>
     * 添加一个新的部门到系统中。
     * 部门名称不能为空，且不能与现有部门重名。
     * </p>
     *
     * @param department 部门信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody Department department) {
        try {
            departmentService.add(department);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("添加部门失败: " + e.getMessage());
        }
    }

    /**
     * 修改部门
     * <p>
     * 更新现有部门的信息。
     * 部门ID必须存在，且部门名称不能为空。
     * 如果修改部门名称，新名称不能与其他部门重名。
     * </p>
     *
     * @param department 部门信息
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@RequestBody Department department) {
        try {
            departmentService.update(department);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新部门失败: " + e.getMessage());
        }
    }

    /**
     * 删除部门
     * <p>
     * 从系统中删除指定ID的部门。
     * 删除前会检查部门下是否有员工或职位，如果有则会提示先处理这些员工或职位。
     * </p>
     *
     * @param id 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable("id") Integer id) {
        try {
            // 检查部门是否存在
            Department department = departmentService.getById(id);
            if (department == null) {
                return Result.error("部门不存在");
            }

            // 执行删除操作
            departmentService.delete(id);

            return Result.success("部门已成功删除");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("删除部门失败: " + e.getMessage());
        }
    }

    /**
     * 更新部门状态
     * <p>
     * 更新部门的状态（启用/禁用）。
     * </p>
     *
     * @param id     部门ID
     * @param status 状态
     * @return 操作结果
     */
    @PutMapping("/status")
    public Result<Void> updateStatus(@RequestParam Integer id, @RequestParam String status) {
        try {
            // 检查状态值是否有效
            if (!"Active".equals(status) && !"Inactive".equals(status)) {
                return Result.error("无效的状态值，只能是Active或Inactive");
            }

            Department department = departmentService.getById(id);
            if (department == null) {
                return Result.error("部门不存在");
            }

            department.setStatus(status);
            departmentService.update(department);
            return Result.success();
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新部门状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据部门名称搜索部门
     * <p>
     * 提供模糊搜索功能，可以根据部门名称查询部门。
     * </p>
     *
     * @param keyword 搜索关键字
     * @return 搜索结果
     */
    @GetMapping("/search")
    public Result<List<Department>> search(@RequestParam String keyword) {
        try {
            List<Department> departments = departmentService.search(keyword);
            return Result.success(departments);
        } catch (Exception e) {
            return Result.error("搜索部门失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户的部门信息
     * <p>
     * 返回当前登录用户所属的部门和担任负责人的部门列表。
     * 用于员工访问自己所在部门和负责的部门信息。
     * </p>
     *
     * @return 部门信息，包含myDepartment(所属部门)和leadingDepartments(负责的部门列表)
     */
    @GetMapping("/current")
    public Result<Map<String, Object>> getCurrentDepartment() {
        try {
            // 从ThreadLocal中获取当前用户信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 获取用户信息
            Employee employee = employeeService.getById(employeeId);
            if (employee == null) {
                return Result.error("无法获取当前用户信息");
            }

            // 准备返回结果
            Map<String, Object> result = new HashMap<>();
            
            // 获取用户所属部门信息
            Department myDepartment = null;
            if (employee.getDepartmentId() != null) {
                myDepartment = departmentService.getById(employee.getDepartmentId());
            }
            result.put("myDepartment", myDepartment);
            
            // 获取用户担任负责人的部门列表
            List<Department> leadingDepartments = departmentService.getDepartmentsByLeaderId(employeeId);
            result.put("leadingDepartments", leadingDepartments);

            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取当前部门信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门的子部门列表
     * <p>
     * 根据部门ID获取其直接子部门列表。
     * 用于构建部门层级结构和部门树形展示。
     * </p>
     *
     * @param departmentId 部门ID
     * @return 子部门列表
     */
    @GetMapping("/{departmentId}/sub")
    public Result<List<Department>> getSubDepartments(@PathVariable("departmentId") Integer departmentId) {
        try {
            // 检查部门是否存在
            Department department = departmentService.getById(departmentId);
            if (department == null) {
                return Result.error("部门不存在");
            }

            // 获取子部门列表
            List<Department> subDepartments = departmentService.getSubDepartments(departmentId);
            return Result.success(subDepartments);
        } catch (Exception e) {
            return Result.error("获取子部门列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户负责的部门树结构
     * <p>
     * 一次性获取当前登录用户负责的所有部门及其子部门的树形结构。
     * 只返回必要字段：departmentId、departmentName、children，减少数据传输量。
     * 用于构建前端级联选择器的树形结构。
     * </p>
     *
     * @return 部门树结构
     */
    @GetMapping("/responsible-tree")
    public Result<List<Map<String, Object>>> getResponsibleDepartmentTree() {
        try {
            // 从ThreadLocal中获取当前用户信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 获取用户负责的部门列表
            List<Department> leadingDepartments = departmentService.getDepartmentsByLeaderId(employeeId);
            if (leadingDepartments == null || leadingDepartments.isEmpty()) {
                return Result.success(new ArrayList<>());
            }

            // 为每个负责的部门构建树形结构
            List<Map<String, Object>> responsibleTree = new ArrayList<>();
            for (Department dept : leadingDepartments) {
                Map<String, Object> deptNode = new HashMap<>();
                deptNode.put("departmentId", dept.getDepartmentId());
                deptNode.put("departmentName", dept.getDepartmentName());
                
                // 递归获取子部门
                List<Map<String, Object>> children = buildDepartmentTree(dept.getDepartmentId());
                deptNode.put("children", children);
                
                responsibleTree.add(deptNode);
            }

            return Result.success(responsibleTree);
        } catch (Exception e) {
            return Result.error("获取负责部门树结构失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建部门的子部门树结构
     * 
     * @param departmentId 部门ID
     * @return 子部门树结构
     */
    private List<Map<String, Object>> buildDepartmentTree(Integer departmentId) {
        // 获取直接子部门
        List<Department> subDepartments = departmentService.getSubDepartments(departmentId);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Department subDept : subDepartments) {
            Map<String, Object> node = new HashMap<>();
            node.put("departmentId", subDept.getDepartmentId());
            node.put("departmentName", subDept.getDepartmentName());
            
            // 递归获取子部门
            List<Map<String, Object>> children = buildDepartmentTree(subDept.getDepartmentId());
            node.put("children", children);
            
            result.add(node);
        }

        return result;
    }
}