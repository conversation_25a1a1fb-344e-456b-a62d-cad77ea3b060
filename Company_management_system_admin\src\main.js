import { createApp } from 'vue'
import { createPinia } from 'pinia'
// {{CHENGQI: Element Plus 按需引入优化}}
// {{CHENGQI: 优化时间: 2025-07-09 11:45:00 +08:00}}
// {{CHENGQI: 中文语言修复: 2025-07-09 12:30:00 +08:00}}
import 'element-plus/dist/index.css'
import router from './router'
import App from './App.vue'
import './style.css'
// 导入全局组件
import GlobalComponents from './components/index.js'

const app = createApp(App)

app.use(createPinia())
app.use(router)
// 注册全局组件
GlobalComponents(app)

app.mount('#app')
