package org.example.company_management.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.BatchExtendExpenseRequestDTO;
import org.example.company_management.dto.EmployeeExpenseDTO;
import org.example.company_management.dto.ExtendedExpenseItemDTO;
import org.example.company_management.entity.EmployeeExpense;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.EmployeeExpenseMapper;
import org.example.company_management.service.EmployeeExpenseService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.example.company_management.dto.ExtendBatchResultDTO;

@Service
@Slf4j
public class EmployeeExpenseServiceImpl implements EmployeeExpenseService {

    @Autowired
    private EmployeeExpenseMapper employeeExpenseMapper;

    @Override
    public PageResult getPage(Map<String, Object> params) {
        // 从Map中安全地获取分页参数，提供默认值
        Integer pageNum = params.containsKey("pageNum") && params.get("pageNum") != null ?
                Integer.parseInt(params.get("pageNum").toString()) : 1;
        Integer pageSize = params.containsKey("pageSize") && params.get("pageSize") != null ?
                Integer.parseInt(params.get("pageSize").toString()) : 10;

        PageHelper.startPage(pageNum, pageSize);
        Page<EmployeeExpense> page = employeeExpenseMapper.selectPage(params);
        return new PageResult(pageNum, pageSize, page.getTotal(), page.getResult());
    }

    @Override
    public EmployeeExpenseDTO getById(Long id) {
        EmployeeExpense employeeExpense = employeeExpenseMapper.selectById(id);
        if (employeeExpense == null) {
            throw new BusinessException("员工费用记录不存在");
        }
        EmployeeExpenseDTO employeeExpenseDTO = new EmployeeExpenseDTO();
        BeanUtils.copyProperties(employeeExpense, employeeExpenseDTO);
        if (employeeExpense.getEmployeeName() != null) {
            employeeExpenseDTO.setEmployeeName(employeeExpense.getEmployeeName());
        }
        if (employeeExpense.getDepartmentId() != null) {
            employeeExpenseDTO.setDepartmentId(employeeExpense.getDepartmentId());
        }
        if (employeeExpense.getDepartmentName() != null) {
            employeeExpenseDTO.setDepartmentName(employeeExpense.getDepartmentName());
        }
        return employeeExpenseDTO;
    }

    // Helper method for parsing month string to LocalDate
    private LocalDate parseMonthStrToLocalDate(String monthStr) {
        try {
            if (monthStr == null || !monthStr.matches("^\\d{4}-\\d{2}$")) {
                throw new BusinessException("月份格式无效: " + monthStr + ". 请使用 YYYY-MM 格式.");
            }
            YearMonth ym = YearMonth.parse(monthStr);
            return ym.atDay(1); // Default to the first day of the month
        } catch (DateTimeParseException e) {
            log.error("无效的月份格式: {} (DateTimeParseException)", monthStr, e);
            throw new BusinessException("月份格式无效: " + monthStr + ". 解析错误.");
        }
    }

    @Override
    @Transactional // Ensure atomicity for batch operations
    public void createSingleOrBatch(EmployeeExpenseDTO dto) {
        boolean isBatchEmployees = dto.getEmployeeIds() != null && !dto.getEmployeeIds().isEmpty();
        boolean isBatchMonths = dto.getExpenseMonths() != null && !dto.getExpenseMonths().isEmpty();
        boolean isSingleEmployee = dto.getEmployeeId() != null;
        boolean isSingleDate = dto.getExpenseDate() != null;

        LocalDateTime now = LocalDateTime.now();

        if (isBatchEmployees && isBatchMonths) {
            log.info("执行批量新增员工费用: 为 {} 名员工在 {} 个月份创建记录", dto.getEmployeeIds().size(), dto.getExpenseMonths().size());
            int count = 0;
            for (Long empId : dto.getEmployeeIds()) {
                if (empId == null) {
                    log.warn("批量员工ID列表中存在null值，已跳过。");
                    continue;
                }
                for (String monthStr : dto.getExpenseMonths()) {
                    LocalDate expenseDateForMonth = parseMonthStrToLocalDate(monthStr);

                    EmployeeExpense expense = new EmployeeExpense();
                    expense.setEmployeeId(empId);
                    expense.setExpenseDate(expenseDateForMonth);
                    expense.setItemName(dto.getItemName());
                    expense.setAmount(dto.getAmount());
                    expense.setRemark(dto.getRemark());
                    expense.setCreateTime(now);
                    expense.setUpdateTime(now);
                    employeeExpenseMapper.insert(expense);
                    count++;
                }
            }
            log.info("批量新增员工费用完成，共成功创建 {} 条记录", count);

        } else if (isSingleEmployee && isSingleDate) {
            log.info("执行单条新增员工费用: 员工ID {}, 日期 {}", dto.getEmployeeId(), dto.getExpenseDate());
            EmployeeExpense expense = new EmployeeExpense();
            BeanUtils.copyProperties(dto, expense, "id", "employeeIds", "expenseMonths"); // Exclude fields not for single add
            // employeeId and expenseDate are already in dto and will be copied by BeanUtils
            expense.setCreateTime(now);
            expense.setUpdateTime(now);
            employeeExpenseMapper.insert(expense);
            log.info("单条新增员工费用成功，记录ID: {}", expense.getId());

        } else {
            // This block tries to infer if it was an intended single add but missed one field
            if (isSingleEmployee && !isSingleDate && !isBatchMonths && !isBatchEmployees) {
                throw new BusinessException("单条费用创建失败：缺少费用日期。");
            } else if (!isSingleEmployee && isSingleDate && !isBatchMonths && !isBatchEmployees) {
                throw new BusinessException("单条费用创建失败：缺少员工ID。");
            } else if (isBatchEmployees && !isBatchMonths) {
                throw new BusinessException("批量费用创建失败：缺少目标月份列表。");
            } else if (!isBatchEmployees && isBatchMonths) {
                throw new BusinessException("批量费用创建失败：缺少员工ID列表。");
            } else {
                throw new BusinessException("无效的费用创建请求：请正确提供单个员工和日期，或员工ID列表和月份列表。");
            }
        }
    }

    @Override
    public void update(EmployeeExpenseDTO employeeExpenseDTO) {
        if (employeeExpenseDTO.getId() == null) {
            throw new BusinessException("更新员工费用时ID不能为空");
        }
        // 先查询是否存在，确保是更新操作且记录存在
        EmployeeExpense existingExpense = employeeExpenseMapper.selectById(employeeExpenseDTO.getId());
        if (existingExpense == null) {
            throw new BusinessException("要更新的员工费用记录不存在");
        }

        EmployeeExpense employeeExpense = new EmployeeExpense();
        BeanUtils.copyProperties(employeeExpenseDTO, employeeExpense);
        employeeExpense.setUpdateTime(LocalDateTime.now());
        // createTime 不应在更新时改变，从现有记录中复制
        employeeExpense.setCreateTime(existingExpense.getCreateTime());

        int affectedRows = employeeExpenseMapper.update(employeeExpense);
        if (affectedRows == 0) {
            // 此处理论上不应发生，因为上面已经检查过记录存在
            throw new BusinessException("员工费用记录更新失败");
        }
        log.info("修改员工费用成功: {}", employeeExpense.getId());
    }

    @Override
    public void deleteById(Long id) {
        int affectedRows = employeeExpenseMapper.deleteById(id);
        if (affectedRows == 0) {
            throw new BusinessException("员工费用记录不存在或删除失败");
        }
        log.info("删除员工费用成功: {}", id);
    }

    @Override
    @Transactional
    public void batchDeleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("批量删除的ID列表不能为空");
        }
        int affectedRows = employeeExpenseMapper.batchDeleteByIds(ids);
        log.info("批量删除员工费用, 影响行数: {}, IDs: {}", affectedRows, ids);
        if (affectedRows != ids.size()) {
            log.warn("批量删除员工费用可能部分未成功，请求删除数量: {}, 实际删除数量: {}", ids.size(), affectedRows);
            // 可以根据业务需求决定是否抛出异常或仅记录警告
        }
    }

    @Override
    @Transactional
    public ExtendBatchResultDTO extendBatch(BatchExtendExpenseRequestDTO batchRequest) {
        ExtendBatchResultDTO result = new ExtendBatchResultDTO();
        int itemsInRequest = (batchRequest.getItems() == null) ? 0 : batchRequest.getItems().size();
        result.setTotalProcessed(itemsInRequest);

        log.info("批量延用员工费用请求，共处理 {} 项", itemsInRequest);

        if (batchRequest.getItems() == null || batchRequest.getItems().isEmpty()) {
            log.warn("批量延用项目列表为空或不存在，操作终止。");
            // totalSuccessfullyExtended and totalSkippedAsDuplicates will remain 0
            return result;
        }

        List<String> successfullyExtendedDescriptions = new ArrayList<>();
        List<String> skippedDuplicateDescriptions = new ArrayList<>();
        int successfulCreations = 0;
        int skippedDuplicates = 0;

        for (ExtendedExpenseItemDTO itemDto : batchRequest.getItems()) {
            // Construct a user-friendly description string. Use employeeName if available, otherwise ID.
            // Assuming expenseDate is already YYYY-MM-DD (first day of month)
            String itemDescription = String.format("员工 %s - 项目 \"%s\" - %s",
                    (itemDto.getEmployeeName() != null ? itemDto.getEmployeeName() : "ID:" + itemDto.getEmployeeId()),
                    itemDto.getItemName(),
                    itemDto.getExpenseDate().toString().substring(0, 7)); // YYYY-MM

            int existingCount = employeeExpenseMapper.countByEmployeeIdAndExpenseDateAndItemName(
                    itemDto.getEmployeeId(),
                    itemDto.getExpenseDate(), // This is the target date (YYYY-MM-01)
                    itemDto.getItemName()
            );

            if (existingCount > 0) {
                skippedDuplicates++;
                skippedDuplicateDescriptions.add(itemDescription + " (已存在)");
                log.warn("跳过重复的员工费用延用: {}", itemDescription);
            } else {
                EmployeeExpense employeeExpense = new EmployeeExpense();
                employeeExpense.setEmployeeId(itemDto.getEmployeeId());
                employeeExpense.setExpenseDate(itemDto.getExpenseDate());
                employeeExpense.setItemName(itemDto.getItemName());
                employeeExpense.setAmount(itemDto.getAmount());
                employeeExpense.setRemark(itemDto.getRemark());
                employeeExpense.setCreateTime(LocalDateTime.now());
                employeeExpense.setUpdateTime(LocalDateTime.now());

                employeeExpenseMapper.insert(employeeExpense);
                successfulCreations++;
                successfullyExtendedDescriptions.add(itemDescription);
                log.debug("已插入员工费用记录 (批量延用): {}", itemDescription);
            }
        }

        result.setSuccessfullyExtendedItems(successfullyExtendedDescriptions);
        result.setSkippedDuplicateItems(skippedDuplicateDescriptions);
        result.setTotalSuccessfullyExtended(successfulCreations);
        result.setTotalSkippedAsDuplicates(skippedDuplicates);

        log.info("批量延用员工费用完成。成功创建 {} 条记录, 跳过 {} 条重复记录。", successfulCreations, skippedDuplicates);
        return result;
    }

    @Override
    public PageResult getPageForUserView(Map<String, Object> params) {
        // 从Map中安全地获取分页参数，提供默认值
        Integer pageNum = params.containsKey("pageNum") && params.get("pageNum") != null ? 
                Integer.parseInt(params.get("pageNum").toString()) : 1;
        Integer pageSize = params.containsKey("pageSize") && params.get("pageSize") != null ? 
                Integer.parseInt(params.get("pageSize").toString()) : 10;

        // 处理月份参数，转换为 startDate 和 endDate
        if (params.containsKey("month") && params.get("month") != null && !params.get("month").toString().isEmpty()) {
            String monthStr = params.get("month").toString();
            if (monthStr.matches("^\\d{4}-\\d{2}$")) { // 校验 YYYY-MM 格式
                try {
                    YearMonth yearMonth = YearMonth.parse(monthStr);
                    params.put("startDate", yearMonth.atDay(1));
                    params.put("endDate", yearMonth.atEndOfMonth());
                } catch (DateTimeParseException e) {
                    log.error("getPageForUserView: 无效的月份格式 provided: {}, for params: {}", monthStr, params, e);
                    throw new BusinessException("月份格式无效: " + monthStr + ". 请使用 YYYY-MM 格式.");
                }
            } else {
                log.warn("getPageForUserView: 月份格式不符合 YYYY-MM: {}, for params: {}", monthStr, params);
                throw new BusinessException("月份格式无效: " + monthStr + ". 必须是 YYYY-MM 格式.");
            }
        }

        PageHelper.startPage(pageNum, pageSize);
        Page<EmployeeExpense> page = employeeExpenseMapper.selectPageForUserView(params);
        return new PageResult(pageNum, pageSize, page.getTotal(), page.getResult());
    }
} 