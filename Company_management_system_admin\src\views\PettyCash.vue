<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Wallet,
    Check,
    Close,
    Loading,
} from '@element-plus/icons-vue';
import {
    getPettyCashPage,
    getPettyCashById,
    addPettyCash,
    updatePettyCash,
    deletePettyCash,
    batchDeletePettyCash,
    approvePettyCash,
    getPendingPettyCash,
    getPendingPettyCashCount
} from '@/api/pettyCash';
import { getDepartmentList } from '@/api/department';
import { getEmployeeList, getEmployeePage } from '@/api/employee';
import { useAuthStore } from '@/stores/token'; // 假设的用户 store

// 用户信息
const userStore = useAuthStore();
const userInfo = computed(() => userStore.user); // 假设 store 中有 userInfo state/getter

// 备用金数据
const pettyCashData = ref([]);
const loading = ref(true);
const searchEmployeeName = ref('');
const searchDepartmentId = ref('');
const searchStatus = ref('');
const searchDate = ref(''); // 新增搜索年月

// 部门和员工列表
const departmentList = ref([]);
const employeeList = ref([]);
const loadingDepartments = ref(false);
const loadingEmployees = ref(false);
const formDepartmentId = ref(''); // 用于部门选择框的绑定值

// 员工选择分页相关
const employeeSearchQuery = ref('');
const employeeCurrentPage = ref(1);
const employeeHasMore = ref(true);
const employeePageSize = ref(100);

// 选中的记录
const selectedPettyCash = ref([]);

// 备用金审批相关
const pendingApprovalDialogVisible = ref(false);
const pendingApprovalLoading = ref(false);
const pendingPettyCash = ref([]);
const selectedPendingPettyCash = ref([]);
const pendingCount = ref(0);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 审核对话框控制
const approveDialogVisible = ref(false);
const approveId = ref(null);
const approveStatus = ref('');

// 状态选项
const statusOptions = [
    { label: '审核中', value: '审核中' },
    { label: '已审核', value: '已审核' },
    { label: '已拒绝', value: '已拒绝' },
];

// 表单数据
const form = reactive({
    id: null,
    employeeId: null,
    employeeName: '',
    departmentId: null,
    date: '', // 新增年月
    purpose: '',
    amount: 0,
    status: '审核中',
});

// 表单规则
const rules = {
    // departmentId: [
    //     { required: true, message: '请选择部门', trigger: 'change' },
    // ], // 部门不是必选的
    employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
    purpose: [{ required: true, message: '请输入用途', trigger: 'blur' }],
    amount: [
        { required: true, message: '请输入金额', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value === null || value === undefined) {
                    callback(new Error('请输入金额'));
                } else if (typeof value === 'number') {
                    callback(); // 验证通过，支持负数金额
                } else {
                    callback(new Error('请输入有效的数字金额')); // 更新错误信息
                }
            },
            trigger: 'blur'
        }
    ],
    date: [{ required: true, message: '请选择年月', trigger: 'change' }], // 新增年月校验
};

// 加载备用金数据
const loadPettyCashData = async () => {
    if (!userInfo.value || !userInfo.value.departmentId) {
        ElMessage.error('无法获取部门信息，请重新登录');
        return;
    }
    try {
        loading.value = true;
        const params = {
            page: pagination.page,
            size: pagination.size,
            departmentId: searchDepartmentId.value || null, // 如果搜索部门ID为空，则不按部门筛选
            employeeName: searchEmployeeName.value,
            status: searchStatus.value,
            date: searchDate.value // 新增年月参数
        };
        // 清理空的搜索参数
        const res = await getPettyCashPage(params);

        if (res.code === 200) {
            pettyCashData.value = res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取备用金数据失败');
        }
    } catch (error) {
        ElMessage.error('加载备用金数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data;
        } else {
            ElMessage.error(res.message || '获取部门列表失败');
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 获取待审批的备用金数量
const loadPendingPettyCashCount = async () => {
  try {
    const res = await getPendingPettyCashCount();
    if (res.code === 200) {
      pendingCount.value = res.data || 0;
      
      // 如果有待审批的备用金，显示通知
      if (pendingCount.value > 0) {
        ElNotification({
          title: '待审批提醒',
          message: `您有 ${pendingCount.value} 条备用金申请待审批`,
          type: 'info',
          duration: 5000,
          position: 'top-right'
        });
      }
    }
  } catch (error) {
    console.error('获取待审批备用金数量失败:', error);
  }
};

// 打开备用金审批对话框
const openPendingApprovalDialog = async () => {
  pendingApprovalDialogVisible.value = true;
  pendingApprovalLoading.value = true;
  selectedPendingPettyCash.value = [];
  
  try {
    // 获取待审批的备用金列表（状态为"审核中"的备用金）
    const res = await getPendingPettyCash();
    if (res.code === 200) {
      pendingPettyCash.value = res.data || [];
    } else {
      ElMessage.error(res.message || '获取待审批备用金列表失败');
      pendingPettyCash.value = [];
    }
  } catch (error) {
    console.error('获取待审批备用金列表失败:', error);
    ElMessage.error('获取待审批备用金列表失败: ' + (error.message || '未知错误'));
    pendingPettyCash.value = [];
  } finally {
    pendingApprovalLoading.value = false;
  }
};

// 审批对话框关闭时刷新待审批数量
const handlePendingApprovalDialogClosed = () => {
  loadPendingPettyCashCount();
};

// 处理待审批表格选择变化
const handlePendingSelectionChange = (selection) => {
  selectedPendingPettyCash.value = selection;
};

// 通过备用金申请
const approvePendingPettyCash = (row) => {
  ElMessageBox.confirm(
    `确定要通过员工 "${row.employeeName}" 的备用金申请(用途: ${row.purpose})吗？`,
    '审批确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    }
  )
  .then(async () => {
    pendingApprovalLoading.value = true;
    try {
      const res = await approvePettyCash(row.id, '已审核');
      
      if (res.code === 200) {
        ElMessage.success('已通过备用金申请');
        
        // 从待审批列表中移除该备用金
        pendingPettyCash.value = pendingPettyCash.value.filter(
          item => item.id !== row.id
        );
        
        // 刷新备用金列表
        loadPettyCashData();
      } else {
        ElMessage.error(res.message || '审批操作失败');
      }
    } catch (error) {
      console.error('审批操作失败:', error);
      ElMessage.error('审批操作失败: ' + (error.message || '未知错误'));
    } finally {
      pendingApprovalLoading.value = false;
    }
  })
  .catch(() => {
    // 用户取消操作，不做处理
  });
};

// 拒绝备用金申请
const rejectPendingPettyCash = (row) => {
  ElMessageBox.confirm(
    `确定要拒绝员工 "${row.employeeName}" 的备用金申请(用途: ${row.purpose})吗？`,
    '审批确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(async () => {
    pendingApprovalLoading.value = true;
    try {
      const res = await approvePettyCash(row.id, '已拒绝');
      
      if (res.code === 200) {
        ElMessage.success('已拒绝备用金申请');
        
        // 从待审批列表中移除该备用金
        pendingPettyCash.value = pendingPettyCash.value.filter(
          item => item.id !== row.id
        );
        
        // 刷新备用金列表
        loadPettyCashData();
      } else {
        ElMessage.error(res.message || '审批操作失败');
      }
    } catch (error) {
      console.error('审批操作失败:', error);
      ElMessage.error('审批操作失败: ' + (error.message || '未知错误'));
    } finally {
      pendingApprovalLoading.value = false;
    }
  })
  .catch(() => {
    // 用户取消操作，不做处理
  });
};

// 批量通过备用金申请
const batchApprovePendingPettyCash = () => {
  if (selectedPendingPettyCash.value.length === 0) {
    ElMessage.warning('请先选择要审批的备用金申请');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要通过选中的 ${selectedPendingPettyCash.value.length} 个备用金申请吗？`,
    '批量审批确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    }
  )
  .then(async () => {
    pendingApprovalLoading.value = true;
    try {
      const promises = selectedPendingPettyCash.value.map(item => 
        approvePettyCash(item.id, '已审核')
      );
      
      const results = await Promise.all(promises);
      const successCount = results.filter(res => res.code === 200).length;
      
      if (successCount > 0) {
        ElMessage.success(`已通过 ${successCount} 个备用金申请`);
        
        // 获取已成功审批的ID列表
        const approvedIds = selectedPendingPettyCash.value
          .map((item, index) => results[index].code === 200 ? item.id : null)
          .filter(id => id !== null);
        
        // 从待审批列表中移除这些备用金
        pendingPettyCash.value = pendingPettyCash.value.filter(
          item => !approvedIds.includes(item.id)
        );
        
        // 清空选中的备用金
        selectedPendingPettyCash.value = [];
        
        // 刷新备用金列表
        loadPettyCashData();
      }
      
      if (successCount < selectedPendingPettyCash.value.length) {
        ElMessage.warning(`${selectedPendingPettyCash.value.length - successCount} 个备用金申请审批失败，请重试`);
      }
    } catch (error) {
      console.error('批量审批操作失败:', error);
      ElMessage.error('批量审批操作失败: ' + (error.message || '未知错误'));
    } finally {
      pendingApprovalLoading.value = false;
    }
  })
  .catch(() => {
    // 用户取消操作，不做处理
  });
};

// 批量拒绝备用金申请
const batchRejectPendingPettyCash = () => {
  if (selectedPendingPettyCash.value.length === 0) {
    ElMessage.warning('请先选择要审批的备用金申请');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要拒绝选中的 ${selectedPendingPettyCash.value.length} 个备用金申请吗？`,
    '批量审批确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(async () => {
    pendingApprovalLoading.value = true;
    try {
      const promises = selectedPendingPettyCash.value.map(item => 
        approvePettyCash(item.id, '已拒绝')
      );
      
      const results = await Promise.all(promises);
      const successCount = results.filter(res => res.code === 200).length;
      
      if (successCount > 0) {
        ElMessage.success(`已拒绝 ${successCount} 个备用金申请`);
        
        // 获取已成功拒绝的ID列表
        const rejectedIds = selectedPendingPettyCash.value
          .map((item, index) => results[index].code === 200 ? item.id : null)
          .filter(id => id !== null);
        
        // 从待审批列表中移除这些备用金
        pendingPettyCash.value = pendingPettyCash.value.filter(
          item => !rejectedIds.includes(item.id)
        );
        
        // 清空选中的备用金
        selectedPendingPettyCash.value = [];
        
        // 刷新备用金列表
        loadPettyCashData();
      }
      
      if (successCount < selectedPendingPettyCash.value.length) {
        ElMessage.warning(`${selectedPendingPettyCash.value.length - successCount} 个备用金申请拒绝失败，请重试`);
      }
    } catch (error) {
      console.error('批量拒绝操作失败:', error);
      ElMessage.error('批量拒绝操作失败: ' + (error.message || '未知错误'));
    } finally {
      pendingApprovalLoading.value = false;
    }
  })
  .catch(() => {
    // 用户取消操作，不做处理
  });
};

// 搜索
const handleSearch = () => {
    pagination.page = 1;
    loadPettyCashData();
};

// 重置搜索
const handleReset = () => {
    searchEmployeeName.value = '';
    searchDepartmentId.value = '';
    searchStatus.value = '';
    searchDate.value = '';
    pagination.page = 1;
    loadPettyCashData();
};

// 表格选中行变化
const handleSelectionChange = (selection) => {
    selectedPettyCash.value = selection;
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadPettyCashData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadPettyCashData();
};

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadPettyCashData();
    loadPendingPettyCashCount(); // 加载待审批数量
});

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 格式化状态标签类型
const getStatusTagType = (status) => {
    switch (status) {
        case '已审核':
            return 'success';
        case '已拒绝':
            return 'danger';
        default:
            return 'warning';
    }
};

// 格式化创建时间
const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    });
};

// 处理金额变更，支持负数金额
const handleAmountChange = (value) => {
    // 移除金额必须为正数的限制，支持负数金额
    // 不再进行任何限制检查
};

// 加载员工数据（支持分页和搜索，支持部门过滤）
const loadEmployees = async (query = '', page = 1, append = false) => {
    // 确保部门列表已加载
    if (departmentList.value.length === 0) {
        await loadDepartmentList();
    }

    loadingEmployees.value = true;
    try {
        // 根据是否选择了部门来决定使用哪个API
        let res;
        if (formDepartmentId.value) {
            // 如果选择了部门，使用分页API并传入部门ID
            res = await getEmployeePage({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined,
                departmentId: formDepartmentId.value
            });
        } else {
            // 如果没有选择部门，使用新的员工列表API获取所有员工
            res = await getEmployeeList({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined
            });
        }

        if (res.code === 200) {
            let employees = [];
            let total = 0;
            let hasMore = false;

            if (formDepartmentId.value) {
                // 部门过滤模式：使用分页API的返回格式
                employees = res.data.list || [];
                total = res.data.total || 0;
                hasMore = page * employeePageSize.value < total;
            } else {
                // 全员工模式：使用新API的返回格式
                const result = res.data;
                employees = result.employees || [];
                total = result.total || 0;
                hasMore = result.hasMore;
            }

            // 为每个员工添加部门名称和离职状态
            employees.forEach(emp => {
                if (emp.departmentId) {
                    const dept = departmentList.value.find(d =>
                        d.departmentId === emp.departmentId ||
                        d.departmentId === parseInt(emp.departmentId, 10));
                    emp.departmentName = dept ? dept.departmentName : '未知部门';
                } else {
                    emp.departmentName = '未知部门';
                }

                // 检查是否已离职
                emp.isResigned = !!(emp.exitDate);
            });

            if (append) {
                employeeList.value = [...employeeList.value, ...employees];
            } else {
                employeeList.value = employees;
            }

            employeeHasMore.value = hasMore;
            employeeCurrentPage.value = page;
        } else {
            ElMessage.error(res.message || '获取员工列表失败');
            if (!append) {
                employeeList.value = [];
            }
        }
    } catch (error) {
        ElMessage.error('获取员工列表失败: ' + (error.message || '未知错误'));
        if (!append) {
            employeeList.value = [];
        }
    } finally {
        loadingEmployees.value = false;
    }
};

// 远程搜索员工
const handleEmployeeRemoteSearch = async (query) => {
    employeeSearchQuery.value = query;
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;

    if (!query && !formDepartmentId.value) {
        // 如果没有查询内容且没有选择部门，清空选项
        employeeList.value = [];
        return;
    }

    await loadEmployees(query, 1, false);
};

// 处理员工选择框可见性变化
const handleEmployeeSelectVisibleChange = (visible) => {
    if (visible && employeeList.value.length === 0) {
        // 当下拉框打开且没有数据时，根据是否选择部门来加载数据
        if (formDepartmentId.value) {
            // 如果选择了部门，只加载该部门的员工
            loadEmployees('', 1, false);
        } else {
            // 如果没有选择部门，加载所有员工的前100条
            loadEmployees('', 1, false);
        }
    }
};

// 加载更多员工
const loadMoreEmployees = async () => {
    if (!employeeHasMore.value || loadingEmployees.value) {
        return;
    }

    const nextPage = employeeCurrentPage.value + 1;
    await loadEmployees(employeeSearchQuery.value, nextPage, true);
};

// 处理员工选择滚动
const handleEmployeeScroll = (event) => {
    const { target } = event;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
        loadMoreEmployees();
    }
};

// 处理部门选择变化
const handleDepartmentChange = (departmentId) => {
    // 当部门变更时，清空员工选择
    form.employeeId = null;
    form.employeeName = '';

    // 同步设置 form.departmentId
    form.departmentId = departmentId;

    // 重置员工选择相关状态
    employeeList.value = [];
    employeeSearchQuery.value = '';
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;

    // 如果选择了部门，自动加载该部门的员工；如果清空部门，则不自动加载
    if (departmentId) {
        loadEmployees('', 1, false);
    }
};

// 员工选择改变事件
const handleEmployeeChange = (employeeId) => {
    if (!employeeId) {
        form.employeeName = '';
        return;
    }

    // 查找选中的员工信息
    const selectedEmployee = employeeList.value.find(
        (emp) => emp.employeeId === employeeId
    );
    if (selectedEmployee) {
        // 自动填充员工姓名
        form.employeeName = selectedEmployee.name;

        // 不自动填充部门信息，保持部门可以为空的逻辑
        // 用户可以手动选择部门或保持为空
    }
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.employeeId = null;
    form.employeeName = '';
    form.departmentId = null;
    form.date = ''; // 重置 form.date
    form.purpose = '';
    form.amount = 0; // 保持初始值为0，通过验证器和change事件处理验证
    form.status = '审核中';

    // 重置部门选择框绑定值
    formDepartmentId.value = '';

    // 重置员工选择分页相关状态
    employeeList.value = [];
    employeeSearchQuery.value = '';
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedPettyCash.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }

    const ids = selectedPettyCash.value.map((item) => item.id);
    const names = selectedPettyCash.value
        .map((item) => `${item.employeeName}(${item.purpose})`)
        .join('、');

    ElMessageBox.confirm(
        `确定要删除以下员工的备用金申请吗？\n${names}\n此操作不可撤销！`,
        '批量删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await batchDeletePettyCash(ids);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '批量删除成功',
                        duration: 2000,
                    });
                    selectedPettyCash.value = [];
                    loadPettyCashData();
                } else {
                    ElMessage.error(res.message || '批量删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error(
                    '批量删除失败: ' + (error.message || '未知错误')
                );
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备提交的数据
                const submitData = {
                    id: form.id, // 编辑时需要ID
                    employeeId: form.employeeId,
                    date: form.date, // 添加 date 到提交数据
                    purpose: form.purpose,
                    amount: form.amount,
                    status: form.status,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 新增备用金记录
                    res = await addPettyCash(submitData);
                } else {
                    // 更新备用金记录
                    res = await updatePettyCash(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加成功'
                                : '更新成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadPettyCashData();
                } else {
                    ElMessage.error(
                        res.message ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败')
                    );
                }
            } catch (error) {
                ElMessage.error('提交失败: ' + (error.message || '未知错误'));
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    try {
        // 确保部门列表已加载，只在列表为空时加载
        if (departmentList.value.length === 0) {
            await loadDepartmentList();
        }
        
        const res = await getPettyCashById(row.id);
        if (res.code === 200) {
            const data = res.data;
            form.id = data.id;
            form.employeeId = data.employeeId;
            form.employeeName = data.employeeName;
            form.date = data.date; // 填充 form.date
            form.purpose = data.purpose;
            form.amount = data.amount;
            form.status = data.status;

            // 设置部门ID
            if (data.departmentId) {
                form.departmentId = data.departmentId;
                formDepartmentId.value = data.departmentId;
            } else if (data.department) {
                // 如果没有直接的部门ID，尝试通过部门名称查找
                const dept = departmentList.value.find(d => d.departmentName === data.department);
                if (dept) {
                    form.departmentId = dept.departmentId;
                    formDepartmentId.value = dept.departmentId;
                }
            }

            // 直接构造员工对象供下拉框使用
            if (data.employeeId) {
                employeeList.value = [{
                    employeeId: data.employeeId,
                    name: data.employeeName,
                    positionName: data.position || '',
                    departmentId: form.departmentId,
                    departmentName: data.department || getDepartmentName(form.departmentId),
                    exitDate: data.exitDate,
                    isResigned: !!(data.exitDate)
                }];
            }

            dialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取备用金详情失败');
        }
    } catch (error) {
        ElMessage.error('获取备用金详情失败: ' + (error.message || '未知错误'));
    }
};

// 确认删除备用金记录
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除员工 "${row.employeeName}" 的备用金申请(用途: ${row.purpose})吗？此操作不可撤销！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deletePettyCash(row.id);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                        duration: 2000,
                    });
                    loadPettyCashData();
                } else {
                    ElMessage.error(res.message || '删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error('删除失败: ' + (error.message || '未知错误'));
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 打开审核对话框
const handleApprove = (row) => {
    approveId.value = row.id;
    approveStatus.value = '';
    approveDialogVisible.value = true;
};

// 提交审核
const submitApprove = async () => {
    if (!approveStatus.value) {
        ElMessage.warning('请选择审核状态');
        return;
    }

    try {
        const res = await approvePettyCash(approveId.value, approveStatus.value);
        if (res.code === 200) {
            ElMessage.success('审核成功');
            approveDialogVisible.value = false;
            loadPettyCashData();
        } else {
            ElMessage.error(res.message || '审核失败');
        }
    } catch (error) {
        ElMessage.error('审核失败: ' + (error.message || '未知错误'));
    }
};
</script>

<template>
    <div class="petty-cash-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                    style="width: 150px;"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>

                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    :loading="loadingDepartments"
                    style="width: 150px;"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>

                <el-select
                    v-model="searchStatus"
                    placeholder="选择状态"
                    clearable
                    @clear="loadPettyCashData"
                    style="width: 150px;"
                >
                    <el-option label="待审批" value="0"></el-option>
                    <el-option label="已批准" value="1"></el-option>
                    <el-option label="已驳回" value="2"></el-option>
                </el-select>

                <el-form-item label="">
                    <el-date-picker
                        v-model="searchDate"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        clearable
                        @clear="loadPettyCashData"
                        style="width: 150px;">
                    </el-date-picker>
                </el-form-item>

                <el-button
                    type="primary"
                    @click="handleSearch"
                >
                    <el-icon>
                        <Search />
                    </el-icon>搜索
                </el-button>

                <el-button @click="handleReset">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>

            <div class="action-box">
                <el-button
                    type="danger"
                    :disabled="selectedPettyCash.length === 0"
                    @click="handleBatchDelete"
                >
                    <el-icon>
                        <Delete />
                    </el-icon>批量删除
                </el-button>

                <el-button
                    type="primary"
                    class="add-btn"
                    @click="handleAdd"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加备用金
                </el-button>
                
                <el-button
                    type="success"
                    class="approve-list-btn"
                    @click="openPendingApprovalDialog"
                >
                    <el-icon>
                        <Check />
                    </el-icon>审批备用金
                    <el-badge v-if="pendingCount > 0" :value="pendingCount" class="approval-badge" />
                </el-button>
            </div>
        </div>

        <!-- 备用金表格 -->
        <el-table
            v-loading="loading"
            :data="pettyCashData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
            fit
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            />

            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="100"
                show-overflow-tooltip
                align="center"
            />

            <el-table-column
                label="部门"
                prop="department"
                min-width="120"
                show-overflow-tooltip
                align="center"
            />

            <el-table-column
                label="职位"
                prop="position"
                min-width="120"
                show-overflow-tooltip
                align="center"
            />

            <el-table-column
                label="年月"
                prop="date"
                min-width="100"
                show-overflow-tooltip
                align="center"
            />

            <el-table-column
                label="用途"
                prop="purpose"
                min-width="150"
                show-overflow-tooltip
                align="center"
            />

            <el-table-column
                label="金额"
                prop="amount"
                min-width="120"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.amount) }}
                </template>
            </el-table-column>

            <el-table-column
                label="状态"
                prop="status"
                width="100"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">{{ row.status }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="createTime"
                min-width="160"
                show-overflow-tooltip
                align="center"
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.createTime) }}
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="180"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加备用金申请' : '编辑备用金申请'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="部门"
                    prop="departmentId"
                >
                    <el-select
                        v-model="formDepartmentId"
                        placeholder="请选择部门（可选，用于筛选员工）"
                        style="width: 100%"
                        @change="handleDepartmentChange"
                        :loading="loadingDepartments"
                        clearable
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="员工"
                    prop="employeeId"
                    required
                >
                    <el-select
                        v-model="form.employeeId"
                        :placeholder="formDepartmentId ? '选择该部门的员工或输入姓名搜索' : '选择员工或输入姓名搜索'"
                        @change="handleEmployeeChange"
                        remote
                        filterable
                        :remote-method="handleEmployeeRemoteSearch"
                        :loading="loadingEmployees"
                        clearable
                        style="width: 100%"
                        popper-class="employee-select-dropdown"
                        @visible-change="handleEmployeeSelectVisibleChange"
                    >
                        <template #empty>
                            <div class="empty-text">
                                <p v-if="!employeeSearchQuery && employeeList.length === 0">
                                    {{ formDepartmentId ? '点击下拉加载该部门员工列表' : '点击下拉加载员工列表' }}
                                </p>
                                <p v-else-if="employeeSearchQuery && employeeList.length === 0">
                                    {{ formDepartmentId ? '该部门未找到匹配的员工' : '未找到匹配的员工' }}
                                </p>
                                <p v-else>暂无数据</p>
                            </div>
                        </template>

                        <!-- 员工选项列表 -->
                        <div
                            v-if="employeeList.length > 0"
                            class="employee-options-container"
                            @scroll="handleEmployeeScroll"
                        >
                            <el-option
                                v-for="employee in employeeList"
                                :key="employee.employeeId"
                                :label="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : '')) + (employee.isResigned ? '（已离职）' : '')"
                                :value="employee.employeeId"
                                :class="{ 'resigned-employee': employee.isResigned }"
                            />

                            <!-- 加载更多提示 -->
                            <div
                                v-if="employeeHasMore"
                                class="load-more-option"
                                @click="loadMoreEmployees"
                            >
                                <el-icon v-if="loadingEmployees" class="is-loading">
                                    <Loading />
                                </el-icon>
                                <span>{{ loadingEmployees ? '加载中...' : '滚动或点击加载更多' }}</span>
                            </div>
                        </div>
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="年月"
                    prop="date"
                    required
                >
                    <el-date-picker
                        v-model="form.date"
                        type="month"
                        placeholder="请选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="用途"
                    prop="purpose"
                    required
                >
                    <el-input
                        v-model="form.purpose"
                        type="textarea"
                        :rows="3"
                        :maxlength="200"
                        show-word-limit
                        placeholder="请输入备用金用途"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="金额"
                    prop="amount"
                    required
                >
                    <el-input-number
                        v-model="form.amount"
                        :precision="2"
                        :step="100"
                        :controls="true"
                        @change="handleAmountChange"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    v-if="dialogType === 'edit'"
                    label="状态"
                    prop="status"
                >
                    <el-select
                        v-model="form.status"
                        placeholder="请选择状态"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>

            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- 审核对话框 -->
        <el-dialog
            v-model="approveDialogVisible"
            title="审核备用金申请"
            width="400px"
            class="custom-dialog"
        >
            <el-form label-width="80px">
                <el-form-item label="审核结果">
                    <el-radio-group v-model="approveStatus">
                        <el-radio label="已审核">审核通过</el-radio>
                        <el-radio label="已拒绝">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="approveDialogVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    :disabled="!approveStatus"
                    @click="submitApprove"
                >确认</el-button>
            </div>
        </el-dialog>
        
        <!-- 备用金审批对话框 -->
        <el-dialog
            v-model="pendingApprovalDialogVisible"
            title="备用金审批"
            width="90%"
            :style="{ maxWidth: '1200px' }"
            :close-on-click-modal="false"
            append-to-body
            @closed="handlePendingApprovalDialogClosed"
        >
            <div class="action-toolbar">
                <el-button 
                    type="success" 
                    :icon="Check" 
                    @click="batchApprovePendingPettyCash" 
                    :disabled="selectedPendingPettyCash.length === 0">
                    批量通过
                </el-button>
                <el-button 
                    type="danger" 
                    :icon="Close" 
                    @click="batchRejectPendingPettyCash" 
                    :disabled="selectedPendingPettyCash.length === 0">
                    批量拒绝
                </el-button>
            </div>

            <el-table
                v-loading="pendingApprovalLoading"
                :data="pendingPettyCash"
                border
                class="custom-table approval-table"
                :max-height="'calc(70vh - 180px)'"
                style="width: 100%; margin-bottom: 0;"
                @selection-change="handlePendingSelectionChange"
                fit
            >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="employeeName" label="员工姓名" min-width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="date" label="年月" min-width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="department" label="部门" min-width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="position" label="职位" min-width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="purpose" label="用途" min-width="150" show-overflow-tooltip align="center" />
                <el-table-column label="金额" min-width="120" align="right">
                    <template #default="{ row }">
                        {{ formatCurrency(row.amount) }}
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" min-width="160" show-overflow-tooltip align="center">
                    <template #default="{ row }">
                        {{ formatDateTime(row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right" align="center">
                    <template #default="scope">
                        <div class="approval-action-buttons">
                            <el-button
                                type="success"
                                link
                                :icon="Check"
                                @click="approvePendingPettyCash(scope.row)"
                            >
                                通过
                            </el-button>
                            <el-button
                                type="danger"
                                link
                                :icon="Close"
                                @click="rejectPendingPettyCash(scope.row)"
                            >
                                拒绝
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<style scoped>
.petty-cash-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: nowrap;
}

.search-box .el-input,
.search-box .el-select {
    width: 220px;
}

.search-box .el-form-item {
    margin-bottom: 0;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.approve-list-btn {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
    position: relative;
}

.approve-list-btn:hover {
    background-color: #85ce61;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.approval-badge {
    position: absolute;
    top: -8px;
    right: -8px;
}

.action-toolbar {
    margin-bottom: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.approval-table {
    margin-bottom: 20px !important;
}

.approval-action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.approve-btn,
.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.approve-btn {
    background-color: #e1f3d8;
    color: #67c23a;
}

.approve-btn:hover {
    background-color: #67c23a;
    color: white;
    transform: translateY(-2px);
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.form-help-text {
    font-size: 12px;
    color: #909399;
    line-height: 1;
    padding-top: 4px;
    margin-left: 2px;
}

/* 员工选择下拉框样式 */
:deep(.employee-select-dropdown) {
    max-height: 300px;
}

.employee-options-container {
    max-height: 250px;
    overflow-y: auto;
}

.load-more-option {
    padding: 8px 12px;
    text-align: center;
    color: #409eff;
    cursor: pointer;
    border-top: 1px solid #ebeef5;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.load-more-option:hover {
    background-color: #f5f7fa;
}

.resigned-employee {
    color: #909399;
}

/* 空状态提示样式 */
.empty-text {
    color: #909399;
    font-size: 14px;
    text-align: center;
    padding: 10px;
    margin: 0;
}
</style> 