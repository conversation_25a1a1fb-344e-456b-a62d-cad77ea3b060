<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.MeetingLocationMapper">

    <!-- 会议地点结果映射 -->
    <resultMap id="MeetingLocationResultMap" type="org.example.company_management.entity.MeetingLocation">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="capacity" column="capacity"/>
        <result property="facilities" column="facilities"/>
        <result property="openTime" column="open_time"/>
        <result property="closeTime" column="close_time"/>
        <result property="availableDays" column="available_days"/>
        <result property="availableStartDate" column="available_start_date"/>
        <result property="availableEndDate" column="available_end_date"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 插入会议地点 -->
    <insert id="insert" parameterType="org.example.company_management.entity.MeetingLocation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meeting_location (
            name, description, capacity, facilities,
            open_time, close_time, available_days,
            available_start_date, available_end_date, status
        ) VALUES (
            #{name}, #{description}, #{capacity}, #{facilities},
            #{openTime}, #{closeTime}, #{availableDays},
            #{availableStartDate}, #{availableEndDate}, #{status}
        )
    </insert>

    <!-- 根据ID删除会议地点 -->
    <delete id="deleteById" parameterType="int">
        DELETE FROM meeting_location WHERE id = #{id}
    </delete>

    <!-- 更新会议地点 -->
    <update id="update" parameterType="org.example.company_management.entity.MeetingLocation">
        UPDATE meeting_location SET
            name = #{name},
            description = #{description},
            capacity = #{capacity},
            facilities = #{facilities},
            open_time = #{openTime},
            close_time = #{closeTime},
            available_days = #{availableDays},
            available_start_date = #{availableStartDate},
            available_end_date = #{availableEndDate},
            status = #{status},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询会议地点 -->
    <select id="selectById" parameterType="int" resultMap="MeetingLocationResultMap">
        SELECT id, name, description, capacity, facilities,
               open_time, close_time, available_days,
               available_start_date, available_end_date, status,
               create_time, update_time
        FROM meeting_location
        WHERE id = #{id}
    </select>

    <!-- 分页查询会议地点列表 -->
    <select id="selectByPage" parameterType="map" resultMap="MeetingLocationResultMap">
        SELECT id, name, description, capacity, facilities,
               open_time, close_time, available_days,
               available_start_date, available_end_date, status,
               create_time, update_time
        FROM meeting_location
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <!-- 查询会议地点总数 -->
    <select id="countByPage" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM meeting_location
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 查询所有启用的会议地点 -->
    <select id="selectActiveLocations" resultMap="MeetingLocationResultMap">
        SELECT id, name, description, capacity, facilities,
               open_time, close_time, available_days,
               available_start_date, available_end_date, status,
               create_time, update_time
        FROM meeting_location
        WHERE status = 'ACTIVE'
        ORDER BY name ASC
    </select>

    <!-- 查询所有会议地点（包括禁用的） -->
    <select id="selectAllLocations" resultMap="MeetingLocationResultMap">
        SELECT id, name, description, capacity, facilities,
               open_time, close_time, available_days,
               available_start_date, available_end_date, status,
               create_time, update_time
        FROM meeting_location
        ORDER BY status DESC, name ASC
    </select>

    <!-- 根据名称查询会议地点（用于重名检查） -->
    <select id="selectByName" resultMap="MeetingLocationResultMap">
        SELECT id, name, description, capacity, facilities,
               open_time, close_time, available_days,
               available_start_date, available_end_date, status,
               create_time, update_time
        FROM meeting_location
        WHERE name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 已预订时间段结果映射 -->
    <resultMap id="BookedTimeSlotResultMap" type="org.example.company_management.dto.LocationAvailabilityDTO$BookedTimeSlot">
        <result property="meetingId" column="meeting_id"/>
        <result property="meetingTitle" column="meeting_title"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="bookedByName" column="booked_by_name"/>
        <result property="bookedById" column="booked_by_id"/>
        <result property="isCurrentMeeting" column="is_current_meeting"/>
    </resultMap>

    <!-- 查询指定地点在指定时间范围内的已预订时间段 -->
    <select id="selectBookedTimeSlots" resultMap="BookedTimeSlotResultMap">
        SELECT
            m.id as meeting_id,
            m.title as meeting_title,
            m.start_time,
            m.end_time,
            e.name as booked_by_name,
            e.employee_id as booked_by_id,
            <if test="excludeMeetingId != null">
                CASE WHEN m.id = #{excludeMeetingId} THEN 1 ELSE 0 END as is_current_meeting
            </if>
            <if test="excludeMeetingId == null">
                0 as is_current_meeting
            </if>
        FROM meeting m
        LEFT JOIN employee e ON m.responsible_id = e.employee_id
        WHERE m.location_id = #{locationId}
        AND (
            (m.start_time BETWEEN #{startDate} AND #{endDate})
            OR (m.end_time BETWEEN #{startDate} AND #{endDate})
            OR (m.start_time &lt;= #{startDate} AND m.end_time &gt;= #{endDate})
        )
        ORDER BY m.start_time ASC
    </select>

</mapper>
