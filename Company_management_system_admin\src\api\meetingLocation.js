import request from '@/utils/request'

/**
 * 分页查询会议地点列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMeetingLocationPage(params) {
  return request({
    url: '/admin/meeting-location',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取会议地点详情
 * @param {number} id 地点ID
 * @returns {Promise}
 */
export function getMeetingLocationById(id) {
  return request({
    url: `/admin/meeting-location/${id}`,
    method: 'get'
  })
}

/**
 * 创建会议地点
 * @param {Object} data 地点信息
 * @returns {Promise}
 */
export function createMeetingLocation(data) {
  return request({
    url: '/admin/meeting-location',
    method: 'post',
    data
  })
}

/**
 * 更新会议地点
 * @param {number} id 地点ID
 * @param {Object} data 地点信息
 * @returns {Promise}
 */
export function updateMeetingLocation(id, data) {
  return request({
    url: `/admin/meeting-location/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除会议地点
 * @param {number} id 地点ID
 * @returns {Promise}
 */
export function deleteMeetingLocation(id) {
  return request({
    url: `/admin/meeting-location/${id}`,
    method: 'delete'
  })
}

/**
 * 获取所有启用的会议地点列表
 * @returns {Promise}
 */
export function getActiveLocations() {
  return request({
    url: '/admin/meeting-location/active',
    method: 'get'
  })
}

/**
 * 获取所有会议地点列表（包括禁用的）
 * @returns {Promise}
 */
export function getAllLocations() {
  return request({
    url: '/admin/meeting-location/all',
    method: 'get'
  })
}

/**
 * 查询地点可用性信息
 * @param {number} locationId 地点ID
 * @param {string} startDate 查询开始日期时间 (YYYY-MM-DD HH:mm:ss)
 * @param {string} endDate 查询结束日期时间 (YYYY-MM-DD HH:mm:ss)
 * @param {number} excludeMeetingId 排除的会议ID（编辑模式下使用，可选）
 * @returns {Promise}
 */
export function getLocationAvailability(locationId, startDate, endDate, excludeMeetingId = null) {
  const params = {
    startDate,
    endDate
  }

  if (excludeMeetingId) {
    params.excludeMeetingId = excludeMeetingId
  }

  return request({
    url: `/admin/meeting-location/${locationId}/availability`,
    method: 'get',
    params
  })
}
