package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.StockPrice;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 股票价格数据访问接口
 */
@Mapper
public interface StockPriceMapper {

    /**
     * 插入股票价格记录
     * @param stockPrice 股票价格对象
     * @return 影响的行数
     */
    int insert(StockPrice stockPrice);

    /**
     * 根据ID删除股票价格记录
     * @param id 股票价格ID
     * @return 影响的行数
     */
    int deleteById(Long id);

    /**
     * 更新股票价格记录
     * @param stockPrice 股票价格对象
     * @return 影响的行数
     */
    int updateById(StockPrice stockPrice);

    /**
     * 根据ID查询股票价格记录
     * @param id 股票价格ID
     * @return 股票价格对象
     */
    StockPrice selectById(Long id);

    // 删除：selectByTime - 功能重复，可用日期范围查询替代

    /**
     * 分页查询股票价格记录
     * @param params 查询参数
     * @return 股票价格列表
     */
    List<StockPrice> selectByPage(Map<String, Object> params);

    /**
     * 分页查询股票价格记录（使用PageHelper）
     * @param params 查询参数
     * @return 股票价格列表
     */
    List<StockPrice> selectByPageWithPageHelper(Map<String, Object> params);

    /**
     * 统计股票价格记录总数
     * @param params 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> params);

    /**
     * 查询所有股票价格记录（不分页）
     * @return 股票价格列表
     */
    List<StockPrice> selectAll();

    /**
     * 查询指定日期范围内的股票价格记录
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 股票价格列表
     */
    List<StockPrice> selectByDateRange(@Param("startDate") LocalDate startDate, 
                                       @Param("endDate") LocalDate endDate);

    /**
     * 查询最新的股票价格记录
     * @return 最新的股票价格对象
     */
    StockPrice selectLatest();

    // 删除：selectByRemarkLike - 通过分页查询的remark参数实现

    /**
     * 检查指定时间是否已存在股票价格记录
     * @param time 股票价格时间
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的记录数
     */
    int countByTime(@Param("time") LocalDate time, @Param("excludeId") Long excludeId);
}
