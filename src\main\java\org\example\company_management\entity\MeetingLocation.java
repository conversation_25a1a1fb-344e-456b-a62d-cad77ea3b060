package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.LocalDate;

/**
 * 会议地点实体类
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingLocation {
    
    /**
     * 地点ID
     */
    private Integer id;
    
    /**
     * 地点名称
     */
    private String name;
    
    /**
     * 地点描述
     */
    private String description;
    
    /**
     * 容纳人数
     */
    private Integer capacity;
    
    /**
     * 设施设备描述
     */
    private String facilities;
    
    /**
     * 开放开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime openTime;
    
    /**
     * 开放结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime closeTime;
    
    /**
     * 可用星期（1-7，逗号分隔）
     */
    private String availableDays;

    /**
     * 开放开始日期（可选，NULL表示无限制）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate availableStartDate;

    /**
     * 开放结束日期（可选，NULL表示无限制）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate availableEndDate;

    /**
     * 状态：ACTIVE-启用、INACTIVE-禁用
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 获取开放时间段的显示文本（非数据库字段）
     */
    public String getOpenTimeDisplay() {
        if (openTime != null && closeTime != null) {
            return String.format("%s - %s", 
                openTime.toString().substring(0, 5), 
                closeTime.toString().substring(0, 5));
        }
        return "";
    }
    
    /**
     * 获取可用天数的显示文本（非数据库字段）
     */
    public String getAvailableDaysDisplay() {
        if (availableDays == null || availableDays.isEmpty()) {
            return "";
        }
        
        String[] days = availableDays.split(",");
        StringBuilder display = new StringBuilder();
        String[] dayNames = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        
        for (String day : days) {
            try {
                int dayNum = Integer.parseInt(day.trim());
                if (dayNum >= 1 && dayNum <= 7) {
                    if (display.length() > 0) {
                        display.append("、");
                    }
                    display.append(dayNames[dayNum]);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的天数
            }
        }
        
        return display.toString();
    }
    
    /**
     * 获取完整的地点信息显示（非数据库字段）
     */
    public String getFullDisplay() {
        StringBuilder display = new StringBuilder(name);
        
        String timeDisplay = getOpenTimeDisplay();
        String daysDisplay = getAvailableDaysDisplay();
        
        if (!timeDisplay.isEmpty() || !daysDisplay.isEmpty()) {
            display.append(" (");
            if (!daysDisplay.isEmpty()) {
                display.append(daysDisplay);
            }
            if (!timeDisplay.isEmpty()) {
                if (!daysDisplay.isEmpty()) {
                    display.append(" ");
                }
                display.append(timeDisplay);
            }
            display.append(")");
        }
        
        return display.toString();
    }
}
