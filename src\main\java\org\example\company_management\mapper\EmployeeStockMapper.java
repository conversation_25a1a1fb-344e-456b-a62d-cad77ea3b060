package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.dto.StockOverviewDTO;
import org.example.company_management.entity.EmployeeStock;

import java.util.List;
import java.util.Map;

/**
 * 员工股票数据访问接口
 */
@Mapper
public interface EmployeeStockMapper {

    /**
     * 插入员工股票记录
     * @param employeeStock 员工股票对象
     * @return 影响的行数
     */
    int insert(EmployeeStock employeeStock);

    /**
     * 根据ID删除员工股票记录
     * @param id 员工股票记录ID
     * @return 影响的行数
     */
    int deleteById(Long id);

    /**
     * 更新员工股票记录
     * @param employeeStock 员工股票对象
     * @return 影响的行数
     */
    int updateById(EmployeeStock employeeStock);

    /**
     * 根据ID查询员工股票记录
     * @param id 员工股票记录ID
     * @return 员工股票对象
     */
    EmployeeStock selectById(Long id);

    /**
     * 分页查询员工股票记录（包含员工和部门信息）
     * @param params 查询参数
     * @return 员工股票列表
     */
    List<EmployeeStock> selectByPage(Map<String, Object> params);

    /**
     * 分页查询员工股票记录（使用PageHelper）
     * @param params 查询参数
     * @return 员工股票列表
     */
    List<EmployeeStock> selectByPageWithPageHelper(Map<String, Object> params);

    /**
     * 统计员工股票记录总数
     * @param params 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> params);

    /**
     * 根据员工ID查询员工股票记录
     * @param employeeId 员工ID
     * @return 员工股票列表
     */
    List<EmployeeStock> selectByEmployeeId(@Param("employeeId") Integer employeeId);

    // 删除：selectByDepartmentId - 可通过分页查询+部门筛选实现

    /**
     * 查询指定员工的股票总数量
     * @param employeeId 员工ID
     * @return 股票总数量
     */
    Integer sumQuantityByEmployeeId(@Param("employeeId") Integer employeeId);

    /**
     * 查询指定员工的股票总价值
     * @param employeeId 员工ID
     * @return 股票总价值
     */
    java.math.BigDecimal sumTotalValueByEmployeeId(@Param("employeeId") Integer employeeId);

    /**
     * 查询已解禁的员工股票记录
     * @return 已解禁的员工股票列表
     */
    List<EmployeeStock> selectUnlockedStocks();

    /**
     * 查询未解禁的员工股票记录
     * @return 未解禁的员工股票列表
     */
    List<EmployeeStock> selectLockedStocks();

    // 删除：selectByStockId - 使用场景较少

    /**
     * 检查员工是否已持有指定股票
     * @param employeeId 员工ID
     * @param stockId 股票价格ID
     * @param excludeId 排除的记录ID（用于更新时检查）
     * @return 存在的记录数
     */
    int countByEmployeeAndStock(@Param("employeeId") Integer employeeId, 
                               @Param("stockId") Long stockId, 
                               @Param("excludeId") Long excludeId);

    /**
     * 查询员工股票统计信息
     * @return 统计信息列表
     */
    List<Map<String, Object>> selectStockStatistics();

    /**
     * 查询持股总览数据（管理员专用）
     * @param params 查询参数
     * @return 持股总览数据列表
     */
    List<StockOverviewDTO> selectStockOverview(Map<String, Object> params);

    // ==================== 用户端专用方法 ====================

    /**
     * 统计指定员工的已解禁股票数量
     * @param employeeId 员工ID
     * @return 已解禁股票数量
     */
    Integer countUnlockedStocksByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 统计指定员工的未解禁股票数量
     * @param employeeId 员工ID
     * @return 未解禁股票数量
     */
    Integer countLockedStocksByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 统计指定员工的股票记录总数
     * @param employeeId 员工ID
     * @return 股票记录总数
     */
    Integer countStockRecordsByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 统计指定员工的股票记录总数（别名方法）
     * @param employeeId 员工ID
     * @return 股票记录总数
     */
    Integer countByEmployeeId(@Param("employeeId") Integer employeeId);

    /**
     * 计算指定员工的股票总数量（用户端专用）
     * @param employeeId 员工ID
     * @return 股票总数量
     */
    Integer sumQuantityByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 计算指定员工的股票总价值（用户端专用，使用最新股票价格）
     * @param employeeId 员工ID
     * @return 股票总价值
     */
    java.math.BigDecimal sumValueByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 查询员工股票详细统计信息（新增）
     * @param employeeId 员工ID
     * @return 详细统计信息Map
     */
    Map<String, Object> selectDetailedStatisticsByEmployee(@Param("employeeId") Integer employeeId);

    /**
     * 计算员工可提现数量（新增）
     * @param employeeId 员工ID
     * @return 可提现数量和金额信息Map
     */
    Map<String, Object> calculateAvailableQuantity(@Param("employeeId") Integer employeeId);
}
