package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工股票查询DTO，用于封装查询条件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeStockQueryDTO {
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 员工ID
     */
    private Integer employeeId;

    /**
     * 员工姓名（模糊搜索）
     */
    private String employeeName;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 获取时间开始（字符串格式，用于查询参数）
     */
    private String acquisitionStartTime;

    /**
     * 获取时间结束（字符串格式，用于查询参数）
     */
    private String acquisitionEndTime;

    /**
     * 是否已解禁（true-已解禁，false-未解禁，null-全部）
     */
    private Boolean isUnlocked;

    /**
     * 备注（模糊搜索）
     */
    private String remark;

    /**
     * 排序字段（默认按获取时间倒序）
     */
    private String orderBy = "acquisition_time";

    /**
     * 排序方向（ASC/DESC，默认DESC）
     */
    private String orderDirection = "DESC";
}
