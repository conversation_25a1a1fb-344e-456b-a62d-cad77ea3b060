package org.example.company_management.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.reflect.Field;
import java.time.LocalDate;

/**
 * 日期范围验证器
 * 验证开始日期不能晚于结束日期
 */
public class DateRangeValidator implements ConstraintValidator<ValidDateRange, Object> {
    
    private String startDateField;
    private String endDateField;
    
    @Override
    public void initialize(ValidDateRange constraintAnnotation) {
        this.startDateField = constraintAnnotation.startDateField();
        this.endDateField = constraintAnnotation.endDateField();
    }
    
    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {
        if (object == null) {
            return true; // 让其他验证器处理null值
        }
        
        try {
            LocalDate startDate = getFieldValue(object, startDateField);
            LocalDate endDate = getFieldValue(object, endDateField);
            
            // 如果两个日期都为null，表示无限制，验证通过
            if (startDate == null && endDate == null) {
                return true;
            }
            
            // 如果只有一个日期为null，表示单边限制，验证通过
            if (startDate == null || endDate == null) {
                return true;
            }
            
            // 如果两个日期都不为null，检查开始日期是否不晚于结束日期
            if (startDate.isAfter(endDate)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("开始日期不能晚于结束日期")
                       .addPropertyNode(endDateField)
                       .addConstraintViolation();
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            // 如果反射获取字段值失败，返回false
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("日期范围验证失败")
                   .addConstraintViolation();
            return false;
        }
    }
    
    /**
     * 通过反射获取字段值
     */
    private LocalDate getFieldValue(Object object, String fieldName) throws Exception {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return (LocalDate) field.get(object);
    }
}
