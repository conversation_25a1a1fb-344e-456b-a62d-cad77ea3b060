<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.DepartmentLeaderMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.DepartmentLeader">
        <id column="id" property="id"/>
        <result column="department_id" property="departmentId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="leader_role" property="leaderRole"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="is_active" property="isActive"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <!-- 关联查询字段 -->
        <result column="department_name" property="departmentName"/>
        <result column="department_description" property="departmentDescription"/>
        <result column="leader_name" property="leaderName"/>
        <result column="leader_email" property="leaderEmail"/>
        <result column="leader_phone" property="leaderPhone"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        dl.id, dl.department_id, dl.employee_id, dl.leader_role, dl.start_date, 
        dl.end_date, dl.is_active, dl.remark, dl.create_time, dl.update_time
    </sql>

    <!-- 关联查询列 -->
    <sql id="Join_Column_List">
        <include refid="Base_Column_List"/>,
        d.department_name, d.department_description,
        e.name as leader_name, e.email as leader_email, e.phone as leader_phone
    </sql>

    <!-- 插入部门负责人关联记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.DepartmentLeader" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO department_leader (
            department_id, employee_id, leader_role, start_date, end_date, 
            is_active, remark, create_time, update_time
        ) VALUES (
            #{departmentId}, #{employeeId}, #{leaderRole}, #{startDate}, #{endDate},
            #{isActive}, #{remark}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM department_leader WHERE id = #{id}
    </delete>

    <!-- 删除指定部门的指定负责人 -->
    <delete id="deleteByDepartmentAndEmployee">
        DELETE FROM department_leader 
        WHERE department_id = #{departmentId} AND employee_id = #{employeeId}
    </delete>

    <!-- 删除部门的所有负责人 -->
    <delete id="deleteByDepartmentId" parameterType="java.lang.Integer">
        DELETE FROM department_leader WHERE department_id = #{departmentId}
    </delete>

    <!-- 删除员工的所有部门负责人关联 -->
    <delete id="deleteByEmployeeId" parameterType="java.lang.Integer">
        DELETE FROM department_leader WHERE employee_id = #{employeeId}
    </delete>

    <!-- 更新部门负责人关联记录 -->
    <update id="updateById" parameterType="org.example.company_management.entity.DepartmentLeader">
        UPDATE department_leader SET
            department_id = #{departmentId},
            employee_id = #{employeeId},
            leader_role = #{leaderRole},
            start_date = #{startDate},
            end_date = #{endDate},
            is_active = #{isActive},
            remark = #{remark},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
            <include refid="Join_Column_List"/>
        FROM department_leader dl
        LEFT JOIN department d ON dl.department_id = d.department_id
        LEFT JOIN employee e ON dl.employee_id = e.employee_id
        WHERE dl.id = #{id}
    </select>

    <!-- 查询部门的所有负责人 -->
    <select id="selectByDepartmentId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Join_Column_List"/>
        FROM department_leader dl
        LEFT JOIN department d ON dl.department_id = d.department_id
        LEFT JOIN employee e ON dl.employee_id = e.employee_id
        WHERE dl.department_id = #{departmentId}
        <if test="includeInactive == null or includeInactive == false">
            AND dl.is_active = TRUE
        </if>
        ORDER BY 
            CASE dl.leader_role 
                WHEN 'PRIMARY' THEN 1 
                WHEN 'DEPUTY' THEN 2 
                WHEN 'ASSISTANT' THEN 3 
                ELSE 4 
            END,
            dl.start_date DESC
    </select>

    <!-- 查询员工负责的所有部门 -->
    <select id="selectByEmployeeId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Join_Column_List"/>
        FROM department_leader dl
        LEFT JOIN department d ON dl.department_id = d.department_id
        LEFT JOIN employee e ON dl.employee_id = e.employee_id
        WHERE dl.employee_id = #{employeeId}
        <if test="includeInactive == null or includeInactive == false">
            AND dl.is_active = TRUE
        </if>
        ORDER BY dl.start_date DESC
    </select>

    <!-- 查询部门的主要负责人 -->
    <select id="selectPrimaryLeaderByDepartmentId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
            <include refid="Join_Column_List"/>
        FROM department_leader dl
        LEFT JOIN department d ON dl.department_id = d.department_id
        LEFT JOIN employee e ON dl.employee_id = e.employee_id
        WHERE dl.department_id = #{departmentId}
          AND dl.leader_role = 'PRIMARY'
          AND dl.is_active = TRUE
        ORDER BY dl.start_date DESC
        LIMIT 1
    </select>

    <!-- 查询部门的所有激活状态负责人ID列表 -->
    <select id="selectActiveLeaderIdsByDepartmentId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT employee_id 
        FROM department_leader 
        WHERE department_id = #{departmentId} AND is_active = TRUE
        ORDER BY 
            CASE leader_role 
                WHEN 'PRIMARY' THEN 1 
                WHEN 'DEPUTY' THEN 2 
                WHEN 'ASSISTANT' THEN 3 
                ELSE 4 
            END
    </select>

    <!-- 查询员工负责的所有激活状态部门ID列表 -->
    <select id="selectActiveDepartmentIdsByEmployeeId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT department_id 
        FROM department_leader 
        WHERE employee_id = #{employeeId} AND is_active = TRUE
        ORDER BY start_date DESC
    </select>

    <!-- 检查员工是否为指定部门的负责人 -->
    <select id="checkIsLeader" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM department_leader 
        WHERE department_id = #{departmentId} 
          AND employee_id = #{employeeId} 
          AND is_active = TRUE
    </select>

    <!-- 检查员工是否为指定部门的主要负责人 -->
    <select id="checkIsPrimaryLeader" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM department_leader 
        WHERE department_id = #{departmentId} 
          AND employee_id = #{employeeId} 
          AND leader_role = 'PRIMARY'
          AND is_active = TRUE
    </select>

    <!-- 统计部门的负责人数量 -->
    <select id="countLeadersByDepartmentId" resultType="int">
        SELECT COUNT(1)
        FROM department_leader 
        WHERE department_id = #{departmentId}
        <if test="includeInactive == null or includeInactive == false">
            AND is_active = TRUE
        </if>
    </select>

    <!-- 统计员工负责的部门数量 -->
    <select id="countDepartmentsByEmployeeId" resultType="int">
        SELECT COUNT(1)
        FROM department_leader 
        WHERE employee_id = #{employeeId}
        <if test="includeInactive == null or includeInactive == false">
            AND is_active = TRUE
        </if>
    </select>

    <!-- 批量插入部门负责人关联记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO department_leader (
            department_id, employee_id, leader_role, start_date, end_date, 
            is_active, remark, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.departmentId}, #{item.employeeId}, #{item.leaderRole}, 
             #{item.startDate}, #{item.endDate}, #{item.isActive}, 
             #{item.remark}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 更新部门负责人的激活状态 -->
    <update id="updateActiveStatus">
        UPDATE department_leader 
        SET is_active = #{isActive}, update_time = NOW()
        WHERE department_id = #{departmentId} AND employee_id = #{employeeId}
    </update>

</mapper>
