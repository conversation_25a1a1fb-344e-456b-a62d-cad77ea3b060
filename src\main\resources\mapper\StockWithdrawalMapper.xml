<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.StockWithdrawalMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.StockWithdrawal">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="employee_id" property="employeeId" jdbcType="INTEGER"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="apply_time" property="applyTime" jdbcType="TIMESTAMP"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="auditor_id" property="auditorId" jdbcType="INTEGER"/>
        <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 包含关联信息的结果映射 -->
    <resultMap id="DetailResultMap" type="org.example.company_management.entity.StockWithdrawal" extends="BaseResultMap">
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="employee_phone" property="employeePhone" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="auditor_name" property="auditorName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, employee_id, quantity, unit_price, total_amount, status,
        apply_time, audit_time, auditor_id, reject_reason, remark, create_time, update_time
    </sql>

    <!-- 包含关联信息的字段 -->
    <sql id="Detail_Column_List">
        sw.id, sw.employee_id, sw.quantity, sw.unit_price, sw.total_amount, sw.status,
        sw.apply_time, sw.audit_time, sw.auditor_id, sw.reject_reason, sw.remark,
        sw.create_time, sw.update_time,
        e.name as employee_name, e.phone as employee_phone, d.department_name,
        a.name as auditor_name
    </sql>

    <!-- 插入提现申请 -->
    <insert id="insert" parameterType="org.example.company_management.entity.StockWithdrawal" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_withdrawal (
            employee_id, quantity, unit_price, total_amount, status,
            apply_time, auditor_id, reject_reason, remark
        ) VALUES (
            #{employeeId}, #{quantity}, #{unitPrice}, #{totalAmount}, #{status},
            #{applyTime}, #{auditorId}, #{rejectReason}, #{remark}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_withdrawal
        WHERE id = #{id}
    </select>

    <!-- 根据ID查询（包含关联信息） -->
    <select id="selectByIdWithDetails" parameterType="java.lang.Long" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM stock_withdrawal sw
        LEFT JOIN employee e ON sw.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee a ON sw.auditor_id = a.employee_id
        WHERE sw.id = #{id}
    </select>

    <!-- 更新提现申请 -->
    <update id="update" parameterType="org.example.company_management.entity.StockWithdrawal">
        UPDATE stock_withdrawal
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据条件查询 -->
    <select id="selectByCondition" parameterType="org.example.company_management.dto.StockWithdrawalQueryDTO" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM stock_withdrawal sw
        LEFT JOIN employee e ON sw.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee a ON sw.auditor_id = a.employee_id
        <where>
            <if test="employeeId != null">
                AND sw.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND sw.status = #{status}
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE(sw.apply_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(sw.apply_time) &lt;= #{endDate}
            </if>
            <if test="auditorId != null">
                AND sw.auditor_id = #{auditorId}
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                sw.${orderBy}
            </when>
            <otherwise>
                sw.apply_time
            </otherwise>
        </choose>
        <choose>
            <when test="orderDirection != null and orderDirection.toUpperCase() == 'ASC'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询员工的提现申请 -->
    <select id="selectByEmployeeId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM stock_withdrawal sw
        LEFT JOIN employee e ON sw.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee a ON sw.auditor_id = a.employee_id
        WHERE sw.employee_id = #{employeeId}
        <if test="status != null and status != ''">
            AND sw.status = #{status}
        </if>
        ORDER BY sw.apply_time DESC
    </select>

    <!-- 查询待审核申请 -->
    <select id="selectPendingList" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM stock_withdrawal sw
        LEFT JOIN employee e ON sw.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        WHERE sw.status = 'PENDING'
        ORDER BY sw.apply_time ASC
    </select>

    <!-- 统计已提现数量 -->
    <select id="getWithdrawnQuantity" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(quantity), 0)
        FROM stock_withdrawal
        WHERE employee_id = #{employeeId} AND status = 'APPROVED'
    </select>

    <!-- 统计已提现金额 -->
    <select id="getWithdrawnAmount" parameterType="java.lang.Integer" resultType="map">
        SELECT 
            COALESCE(SUM(quantity), 0) as withdrawnQuantity,
            COALESCE(SUM(total_amount), 0) as withdrawnValue
        FROM stock_withdrawal
        WHERE employee_id = #{employeeId} AND status = 'APPROVED'
    </select>

    <!-- 按状态统计申请数量 -->
    <select id="countByStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM stock_withdrawal
        WHERE status = #{status}
    </select>

    <!-- 统计员工申请数量 -->
    <select id="countByEmployeeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM stock_withdrawal
        WHERE employee_id = #{employeeId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <!-- 检查员工待审核申请 -->
    <select id="countPendingByEmployeeId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM stock_withdrawal
        WHERE employee_id = #{employeeId} AND status = 'PENDING'
    </select>

</mapper>
