# 主配置文件 - 通用配置
server:
  port: 8080
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}  # 默认使用开发环境，可通过环境变量覆盖
  web:
    resources:
      # 禁用默认静态资源处理，避免与API路径冲突
      add-mappings: false

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: org.example.company_management.entity
  configuration:
    map-underscore-to-camel-case: true

# PageHelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  auto-runtime-dialect: false
  close-conn: true
  # 禁用自动拦截，只对使用PageHelper.startPage()的查询进行分页
  params: count=countSql

# 自定义应用配置
app:
  upload:
    # 物理存储根路径
    path: ${UPLOAD_PATH:D:/Jobs/Project/company_management/ManaSysSer/uploads}  # 统一文件上传根路径，可通过环境变量覆盖
    # URL访问前缀
    url-prefix: ${FILE_ACCESS_URL_PREFIX:/uploads}  # 附件访问URL前缀
    base-url: ${APP_BASE_URL:http://localhost:8080}  # 应用基础URL

# JWT配置
jwt:
  secret: ${JWT_SECRET:company_management_system}
  expiration: ${JWT_EXPIRATION:86400000}
