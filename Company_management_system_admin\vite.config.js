import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
	// 加载环境变量
	const env = loadEnv(mode, process.cwd(), '')

	// 根据环境确定后端地址
	const getApiTarget = () => {
		if (mode === 'development') {
			return env.VITE_API_BASE_URL || 'http://localhost:8080'
		}
		// 生产环境下，如果是通过代理访问，仍然使用localhost
		return 'http://localhost:8080'
	}

	return {
		plugins: [
			vue(),
			// Element Plus 按需引入配置
			AutoImport({
				resolvers: [ElementPlusResolver()],
				imports: ['vue'],
				dts: true,
				eslintrc: {
					enabled: false
				}
			}),
			Components({
				resolvers: [ElementPlusResolver({
					// 自动引入样式
					importStyle: true
				})],
				dts: true
			}),
		],
		resolve: {
			alias: {
				'@': path.resolve(__dirname, 'src'),
			},
		},
		server: {
			// host: '0.0.0.0', // 新增此行，使服务监听所有网络接口
			port: 5173,
			proxy: {
				'/api': {
					target: getApiTarget(),
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/api/, ''),
				},
				// 代理静态资源请求到后端
				'/images': {
					target: getApiTarget(),
					changeOrigin: true,
				},
			},
		},
		build: {
			// 生产环境独有配置
			minify: 'terser', // 使用terser进行代码压缩
			terserOptions: {
			  compress: {
				drop_console: true, // 移除所有console.log
				drop_debugger: true // 移除debugger
			  }
			},
			// 代码分割配置
			rollupOptions: {
				output: {
					// 手动分割代码块
					manualChunks: {
						// 将Vue相关库分离
						'vue-vendor': ['vue', 'vue-router', 'pinia'],
						// 将图表库分离
						'chart-vendor': ['echarts'],
						// 将工具库分离
						'utils-vendor': ['axios']
						// Element Plus 将通过按需引入自动优化，无需手动配置
					},
					// 为每个chunk生成独立的CSS文件
					assetFileNames: (assetInfo) => {
						const fileName = assetInfo.fileName || assetInfo.name
						const info = fileName.split('.')
						const extType = info[info.length - 1]
						if (/\.(css)$/.test(fileName)) {
							return `css/[name]-[hash].${extType}`
						}
						if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(fileName)) {
							return `images/[name]-[hash].${extType}`
						}
						return `assets/[name]-[hash].${extType}`
					},
					// 为JS文件生成更好的文件名
					chunkFileNames: 'js/[name]-[hash].js',
					entryFileNames: 'js/[name]-[hash].js'
				}
			},
			// 设置chunk大小警告限制
			chunkSizeWarningLimit: 800,
			// 启用源码映射（可选，生产环境可关闭）
			sourcemap: false
		},
		// 定义全局常量
		define: {
			__APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
		}
	}
})
