package org.example.company_management.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * {{CHENGQI: 图片上传工具类}}
 * {{CHENGQI: 任务ID: P4-LD-003}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 描述: 通用图片上传工具类，支持文件验证、路径生成、存储管理}}
 */
@Slf4j
@Component
public class ImageUploadUtil {

    /**
     * 支持的图片格式
     */
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "webp");

    /**
     * 支持的MIME类型
     */
    private static final Set<String> ALLOWED_MIME_TYPES = Set.of(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    /**
     * 单个文件最大大小（10MB）
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 图片存储基础路径 - 从配置文件读取
     */
    private static String baseUploadPath;

    @Value("${app.upload.path:./uploads}")
    public void setBaseUploadPath(String path) {
        baseUploadPath = path;
        log.info("图片上传路径配置为: {}", baseUploadPath);
    }

    /**
     * 推广图片子目录
     */
    private static final String PROMOTION_SUB_PATH = "promotions/images";

    /**
     * 验证上传的文件是否为有效图片
     *
     * @param file 上传的文件
     * @return 验证结果
     */
    public static ImageValidationResult validateImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return ImageValidationResult.error("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return ImageValidationResult.error("文件大小不能超过10MB");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return ImageValidationResult.error("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            return ImageValidationResult.error("不支持的文件格式，仅支持：" + String.join(", ", ALLOWED_EXTENSIONS));
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            return ImageValidationResult.error("不支持的文件类型");
        }

        return ImageValidationResult.success();
    }

    /**
     * 上传单个图片文件
     *
     * @param file 上传的文件
     * @return 上传结果，包含文件访问URL
     */
    public static ImageUploadResult uploadImage(MultipartFile file) {
        // 验证文件
        ImageValidationResult validationResult = validateImage(file);
        if (!validationResult.isValid()) {
            return ImageUploadResult.error(validationResult.getErrorMessage());
        }

        try {
            // 生成文件存储路径
            String relativePath = generateRelativePath(file.getOriginalFilename());
            String fullPath = getFullStoragePath(relativePath);

            // 图片上传处理

            // 确保目录存在
            createDirectoriesIfNotExists(fullPath);

            // 保存文件
            Path targetPath = Paths.get(fullPath);
            Files.copy(file.getInputStream(), targetPath);

            // 生成访问URL
            String accessUrl = generateAccessUrl(relativePath);

            log.info("图片上传成功: {}", file.getOriginalFilename());
            return ImageUploadResult.success(accessUrl, relativePath);

        } catch (IOException e) {
            log.error("图片上传失败: {}", file.getOriginalFilename(), e);
            return ImageUploadResult.error("文件保存失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传图片文件
     *
     * @param files 上传的文件列表
     * @return 批量上传结果
     */
    public static BatchImageUploadResult uploadImages(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            return BatchImageUploadResult.error("文件列表不能为空");
        }

        if (files.size() > 9) {
            return BatchImageUploadResult.error("最多只能上传9张图片");
        }

        List<String> successUrls = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            ImageUploadResult result = uploadImage(file);

            if (result.isSuccess()) {
                successUrls.add(result.getAccessUrl());
            } else {
                errorMessages.add("第" + (i + 1) + "个文件上传失败: " + result.getErrorMessage());
            }
        }

        return new BatchImageUploadResult(successUrls, errorMessages);
    }

    /**
     * 删除图片文件
     *
     * @param relativePath 相对路径
     * @return 是否删除成功
     */
    public static boolean deleteImage(String relativePath) {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return false;
        }

        try {
            String fullPath = getFullStoragePath(relativePath);
            Path path = Paths.get(fullPath);
            boolean deleted = Files.deleteIfExists(path);

            if (!deleted) {
                log.warn("图片文件不存在: {}", relativePath);
            }

            return deleted;
        } catch (IOException e) {
            log.error("图片删除失败: {}", relativePath, e);
            return false;
        }
    }

    /**
     * 生成相对存储路径
     * 格式：promotions/images/年/月/日/时间戳_UUID.扩展名
     */
    private static String generateRelativePath(String originalFilename) {
        LocalDateTime now = LocalDateTime.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        String extension = getFileExtension(originalFilename);
        String filename = timestamp + "_" + uuid + "." + extension;

        return PROMOTION_SUB_PATH + "/" + datePath + "/" + filename;
    }

    /**
     * 获取完整存储路径
     * 支持Windows和Linux路径格式
     */
    private static String getFullStoragePath(String relativePath) {
        // 使用File.separator确保路径分隔符正确
        String separator = System.getProperty("file.separator");
        String normalizedRelativePath = relativePath.replace("/", separator);

        if (baseUploadPath.endsWith(separator)) {
            return baseUploadPath + normalizedRelativePath;
        } else {
            return baseUploadPath + separator + normalizedRelativePath;
        }
    }

    /**
     * 生成访问URL
     */
    private static String generateAccessUrl(String relativePath) {
        return "/images/" + relativePath;
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 创建目录（如果不存在）
     */
    private static void createDirectoriesIfNotExists(String fullPath) throws IOException {
        Path path = Paths.get(fullPath);
        Path parentDir = path.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
    }



    /**
     * 获取当前配置的上传路径信息（用于调试）
     */
    public static String getUploadPathInfo() {
        String basePath = baseUploadPath != null ? baseUploadPath : "未初始化";
        String absolutePath;
        try {
            absolutePath = basePath.equals("未初始化") ? "未初始化" : Paths.get(basePath).toAbsolutePath().toString();
        } catch (Exception e) {
            absolutePath = "无法获取绝对路径: " + e.getMessage();
        }

        return String.format("配置路径: %s, 绝对路径: %s, 工作目录: %s, 活动Profile: %s",
                basePath, absolutePath, System.getProperty("user.dir"), System.getProperty("spring.profiles.active", "default"));
    }

    // ==================== 内部结果类 ====================

    /**
     * 图片验证结果
     */
    public static class ImageValidationResult {
        private final boolean valid;
        private final String errorMessage;

        private ImageValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public static ImageValidationResult success() {
            return new ImageValidationResult(true, null);
        }

        public static ImageValidationResult error(String message) {
            return new ImageValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 单个图片上传结果
     */
    public static class ImageUploadResult {
        private final boolean success;
        private final String accessUrl;
        private final String relativePath;
        private final String errorMessage;

        private ImageUploadResult(boolean success, String accessUrl, String relativePath, String errorMessage) {
            this.success = success;
            this.accessUrl = accessUrl;
            this.relativePath = relativePath;
            this.errorMessage = errorMessage;
        }

        public static ImageUploadResult success(String accessUrl, String relativePath) {
            return new ImageUploadResult(true, accessUrl, relativePath, null);
        }

        public static ImageUploadResult error(String message) {
            return new ImageUploadResult(false, null, null, message);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAccessUrl() {
            return accessUrl;
        }

        public String getRelativePath() {
            return relativePath;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 批量图片上传结果
     */
    public static class BatchImageUploadResult {
        private final List<String> successUrls;
        private final List<String> errorMessages;

        public BatchImageUploadResult(List<String> successUrls, List<String> errorMessages) {
            this.successUrls = successUrls != null ? successUrls : new ArrayList<>();
            this.errorMessages = errorMessages != null ? errorMessages : new ArrayList<>();
        }

        public static BatchImageUploadResult error(String message) {
            return new BatchImageUploadResult(new ArrayList<>(), List.of(message));
        }

        public boolean isSuccess() {
            return errorMessages.isEmpty();
        }

        public boolean hasPartialSuccess() {
            return !successUrls.isEmpty() && !errorMessages.isEmpty();
        }

        public List<String> getSuccessUrls() {
            return successUrls;
        }

        public List<String> getErrorMessages() {
            return errorMessages;
        }

        public int getSuccessCount() {
            return successUrls.size();
        }

        public int getErrorCount() {
            return errorMessages.size();
        }
    }
}

// {{CHENGQI: 图片上传工具类创建完成}}
