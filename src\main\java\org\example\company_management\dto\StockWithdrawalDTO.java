package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 股票提现申请DTO
 * 用于API响应数据传输
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockWithdrawalDTO {
    
    /**
     * 提现申请ID
     */
    private Long id;
    
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 员工部门名称
     */
    private String departmentName;

    /**
     * 员工手机号
     */
    private String employeePhone;
    
    /**
     * 提现股票数量
     */
    private Integer quantity;
    
    /**
     * 申请时股票单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 提现总金额
     */
    private BigDecimal totalAmount;

    /**
     * 申请状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝
     */
    private String status;
    
    /**
     * 状态描述
     */
    private String statusDescription;
    
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;
    
    /**
     * 审核人ID
     */
    private Integer auditorId;
    
    /**
     * 审核人姓名
     */
    private String auditorName;
    
    /**
     * 拒绝理由
     */
    private String rejectReason;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "";
        }
        switch (status) {
            case "PENDING":
                return "待审核";
            case "APPROVED":
                return "已批准";
            case "REJECTED":
                return "已拒绝";
            default:
                return status;
        }
    }
}
