package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

/**
 * 股票提现申请提交DTO
 * 用于接收前端提现申请数据
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockWithdrawalSubmitDTO {
    
    /**
     * 员工ID（从ThreadLocal获取，前端不需要传递）
     */
    private Integer employeeId;
    
    /**
     * 提现股票数量
     */
    @NotNull(message = "提现数量不能为空", groups = {ValidationGroups.Add.class})
    @Min(value = 1, message = "提现数量必须大于0", groups = {ValidationGroups.Add.class})
    @Max(value = 999999, message = "提现数量不能超过999999", groups = {ValidationGroups.Add.class})
    private Integer quantity;
    
    /**
     * 备注
     */
    @Size(max = 255, message = "备注不能超过255字符", groups = {ValidationGroups.Add.class})
    private String remark;
}
