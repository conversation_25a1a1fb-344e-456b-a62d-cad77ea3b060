package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 员工股票汇总实体类
 * 采用汇总+明细混合模式，提供高性能的股票数量查询
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeStockSummary {
    
    /**
     * 员工ID（主键）
     */
    private Integer employeeId;
    
    /**
     * 总股票数量
     */
    private Integer totalQuantity;
    
    /**
     * 已解禁数量
     */
    private Integer unlockedQuantity;
    
    /**
     * 已提现数量
     */
    private Integer withdrawnQuantity;
    
    /**
     * 可提现数量（计算列：unlocked_quantity - withdrawn_quantity）
     */
    private Integer availableQuantity;
    
    /**
     * 最后计算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCalculatedTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 扩展字段（用于关联查询）
    
    /**
     * 员工姓名（关联查询）
     */
    private String employeeName;
    
    /**
     * 部门名称（关联查询）
     */
    private String departmentName;
    
    /**
     * 未解禁数量（计算字段）
     */
    public Integer getLockedQuantity() {
        if (totalQuantity == null || unlockedQuantity == null) {
            return 0;
        }
        return totalQuantity - unlockedQuantity;
    }
    
    /**
     * 检查数据一致性
     */
    public boolean isDataConsistent() {
        if (totalQuantity == null || unlockedQuantity == null || withdrawnQuantity == null) {
            return false;
        }
        
        // 已解禁数量不能超过总数量
        if (unlockedQuantity > totalQuantity) {
            return false;
        }
        
        // 已提现数量不能超过已解禁数量
        if (withdrawnQuantity > unlockedQuantity) {
            return false;
        }
        
        // 可提现数量应该等于已解禁数量减去已提现数量
        if (availableQuantity != null && !availableQuantity.equals(unlockedQuantity - withdrawnQuantity)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取数据一致性检查结果描述
     */
    public String getConsistencyCheckResult() {
        if (isDataConsistent()) {
            return "数据一致";
        }
        
        StringBuilder sb = new StringBuilder("数据不一致：");
        
        if (totalQuantity == null || unlockedQuantity == null || withdrawnQuantity == null) {
            sb.append("存在空值; ");
        }
        
        if (unlockedQuantity != null && totalQuantity != null && unlockedQuantity > totalQuantity) {
            sb.append("已解禁数量超过总数量; ");
        }
        
        if (withdrawnQuantity != null && unlockedQuantity != null && withdrawnQuantity > unlockedQuantity) {
            sb.append("已提现数量超过已解禁数量; ");
        }
        
        if (availableQuantity != null && unlockedQuantity != null && withdrawnQuantity != null 
            && !availableQuantity.equals(unlockedQuantity - withdrawnQuantity)) {
            sb.append("可提现数量计算错误; ");
        }
        
        return sb.toString();
    }
}
