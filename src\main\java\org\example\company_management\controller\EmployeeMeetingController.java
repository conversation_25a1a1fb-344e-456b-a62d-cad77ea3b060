package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.MeetingDTO;
import org.example.company_management.dto.MeetingSummaryDTO;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.Meeting;
import org.example.company_management.entity.MeetingSummary;
import org.example.company_management.service.MeetingService;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.utils.ThreadLocalUtil;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 员工会议控制器（用户端）
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@RestController
@RequestMapping("/employee/meeting")
public class EmployeeMeetingController {
    
    @Autowired
    private MeetingService meetingService;

    @Autowired
    private EmployeeService employeeService;
    
    /**
     * 分页查询员工相关的会议列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param status 会议状态（可选）
     * @param title 会议主题（可选）
     * @return 分页结果
     */
    @GetMapping
    public Result<PageResult<Meeting>> getEmployeeMeetingPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String title) {
        // 获取当前登录员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            return Result.error("未获取到当前登录用户信息");
        }

        PageResult<Meeting> pageResult = meetingService.getEmployeeMeetingPage(
            pageNum, pageSize, currentEmployee.getEmployeeId(), status, title);
        return Result.success(pageResult);
    }
    
    /**
     * 获取会议详情
     * 
     * @param id 会议ID
     * @return 会议信息
     */
    @GetMapping("/{id}")
    public Result<Meeting> getMeetingById(@PathVariable Long id) {
        try {
            Meeting meeting = meetingService.getMeetingById(id);
            return Result.success(meeting);
        } catch (Exception e) {
            log.error("获取会议详情失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建会议（员工端）
     *
     * @param meetingDTO 会议信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Void> createMeeting(@Validated(ValidationGroups.Add.class) @RequestBody MeetingDTO meetingDTO) {
        try {
            // 获取当前登录员工信息
            Employee currentEmployee = getCurrentEmployee();
            if (currentEmployee == null) {
                return Result.error("未获取到当前登录用户信息");
            }

            // 自动设置会议负责人为当前登录员工
            meetingDTO.setResponsibleId(currentEmployee.getEmployeeId());

            // 调用员工创建会议的服务方法
            meetingService.createEmployeeMeeting(meetingDTO);
            return Result.success();
        } catch (Exception e) {
            log.error("创建会议失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新会议（员工端）
     *
     * @param id 会议ID
     * @param meetingDTO 会议信息
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public Result<Void> updateMeeting(@PathVariable Long id, @Validated(ValidationGroups.Update.class) @RequestBody MeetingDTO meetingDTO) {
        try {
            // 获取当前登录员工信息
            Employee currentEmployee = getCurrentEmployee();
            if (currentEmployee == null) {
                return Result.error("未获取到当前登录用户信息");
            }

            // 设置会议ID
            meetingDTO.setId(id);

            // 调用员工更新会议的服务方法
            meetingService.updateEmployeeMeeting(meetingDTO, currentEmployee.getEmployeeId());
            return Result.success();
        } catch (Exception e) {
            log.error("更新会议失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加会议总结
     * 
     * @param summaryDTO 总结信息
     * @return 操作结果
     */
    @PostMapping("/summary")
    public Result<Void> addMeetingSummary(@Validated(ValidationGroups.Add.class) @RequestBody MeetingSummaryDTO summaryDTO) {
        meetingService.addMeetingSummary(summaryDTO);
        return Result.success();
    }
    
    /**
     * 更新会议总结
     * 
     * @param summaryDTO 总结信息
     * @return 操作结果
     */
    @PutMapping("/summary")
    public Result<Void> updateMeetingSummary(@Validated(ValidationGroups.Update.class) @RequestBody MeetingSummaryDTO summaryDTO) {
        meetingService.updateMeetingSummary(summaryDTO);
        return Result.success();
    }
    
    /**
     * 删除会议总结
     * 
     * @param id 总结ID
     * @return 操作结果
     */
    @DeleteMapping("/summary/{id}")
    public Result<Void> deleteMeetingSummary(@PathVariable Long id) {
        meetingService.deleteMeetingSummary(id);
        return Result.success();
    }
    
    /**
     * 获取当前员工对指定会议的总结
     * 
     * @param meetingId 会议ID
     * @return 会议总结
     */
    @GetMapping("/{meetingId}/summary")
    public Result<MeetingSummary> getMeetingSummary(@PathVariable Long meetingId) {
        // 获取当前登录员工信息
        Employee currentEmployee = getCurrentEmployee();
        if (currentEmployee == null) {
            return Result.error("未获取到当前登录用户信息");
        }

        MeetingSummary summary = meetingService.getMeetingSummaryByMeetingIdAndEmployeeId(
            meetingId, currentEmployee.getEmployeeId());
        return Result.success(summary);
    }

    /**
     * 获取当前登录员工信息
     *
     * @return 当前登录员工信息，如果未登录则返回null
     */
    private Employee getCurrentEmployee() {
        try {
            // 从ThreadLocal中获取员工信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return null;
            }

            // 获取员工ID
            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return null;
            }

            // 查询员工详细信息
            return employeeService.getById(employeeId);
        } catch (Exception e) {
            log.error("获取当前员工信息失败", e);
            return null;
        }
    }
}
