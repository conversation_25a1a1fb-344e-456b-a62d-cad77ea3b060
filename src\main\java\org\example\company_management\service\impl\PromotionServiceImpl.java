package org.example.company_management.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.PromotionDTO;
import org.example.company_management.entity.Department;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.Promotion;
import org.example.company_management.entity.PromotionAttachment;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.mapper.PromotionMapper;
import org.example.company_management.service.DepartmentService;
import org.example.company_management.service.PromotionAttachmentService;
import org.example.company_management.service.PromotionService;
import org.example.company_management.utils.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {{CHENGQI: 推广管理业务逻辑实现类}}
 * {{CHENGQI: 任务ID: P4-LD-007}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 描述: 推广管理业务逻辑实现，包含权限控制和业务流程}}
 */
@Slf4j
@Service
public class PromotionServiceImpl implements PromotionService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private PromotionMapper promotionMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private PromotionAttachmentService attachmentService;
    @Autowired
    private org.example.company_management.mapper.PromotionAttachmentMapper attachmentMapper;
    @Value("${app.upload.path}")
    private String uploadRootPath;
    @Value("${app.upload.url-prefix}")
    private String urlPrefix;

    @Override
    @Transactional
    public Result<PromotionDTO> createPromotion(PromotionDTO promotionDTO) {
        try {
            // 获取当前用户信息
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            Employee currentEmployee = employeeMapper.selectById(currentEmployeeId);
            if (currentEmployee == null) {
                return Result.error("用户信息不存在");
            }

            // 创建推广实体
            Promotion promotion = new Promotion();
            promotion.setTitle(promotionDTO.getTitle());

            // 处理内容和内容类型
            String content = promotionDTO.getContent();
            String contentType = StringUtils.hasText(promotionDTO.getContentType()) ?
                    promotionDTO.getContentType() : "TEXT";

            // 如果是HTML内容，进行清理
            if ("HTML".equals(contentType)) {
                content = ContentProcessingUtils.sanitizeHtmlContent(content);
            }

            promotion.setContent(content);
            promotion.setContentType(contentType);

            // 生成内容摘要
            String contentSummary;
            if ("HTML".equals(contentType)) {
                contentSummary = ContentProcessingUtils.extractTextSummary(content, 500);
            } else {
                contentSummary = content.length() > 500 ? content.substring(0, 500) + "..." : content;
            }
            promotion.setContentSummary(contentSummary);

            promotion.setAuthorId(currentEmployeeId);
            promotion.setStatus("待审核");
            promotion.setCreateTime(LocalDateTime.now());
            promotion.setUpdateTime(LocalDateTime.now());

            // 处理图片列表（保持向后兼容）
            if (promotionDTO.getImages() != null && !promotionDTO.getImages().isEmpty()) {
                try {
                    String imagesJson = objectMapper.writeValueAsString(promotionDTO.getImages());
                    promotion.setImages(imagesJson);
                } catch (JsonProcessingException e) {
                    log.error("图片列表序列化失败", e);
                    return Result.error("图片信息处理失败");
                }
            }

            // 注意：不再处理attachments字段，因为附件数据现在通过独立的附件表管理
            // 附件通过临时附件转换机制或直接上传到附件表中处理

            // 插入数据库
            int result = promotionMapper.insert(promotion);
            if (result > 0) {
                // 处理临时附件转换
                if (promotionDTO.getTempAttachmentPaths() != null && !promotionDTO.getTempAttachmentPaths().isEmpty()) {
                    Result<Void> convertResult = convertTempAttachments(promotion.getId(), promotionDTO.getTempAttachmentPaths());
                    if (!convertResult.isSuccess()) {
                        log.warn("临时附件转换失败，推广ID: {}, 错误: {}", promotion.getId(), convertResult.getMessage());
                    }
                }

                // 返回创建的推广信息
                PromotionDTO resultDTO = convertToDTO(promotion);
                resultDTO.setAuthorName(currentEmployee.getName());
                resultDTO.setDepartmentName(currentEmployee.getDepartment() != null ?
                        currentEmployee.getDepartment().getDepartmentName() : null);

                log.info("推广创建成功，ID: {}, 标题: {}", promotion.getId(), promotion.getTitle());
                return Result.success(resultDTO);
            } else {
                return Result.error("推广创建失败");
            }
        } catch (Exception e) {
            log.error("创建推广时发生异常", e);
            return Result.error("创建推广失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<PromotionDTO> updatePromotion(Long id, PromotionDTO promotionDTO) {
        try {
            // 检查推广是否存在
            Promotion existingPromotion = promotionMapper.selectById(id);
            if (existingPromotion == null) {
                return Result.error("推广不存在");
            }

            // 检查权限
            if (!hasPermission(id, "edit")) {
                return Result.error("权限不足，无法编辑该推广");
            }

            // 检查状态是否允许编辑
            String currentRole = getCurrentUserRole();
            Integer currentEmployeeId = getCurrentEmployeeId();
            boolean isAuthor = existingPromotion.getAuthorId().equals(currentEmployeeId);
            boolean isManager = "MANAGER".equalsIgnoreCase(currentRole);
            String originalStatus = existingPromotion.getStatus();
            boolean needsStatusReset = false;

            // 检查编辑权限和状态重置逻辑
            if (!"待审核".equals(originalStatus)) {
                // 对于已通过和已拒绝状态的推广，所有角色编辑时都需要重置状态为待审核
                if ("已通过".equals(originalStatus) || "已拒绝".equals(originalStatus)) {
                    needsStatusReset = true;
                    log.info("编辑已完成审核的推广，将重置状态为待审核，推广ID: {}, 原状态: {}, 编辑者角色: {}", id, originalStatus, currentRole);
                }

                // 权限检查
                if ("ADMIN".equalsIgnoreCase(currentRole)) {
                    // 管理员有所有权限，跳过权限检查
                } else if (isManager) {
                    // 部门负责人需要检查是否管理该推广作者所属部门
                    Employee author = employeeMapper.selectById(existingPromotion.getAuthorId());
                    if (author == null || author.getDepartmentId() == null) {
                        return Result.error("推广作者信息异常，无法编辑");
                    }

                    List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                    if (!managedDepartmentIds.contains(author.getDepartmentId())) {
                        return Result.error("您无权编辑其他部门的推广");
                    }
                } else if (isAuthor) {
                    // 普通员工只能编辑自己的推广
                    if ("审核中".equals(originalStatus)) {
                        // 审核中状态不允许普通员工编辑
                        return Result.error("推广正在审核中，无法编辑");
                    }
                } else {
                    // 既不是管理员，也不是部门负责人，也不是作者
                    return Result.error("权限不足，无法编辑该推广");
                }
            }

            // 更新推广信息
            Promotion promotion = new Promotion();
            promotion.setId(id);
            promotion.setTitle(promotionDTO.getTitle());

            // 处理内容和内容类型
            String content = promotionDTO.getContent();
            String contentType = StringUtils.hasText(promotionDTO.getContentType()) ?
                    promotionDTO.getContentType() : existingPromotion.getContentType();

            // 如果是HTML内容，进行清理
            if ("HTML".equals(contentType)) {
                content = ContentProcessingUtils.sanitizeHtmlContent(content);
            }

            promotion.setContent(content);
            promotion.setContentType(contentType);

            // 生成内容摘要
            String contentSummary;
            if ("HTML".equals(contentType)) {
                contentSummary = ContentProcessingUtils.extractTextSummary(content, 500);
            } else {
                contentSummary = content.length() > 500 ? content.substring(0, 500) + "..." : content;
            }
            promotion.setContentSummary(contentSummary);

            promotion.setUpdateTime(LocalDateTime.now());

            // 处理图片列表（保持向后兼容）
            if (promotionDTO.getImages() != null) {
                try {
                    String imagesJson = objectMapper.writeValueAsString(promotionDTO.getImages());
                    promotion.setImages(imagesJson);
                } catch (JsonProcessingException e) {
                    log.error("图片列表序列化失败", e);
                    return Result.error("图片信息处理失败");
                }
            }

            // 注意：不再处理attachments字段，因为附件数据现在通过独立的附件表管理
            // 前端传来的attachments仅用于显示，实际的附件管理通过PromotionAttachmentService处理

            // 如果不需要重置状态，部门负责人和管理员可以修改状态
            if (!needsStatusReset && (isManager || "ADMIN".equalsIgnoreCase(currentRole)) && promotionDTO.getStatus() != null) {
                promotion.setStatus(promotionDTO.getStatus());
            }

            // 更新数据库
            int result = promotionMapper.updateById(promotion);

            // 如果需要重置状态，使用专门的状态更新方法
            if (needsStatusReset && result > 0) {
                log.info("推广内容更新成功，开始重置状态为待审核，推广ID: {}", id);
                int statusResult = promotionMapper.updatePromotionStatus(id, "待审核", getCurrentEmployeeId(), LocalDateTime.now());
                if (statusResult <= 0) {
                    log.error("推广状态重置失败，推广ID: {}", id);
                    return Result.error("推广状态重置失败");
                }
                log.info("推广状态已重置为待审核，推广ID: {}", id);
            }
            if (result > 0) {
                // 返回更新后的推广信息
                Promotion updatedPromotion = promotionMapper.selectById(id);
                PromotionDTO resultDTO = convertToDTO(updatedPromotion);

                // 如果状态发生了重置，在返回结果中添加提示信息
                if (needsStatusReset) {
                    log.info("推广更新成功，状态已重置为待审核，ID: {}, 标题: {}, 原状态: {}",
                            id, promotion.getTitle(), originalStatus);
                    return Result.success("推广更新成功，状态已重置为待审核，需要重新提交审核", resultDTO);
                } else {
                    log.info("推广更新成功，ID: {}, 标题: {}", id, promotion.getTitle());
                    return Result.success(resultDTO);
                }
            } else {
                return Result.error("推广更新失败");
            }
        } catch (Exception e) {
            log.error("更新推广时发生异常，ID: {}", id, e);
            return Result.error("更新推广失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deletePromotion(Long id) {
        try {
            // 检查推广是否存在
            Promotion existingPromotion = promotionMapper.selectById(id);
            if (existingPromotion == null) {
                return Result.error("推广不存在");
            }

            // 检查权限
            if (!hasPermission(id, "delete")) {
                return Result.error("权限不足，无法删除该推广");
            }

            // 检查状态是否允许删除
            String currentRole = getCurrentUserRole();
            Integer currentEmployeeId = getCurrentEmployeeId();

            // 普通员工不能删除已通过的推广，但部门负责人可以
            if ("已通过".equals(existingPromotion.getStatus())) {
                boolean isManager = "MANAGER".equalsIgnoreCase(currentRole);
                boolean isAuthor = existingPromotion.getAuthorId().equals(currentEmployeeId);

                if (!isManager && !isAuthor) {
                    return Result.error("已通过的推广不能删除");
                }

                // 如果是部门负责人，检查是否管理该推广作者所属部门
                if (isManager && !isAuthor) {
                    // 获取推广作者的部门信息
                    Employee author = employeeMapper.selectById(existingPromotion.getAuthorId());
                    if (author == null || author.getDepartmentId() == null) {
                        return Result.error("推广作者信息异常，无法删除");
                    }

                    List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                    if (!managedDepartmentIds.contains(author.getDepartmentId())) {
                        return Result.error("您无权删除其他部门的推广");
                    }
                }
            }

            // 删除关联的图片文件
            if (existingPromotion.getImages() != null && !existingPromotion.getImages().trim().isEmpty()) {
                try {
                    List<String> imageUrls = objectMapper.readValue(existingPromotion.getImages(),
                            new TypeReference<List<String>>() {
                            });
                    for (String imageUrl : imageUrls) {
                        deleteImageFile(imageUrl);
                    }
                } catch (JsonProcessingException e) {
                    log.warn("解析图片列表失败，跳过图片删除，推广ID: {}", id, e);
                }
            }

            // 删除关联的附件文件
            Result<Void> deleteAttachmentsResult = attachmentService.deleteAttachmentsByPromotionId(id);
            if (!deleteAttachmentsResult.isSuccess()) {
                log.warn("删除推广附件失败，推广ID: {}, 错误信息: {}", id, deleteAttachmentsResult.getMessage());
            }

            // 删除推广记录
            int result = promotionMapper.deleteById(id);
            if (result > 0) {
                log.info("推广删除成功，ID: {}", id);
                return Result.success();
            } else {
                return Result.error("推广删除失败");
            }
        } catch (Exception e) {
            log.error("删除推广时发生异常，ID: {}", id, e);
            return Result.error("删除推广失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PromotionDTO> getPromotionById(Long id) {
        try {
            Promotion promotion = promotionMapper.selectById(id);
            if (promotion == null) {
                return Result.error("推广不存在");
            }

            // 检查查看权限
            if (!hasPermission(id, "view")) {
                return Result.error("权限不足，无法查看该推广");
            }

            PromotionDTO promotionDTO = convertToDTO(promotion);
            return Result.success(promotionDTO);
        } catch (Exception e) {
            log.error("查询推广详情时发生异常，ID: {}", id, e);
            return Result.error("查询推广详情失败: " + e.getMessage());
        }
    }


    // 获取当前用户ID的辅助方法
    private Integer getCurrentEmployeeId() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("employeeId")) {
            Object idObject = employeeInfo.get("employeeId");
            if (idObject instanceof Integer) {
                return (Integer) idObject;
            } else if (idObject instanceof Number) {
                return ((Number) idObject).intValue();
            }
        }
        return null;
    }

    // 获取当前用户角色的辅助方法
    private String getCurrentUserRole() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("role")) {
            return (String) employeeInfo.get("role");
        }
        return null;
    }

    // 获取管理的部门ID列表的辅助方法
    private List<Integer> getManagedDepartmentIds() {
        Integer currentEmployeeId = getCurrentEmployeeId();

        if (currentEmployeeId == null) {
            log.warn("当前用户ID为空，返回空列表");
            return new ArrayList<>();
        }

        Employee currentEmployee = employeeMapper.selectById(currentEmployeeId);
        if (currentEmployee == null) {
            log.warn("无法找到用户信息，用户ID: {}", currentEmployeeId);
            return new ArrayList<>();
        }

        // 只有部门负责人才有管理权限
        String currentRole = getCurrentUserRole();

        if (!"MANAGER".equalsIgnoreCase(currentRole)) {
            return new ArrayList<>();
        }

        // 获取当前部门及其子部门ID列表
        List<Integer> departmentIds = new ArrayList<>();

        // 1. 首先添加用户自己所在的部门ID（确保能看到自己部门的推广）
        if (currentEmployee.getDepartmentId() != null) {
            departmentIds.add(currentEmployee.getDepartmentId());

            // 同时添加自己所在部门的所有子部门
            List<Integer> myDeptChildIds = getAllChildDepartmentIds(currentEmployee.getDepartmentId());
            for (Integer childId : myDeptChildIds) {
                if (!departmentIds.contains(childId)) {
                    departmentIds.add(childId);
                }
            }
        } else {
            log.warn("用户没有所在部门信息");
        }

        // 2. 获取用户负责的部门列表（使用新的多负责人功能）
        List<Integer> managedDepartmentIds = departmentService.getManagedDepartmentIds(currentEmployeeId);
        List<Department> leadingDepartments = new ArrayList<>();
        for (Integer deptId : managedDepartmentIds) {
            Department dept = departmentService.getById(deptId);
            if (dept != null) {
                leadingDepartments.add(dept);
            }
        }

        if (leadingDepartments != null && !leadingDepartments.isEmpty()) {
            for (Department dept : leadingDepartments) {
                // 添加负责的部门ID（如果不是自己所在部门的话）
                if (!departmentIds.contains(dept.getDepartmentId())) {
                    departmentIds.add(dept.getDepartmentId());
                }

                // 递归获取该部门的所有子部门ID
                List<Integer> childDepartmentIds = getAllChildDepartmentIds(dept.getDepartmentId());

                for (Integer childId : childDepartmentIds) {
                    if (!departmentIds.contains(childId)) {
                        departmentIds.add(childId);
                    }
                }
            }
        }

        return departmentIds;
    }

    // 递归获取部门的所有子部门ID
    private List<Integer> getAllChildDepartmentIds(Integer departmentId) {
        List<Integer> childIds = new ArrayList<>();
        List<Department> subDepartments = departmentService.getSubDepartments(departmentId);

        if (subDepartments != null && !subDepartments.isEmpty()) {
            for (Department subDept : subDepartments) {
                childIds.add(subDept.getDepartmentId());
                // 递归获取子部门的子部门
                List<Integer> grandChildIds = getAllChildDepartmentIds(subDept.getDepartmentId());
                childIds.addAll(grandChildIds);
            }
        }

        return childIds;
    }

    // 实体转DTO的辅助方法
    private PromotionDTO convertToDTO(Promotion promotion) {
        PromotionDTO dto = new PromotionDTO();
        BeanUtils.copyProperties(promotion, dto);

        // 处理图片列表（保持向后兼容）
        if (promotion.getImages() != null && !promotion.getImages().trim().isEmpty()) {
            try {
                List<String> imageUrls = objectMapper.readValue(promotion.getImages(),
                        new TypeReference<List<String>>() {
                        });
                dto.setImages(imageUrls);
            } catch (JsonProcessingException e) {
                log.warn("解析图片列表失败，推广ID: {}", promotion.getId(), e);
                dto.setImages(new ArrayList<>());
            }
        } else {
            dto.setImages(new ArrayList<>());
        }

        // 处理附件列表 - 直接从数据库查询完整的附件信息
        try {
            if (promotion.getId() != null) {
                // 直接通过Mapper查询附件信息
                List<PromotionAttachment> attachments = attachmentMapper.selectByPromotionId(promotion.getId());
                if (attachments != null && !attachments.isEmpty()) {
                    // 转换为Object列表以保持兼容性
                    List<Object> attachmentObjects = new ArrayList<>();
                    for (PromotionAttachment attachment : attachments) {
                        attachmentObjects.add(attachment);
                    }
                    dto.setAttachments(attachmentObjects);
                } else {
                    dto.setAttachments(new ArrayList<>());
                }
            } else {
                dto.setAttachments(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("查询附件列表失败，推广ID: {}", promotion.getId(), e);
            dto.setAttachments(new ArrayList<>());
        }

        return dto;
    }

    // 删除图片文件的辅助方法
    private void deleteImageFile(String imageUrl) {
        if (imageUrl != null && imageUrl.startsWith("/images/")) {
            String relativePath = imageUrl.substring("/images/".length());
            ImageUploadUtil.deleteImage(relativePath);
        }
    }

    @Override
    public Result<PageResult<PromotionDTO>> getMyPromotions(Integer pageNum, Integer pageSize, String status, String titleKeyword, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
        try {
            // 设置分页参数
            PageHelper.startPage(pageNum, pageSize);

            // 获取当前用户ID
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 执行查询
            List<Promotion> promotions = promotionMapper.selectPage(
                    currentEmployeeId, null, status, titleKeyword, createTimeStart, createTimeEnd
            );

            // 转换为DTO
            List<PromotionDTO> promotionDTOs = promotions.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageInfo<Promotion> pageInfo = new PageInfo<>(promotions);
            PageResult<PromotionDTO> pageResult = new PageResult<>(
                    pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), promotionDTOs
            );

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("查询我的推广列表时发生异常", e);
            return Result.error("查询推广列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<PromotionDTO>> getApprovedPromotions(Integer pageNum, Integer pageSize, PromotionDTO promotionDTO) {
        try {
            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;

            // 先查询总记录数
            int total = promotionMapper.countApprovedPromotions(
                    promotionDTO.getDepartmentIds(),
                    promotionDTO.getTitleKeyword(),
                    promotionDTO.getCreateTimeStart(),
                    promotionDTO.getCreateTimeEnd()
            );

            // 查询已通过的推广（带分页）
            List<Promotion> promotions = promotionMapper.selectApprovedPromotions(
                    promotionDTO.getDepartmentIds(),
                    promotionDTO.getTitleKeyword(),
                    promotionDTO.getCreateTimeStart(),
                    promotionDTO.getCreateTimeEnd(),
                    offset,
                    pageSize
            );

            // 转换为DTO
            List<PromotionDTO> promotionDTOs = promotions.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<PromotionDTO> pageResult = new PageResult<>(
                    pageNum, pageSize, total, promotionDTOs
            );

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("查询已通过推广列表时发生异常", e);
            return Result.error("查询推广列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<PromotionDTO>> getPendingAuditPromotions(Integer pageNum, Integer pageSize, PromotionDTO queryDTO) {
        try {
            // 检查是否有审核权限
            String currentRole = getCurrentUserRole();

            // 支持大小写不敏感的角色检查
            if (currentRole == null ||
                    (!"MANAGER".equalsIgnoreCase(currentRole) && !"ADMIN".equalsIgnoreCase(currentRole))) {
                return Result.error("权限不足，无法查看待审核推广");
            }

            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;

            // 获取管理的部门ID列表
            List<Integer> managedDepartmentIds = "ADMIN".equalsIgnoreCase(currentRole) ? null : getManagedDepartmentIds();

            // 如果查询条件中指定了部门ID，需要验证权限并添加用户自己所在的部门
            List<Integer> queryDepartmentIds = null;
            if (queryDTO != null && queryDTO.getDepartmentIds() != null && !queryDTO.getDepartmentIds().isEmpty()) {
                queryDepartmentIds = new ArrayList<>(queryDTO.getDepartmentIds());

                // 非管理员需要验证部门权限
                if (!"ADMIN".equalsIgnoreCase(currentRole)) {
                    for (Integer deptId : queryDTO.getDepartmentIds()) {
                        if (!managedDepartmentIds.contains(deptId)) {
                            return Result.error("权限不足，无法查看指定部门的推广");
                        }
                    }
                }

                // 无论什么角色，都添加用户自己所在的部门ID（如果不在查询列表中）
                Integer currentEmployeeId = getCurrentEmployeeId();
                if (currentEmployeeId != null) {
                    Employee currentEmployee = employeeMapper.selectById(currentEmployeeId);
                    if (currentEmployee != null && currentEmployee.getDepartmentId() != null) {
                        if (!queryDepartmentIds.contains(currentEmployee.getDepartmentId())) {
                            queryDepartmentIds.add(currentEmployee.getDepartmentId());
                        }
                    }
                }
            } else {
                // 如果没有指定部门，使用用户管理的部门
                queryDepartmentIds = managedDepartmentIds;
            }

            // 先查询总记录数
            int total = promotionMapper.countPendingAuditByConditions(
                    queryDepartmentIds,
                    queryDTO != null ? queryDTO.getTitleKeyword() : null,
                    queryDTO != null ? queryDTO.getStatusFilter() : null,
                    queryDTO != null ? queryDTO.getCreateTimeStart() : null,
                    queryDTO != null ? queryDTO.getCreateTimeEnd() : null
            );

            // 查询待审核的推广（带分页）
            List<Promotion> promotions = promotionMapper.selectPendingAuditByConditions(
                    queryDepartmentIds,
                    queryDTO != null ? queryDTO.getTitleKeyword() : null,
                    queryDTO != null ? queryDTO.getStatusFilter() : null,
                    queryDTO != null ? queryDTO.getCreateTimeStart() : null,
                    queryDTO != null ? queryDTO.getCreateTimeEnd() : null,
                    offset,
                    pageSize
            );

            // 转换为DTO
            List<PromotionDTO> promotionDTOs = promotions.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<PromotionDTO> pageResult = new PageResult<>(
                    pageNum, pageSize, total, promotionDTOs
            );

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("查询待审核推广列表时发生异常", e);
            return Result.error("查询待审核推广列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> submitForAudit(Long id) {
        try {
            // 检查推广是否存在
            Promotion promotion = promotionMapper.selectById(id);
            if (promotion == null) {
                return Result.error("推广不存在");
            }

            // 检查权限
            if (!hasPermission(id, "edit")) {
                return Result.error("权限不足，无法提交该推广");
            }

            // 检查状态
            if (!"待审核".equals(promotion.getStatus())) {
                return Result.error("只能提交待审核状态的推广");
            }

            // 提交审核
            int result = promotionMapper.updatePromotionStatus(id, "审核中", getCurrentEmployeeId(), LocalDateTime.now());
            if (result > 0) {
                log.info("推广提交审核成功，ID: {}", id);
                return Result.success();
            } else {
                return Result.error("提交审核失败");
            }
        } catch (Exception e) {
            log.error("提交推广审核时发生异常，ID: {}", id, e);
            return Result.error("提交审核失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updatePromotionStatus(Long id, String status) {
        try {
            // 检查推广是否存在
            Promotion promotion = promotionMapper.selectById(id);
            if (promotion == null) {
                return Result.error("推广不存在");
            }

            // 验证状态值
            if (!isValidStatus(status)) {
                return Result.error("无效的状态值");
            }

            // 检查权限
            String currentRole = getCurrentUserRole();
            Integer currentEmployeeId = getCurrentEmployeeId();

            if (currentEmployeeId == null) {
                return Result.error("用户未登录或登录信息已失效");
            }

            // 权限检查逻辑
            if (!hasStatusUpdatePermission(promotion, status, currentRole, currentEmployeeId)) {
                return Result.error("权限不足，无法修改该推广状态");
            }

            // 状态转换验证
            if (!isValidStatusTransition(promotion.getStatus(), status)) {
                return Result.error("无效的状态转换：从 " + promotion.getStatus() + " 到 " + status);
            }

            // 更新状态
            int result = promotionMapper.updatePromotionStatus(id, status, currentEmployeeId, LocalDateTime.now());
            if (result > 0) {
                log.info("推广状态修改成功，ID: {}, 原状态: {}, 新状态: {}, 操作人: {}",
                        id, promotion.getStatus(), status, currentEmployeeId);
                return Result.success();
            } else {
                return Result.error("状态修改失败");
            }
        } catch (Exception e) {
            log.error("修改推广状态时发生异常，ID: {}, 状态: {}", id, status, e);
            return Result.error("状态修改失败: " + e.getMessage());
        }
    }

    // 验证状态值是否有效
    private boolean isValidStatus(String status) {
        return status != null && ("待审核".equals(status) || "审核中".equals(status) ||
                "已通过".equals(status) || "已拒绝".equals(status));
    }

    // 检查状态修改权限
    private boolean hasStatusUpdatePermission(Promotion promotion, String status, String currentRole, Integer currentEmployeeId) {
        // 管理员拥有所有权限
        if ("ADMIN".equalsIgnoreCase(currentRole)) {
            return true;
        }

        // 作者可以进行的状态转换
        if (promotion.getAuthorId().equals(currentEmployeeId)) {
            // 作者可以：待审核->审核中，审核中->待审核
            return ("待审核".equals(promotion.getStatus()) && "审核中".equals(status)) ||
                    ("审核中".equals(promotion.getStatus()) && "待审核".equals(status));
        }

        // 部门主管可以进行审核操作
        if ("MANAGER".equalsIgnoreCase(currentRole)) {
            // 通过推广作者的部门ID检查权限
            Employee author = employeeMapper.selectById(promotion.getAuthorId());
            if (author != null && author.getDepartmentId() != null) {
                List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                if (managedDepartmentIds.contains(author.getDepartmentId())) {
                    // 部门主管可以：审核中->已通过，审核中->已拒绝
                    return "审核中".equals(promotion.getStatus()) &&
                            ("已通过".equals(status) || "已拒绝".equals(status));
                }
            }
        }

        return false;
    }

    // 验证状态转换是否合法
    private boolean isValidStatusTransition(String fromStatus, String toStatus) {
        if (fromStatus.equals(toStatus)) {
            return false; // 不允许转换到相同状态
        }

        switch (fromStatus) {
            case "待审核":
                return "审核中".equals(toStatus);
            case "审核中":
                return "待审核".equals(toStatus) || "已通过".equals(toStatus) || "已拒绝".equals(toStatus);
            case "已通过":
            case "已拒绝":
                // 已完成审核的推广一般不允许再次修改状态，除非是管理员
                return false;
            default:
                return false;
        }
    }

    @Override
    @Transactional
    public Result<Void> auditPromotion(Long id, PromotionDTO promotionDTO) {
        try {
            // 检查推广是否存在
            Promotion promotion = promotionMapper.selectById(id);
            if (promotion == null) {
                return Result.error("推广不存在");
            }

            // 检查审核权限
            if (!hasPermission(id, "audit")) {
                return Result.error("权限不足，无法审核该推广");
            }

            // 检查状态
            if (!"审核中".equals(promotion.getStatus())) {
                return Result.error("只能审核审核中状态的推广");
            }

            // 验证审核状态
            String auditStatus = promotionDTO.getAuditStatus();
            if (!"已通过".equals(auditStatus) && !"已拒绝".equals(auditStatus)) {
                return Result.error("审核状态无效");
            }

            // 如果是拒绝，必须提供拒绝理由
            if ("已拒绝".equals(auditStatus) &&
                    (promotionDTO.getRejectReason() == null || promotionDTO.getRejectReason().trim().isEmpty())) {
                return Result.error("拒绝审核时必须提供拒绝理由");
            }

            // 执行审核
            Integer currentEmployeeId = getCurrentEmployeeId();
            int result = promotionMapper.auditPromotionStatus(
                    id, auditStatus, currentEmployeeId, LocalDateTime.now(),
                    "已拒绝".equals(auditStatus) ? promotionDTO.getRejectReason() : null
            );

            if (result > 0) {
                log.info("推广审核完成，ID: {}, 状态: {}, 审核人: {}", id, auditStatus, currentEmployeeId);
                return Result.success();
            } else {
                return Result.error("审核失败");
            }
        } catch (Exception e) {
            log.error("审核推广时发生异常，ID: {}", id, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> uploadImage(MultipartFile file) {
        try {
            ImageUploadUtil.ImageUploadResult result = ImageUploadUtil.uploadImage(file);
            if (result.isSuccess()) {
                return Result.success(result.getAccessUrl());
            } else {
                return Result.error(result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("上传图片时发生异常", e);
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 批量图片上传方法 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前使用单图片上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用 uploadImage 进行单图片上传

    // {{FUTURE_EXTENSION: 图片删除方法 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前图片管理通过富文本编辑器处理，独立删除功能未使用}}
    // 已删除 - 图片删除通过富文本编辑器内部处理

    // {{FUTURE_EXTENSION: 权限检查方法 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于复杂权限控制场景，当前通过前端计算属性实现}}
    @Override
    public boolean hasPermission(Long promotionId, String operation) {
        try {
            Integer currentEmployeeId = getCurrentEmployeeId();
            String currentRole = getCurrentUserRole();

            if (currentEmployeeId == null || currentRole == null) {
                return false;
            }

            // 管理员拥有所有权限（支持大小写不敏感）
            if ("ADMIN".equalsIgnoreCase(currentRole)) {
                return true;
            }

            Promotion promotion = promotionMapper.selectById(promotionId);
            if (promotion == null) {
                return false;
            }

            switch (operation) {
                case "view":
                    // 查看权限：作者本人、已通过的推广任何人都可以查看、部门主管可以查看本部门推广
                    if (promotion.getAuthorId().equals(currentEmployeeId)) {
                        return true;
                    }
                    if ("已通过".equals(promotion.getStatus())) {
                        return true;
                    }
                    if ("MANAGER".equalsIgnoreCase(currentRole)) {
                        // 通过推广作者的部门ID检查权限
                        Employee author = employeeMapper.selectById(promotion.getAuthorId());
                        if (author != null && author.getDepartmentId() != null) {
                            List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                            return managedDepartmentIds.contains(author.getDepartmentId());
                        }
                    }
                    return false;

                case "edit":
                case "delete":
                    // 编辑/删除权限：作者本人 或 部门负责人可以操作
                    if (promotion.getAuthorId().equals(currentEmployeeId)) {
                        return true; // 作者本人
                    }
                    // 部门负责人可以编辑/删除本部门及子部门的推广
                    if ("MANAGER".equalsIgnoreCase(currentRole)) {
                        // 通过推广作者的部门ID检查权限
                        Employee author = employeeMapper.selectById(promotion.getAuthorId());
                        if (author != null && author.getDepartmentId() != null) {
                            List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                            return managedDepartmentIds.contains(author.getDepartmentId());
                        }
                    }
                    return false;

                case "audit":
                    // 审核权限：部门主管可以审核本部门及子部门的推广
                    if ("MANAGER".equalsIgnoreCase(currentRole)) {
                        // 通过推广作者的部门ID检查权限
                        Employee author = employeeMapper.selectById(promotion.getAuthorId());
                        if (author != null && author.getDepartmentId() != null) {
                            List<Integer> managedDepartmentIds = getManagedDepartmentIds();
                            return managedDepartmentIds.contains(author.getDepartmentId());
                        }
                    }
                    return false;

                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("检查权限时发生异常，推广ID: {}, 操作: {}", promotionId, operation, e);
            return false;
        }
    }

    // {{FUTURE_EXTENSION: 推广统计方法 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于管理后台的数据统计和分析功能}}
    @Override
    public Result<Map<String, Object>> getPromotionStatistics(Integer authorId) {
        try {
            Integer targetAuthorId = authorId != null ? authorId : getCurrentEmployeeId();
            if (targetAuthorId == null) {
                return Result.error("用户信息无效");
            }

            // 获取统计数据
            List<Map<String, Object>> statusCounts = promotionMapper.countByStatus(targetAuthorId, null);

            // 构建统计结果
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", 0);
            statistics.put("pendingCount", 0);
            statistics.put("auditingCount", 0);
            statistics.put("approvedCount", 0);
            statistics.put("rejectedCount", 0);

            int totalCount = 0;
            for (Map<String, Object> statusCount : statusCounts) {
                String status = (String) statusCount.get("status");
                Long count = (Long) statusCount.get("count");
                totalCount += count.intValue();

                switch (status) {
                    case "待审核":
                        statistics.put("pendingCount", count.intValue());
                        break;
                    case "审核中":
                        statistics.put("auditingCount", count.intValue());
                        break;
                    case "已通过":
                        statistics.put("approvedCount", count.intValue());
                        break;
                    case "已拒绝":
                        statistics.put("rejectedCount", count.intValue());
                        break;
                }
            }
            statistics.put("totalCount", totalCount);

            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取推广统计信息时发生异常", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> convertTempAttachments(Long promotionId, List<String> tempAttachmentPaths) {
        try {
            if (tempAttachmentPaths == null || tempAttachmentPaths.isEmpty()) {
                return Result.success(); // 没有临时附件需要转换
            }

            // 验证推广是否存在
            Promotion promotion = promotionMapper.selectById(promotionId);
            if (promotion == null) {
                return Result.error("推广不存在");
            }

            // 获取当前用户ID
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录");
            }

            // 处理每个临时附件
            for (String tempPath : tempAttachmentPaths) {
                try {
                    // 将临时附件移动到正式目录并创建数据库记录
                    Result<Void> convertResult = convertSingleTempAttachment(promotionId, tempPath, currentEmployeeId);
                    if (!convertResult.isSuccess()) {
                        log.warn("转换临时附件失败: {}, 错误: {}", tempPath, convertResult.getMessage());
                    }
                } catch (Exception e) {
                    log.error("转换临时附件异常: {}", tempPath, e);
                }
            }

            log.info("临时附件转换完成，推广ID: {}, 处理数量: {}", promotionId, tempAttachmentPaths.size());
            return Result.success();
        } catch (Exception e) {
            log.error("转换临时附件异常，推广ID: {}", promotionId, e);
            return Result.error("转换临时附件失败: " + e.getMessage());
        }
    }

    /**
     * 转换单个临时附件
     */
    private Result<Void> convertSingleTempAttachment(Long promotionId, String tempUrlPath, Integer uploaderId) {
        try {
            // 从URL路径转换为物理路径
            String relativePath = tempUrlPath.substring(this.urlPrefix.length());
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }
            Path tempPhysicalPath = Paths.get(this.uploadRootPath, relativePath);

            // 检查临时文件是否存在
            if (!Files.exists(tempPhysicalPath)) {
                return Result.error("临时文件不存在: " + tempUrlPath);
            }

            // 从临时文件名中提取原始文件名
            String tempFileName = tempPhysicalPath.getFileName().toString();
            String originalFileName = extractOriginalFileNameFromTemp(tempFileName);

            // 生成安全的文件名（用于正式存储）
            String safeFileName = getSafeFileName(originalFileName);

            // 生成正式文件路径，包含冲突解决
            LocalDateTime now = LocalDateTime.now();
            String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM"));
            String subDirectory = "promotions/attachments/" + datePath;
            String finalFileName = resolveFileNameConflictForConversion(subDirectory, safeFileName);

            // 构建正式文件的物理路径和URL路径
            Path formalPhysicalPath = Paths.get(this.uploadRootPath, subDirectory, finalFileName);
            String formalUrlPath = this.urlPrefix + "/" + subDirectory + "/" + finalFileName;

            // 创建正式目录
            Files.createDirectories(formalPhysicalPath.getParent());

            // 移动文件
            Files.move(tempPhysicalPath, formalPhysicalPath);

            // 创建数据库记录
            PromotionAttachment attachment = new PromotionAttachment();
            attachment.setPromotionId(promotionId);
            attachment.setFileName(originalFileName); // 保存原始文件名
            attachment.setFilePath(formalUrlPath); // 保存URL路径
            attachment.setFileSize(Files.size(formalPhysicalPath));
            attachment.setFileType(getFileTypeFromPath(originalFileName));
            attachment.setFileExtension(getFileExtensionFromPath(originalFileName));
            attachment.setUploadTime(now);
            attachment.setUploaderId(uploaderId);
            attachment.setDownloadCount(0);
            attachment.setIsDeleted(false);
            attachment.setCreateTime(now);
            attachment.setUpdateTime(now);

            // 保存到数据库
            int result = attachmentMapper.insert(attachment);
            if (result > 0) {
                log.info("临时附件转换成功: {} -> {}", tempUrlPath, formalUrlPath);
                return Result.success();
            } else {
                // 如果数据库保存失败，删除已移动的文件
                Files.deleteIfExists(formalPhysicalPath);
                return Result.error("数据库保存失败");
            }
        } catch (Exception e) {
            log.error("转换单个临时附件异常: {}", tempUrlPath, e);
            return Result.error("转换失败: " + e.getMessage());
        }
    }

    /**
     * 从临时文件名中提取原始文件名
     */
    private String extractOriginalFileNameFromTemp(String tempFileName) {
        // 新的临时文件名格式：直接使用原始文件名或带序号的文件名
        // 例如：file.zip, file(1).zip, file(2).zip
        // 或者在极端冲突情况下：temp_timestamp_uuid_originalname

        if (tempFileName.startsWith("temp_")) {
            // 兼容旧的timestamp格式（极端冲突情况下的回退策略）
            String withoutTempPrefix = tempFileName.substring(5);
            int firstUnderscoreIndex = withoutTempPrefix.indexOf('_');
            if (firstUnderscoreIndex > 0) {
                String afterFirstUnderscore = withoutTempPrefix.substring(firstUnderscoreIndex + 1);
                int secondUnderscoreIndex = afterFirstUnderscore.indexOf('_');
                if (secondUnderscoreIndex > 0 && secondUnderscoreIndex < afterFirstUnderscore.length() - 1) {
                    return afterFirstUnderscore.substring(secondUnderscoreIndex + 1);
                }
            }
            return tempFileName; // 如果解析失败，返回原文件名
        } else {
            // 新格式：直接是原始文件名或带序号的文件名
            // 移除序号后缀，例如 file(1).zip -> file.zip
            String fileName = tempFileName;
            // 检查是否有序号格式 (数字)
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                String nameWithoutExt = fileName.substring(0, lastDotIndex);
                String extension = fileName.substring(lastDotIndex);

                // 检查是否以 (数字) 结尾
                if (nameWithoutExt.matches(".*\\(\\d+\\)$")) {
                    // 移除序号部分，例如 file(1) -> file
                    int lastParenIndex = nameWithoutExt.lastIndexOf('(');
                    if (lastParenIndex > 0) {
                        fileName = nameWithoutExt.substring(0, lastParenIndex) + extension;
                    }
                }
            }
            return fileName;
        }
    }

    /**
     * 从文件名中提取原始文件名（兼容旧格式）
     */
    private String getOriginalFileName(String fileName) {
        // 兼容旧的文件名格式: timestamp_uuid_originalname 或 timestamp_uuid.extension
        String[] parts = fileName.split("_", 3);
        return parts.length >= 3 ? parts[2] : fileName;
    }

    /**
     * 获取安全的文件名
     */
    private String getSafeFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.trim().isEmpty()) {
            return "unnamed_" + System.currentTimeMillis() + ".unknown";
        }

        // 移除危险字符，保留中文、英文、数字、点、下划线、连字符、括号
        String safeName = originalFileName.replaceAll("[\\\\/:*?\"<>|]", "_");

        // 确保文件有扩展名
        if (!safeName.contains(".")) {
            safeName += ".unknown";
        }

        // 限制长度（保留扩展名）
        if (safeName.length() > 200) {
            int lastDotIndex = safeName.lastIndexOf('.');
            String nameWithoutExt = safeName.substring(0, lastDotIndex);
            String extension = safeName.substring(lastDotIndex);

            int maxNameLength = 200 - extension.length();
            if (maxNameLength > 0) {
                safeName = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength)) + extension;
            } else {
                // 扩展名太长，截断扩展名
                safeName = nameWithoutExt.substring(0, 190) + extension.substring(0, 10);
            }
        }

        return safeName;
    }

    /**
     * 解决文件名冲突（用于临时附件转换）
     */
    private String resolveFileNameConflictForConversion(String subDirectory, String fileName) {
        Path basePath = Paths.get(this.uploadRootPath, subDirectory);

        // 创建目录（如果不存在）
        try {
            Files.createDirectories(basePath);
        } catch (IOException e) {
            log.warn("创建目录失败: {}", basePath, e);
        }

        String originalFileName = fileName;
        int lastDotIndex = fileName.lastIndexOf('.');
        String nameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        String extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";

        int counter = 1;
        while (Files.exists(basePath.resolve(fileName))) {
            fileName = nameWithoutExt + "(" + counter + ")" + extension;
            counter++;

            // 防止无限循环，超过9999个重复文件时回退到timestamp策略
            if (counter > 9999) {
                String timestamp = String.valueOf(System.currentTimeMillis());
                String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                fileName = timestamp + "_" + uuid + "_" + originalFileName;
                log.warn("文件名冲突过多，回退到timestamp策略: {}", fileName);
                break;
            }
        }

        if (counter > 1) {
            log.info("文件名冲突已解决: {} -> {}", originalFileName, fileName);
        }

        return fileName;
    }

    /**
     * 解决文件名冲突（保留原有方法用于兼容）
     */
    private String resolveFileNameConflict(String datePath, String fileName) {
        String basePath = java.nio.file.Paths.get("uploads", "promotions", "attachments", datePath).toString();

        // 创建目录（如果不存在）
        try {
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get(basePath));
        } catch (java.io.IOException e) {
            log.warn("创建目录失败: {}", basePath, e);
        }

        String originalFileName = fileName;
        int lastDotIndex = fileName.lastIndexOf('.');
        String nameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        String extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";

        int counter = 1;
        while (java.nio.file.Files.exists(java.nio.file.Paths.get(basePath, fileName))) {
            fileName = nameWithoutExt + "(" + counter + ")" + extension;
            counter++;

            // 防止无限循环，超过9999个重复文件时回退到timestamp策略
            if (counter > 9999) {
                String timestamp = String.valueOf(System.currentTimeMillis());
                String uuid = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                fileName = timestamp + "_" + uuid + "_" + originalFileName;
                log.warn("文件名冲突过多，回退到timestamp策略: {}", fileName);
                break;
            }
        }

        if (counter > 1) {
            log.info("文件名冲突已解决: {} -> {}", originalFileName, fileName);
        }

        return fileName;
    }

    /**
     * 从文件路径获取文件类型
     */
    private String getFileTypeFromPath(String filePath) {
        String extension = getFileExtensionFromPath(filePath).toLowerCase();
        switch (extension) {
            case ".pdf":
                return "application/pdf";
            case ".doc":
                return "application/msword";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".xls":
                return "application/vnd.ms-excel";
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/x-rar-compressed";
            case ".txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 从文件路径获取文件扩展名
     */
    private String getFileExtensionFromPath(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        return lastDotIndex > 0 ? filePath.substring(lastDotIndex) : "";
    }
}

// {{CHENGQI: 推广管理业务逻辑实现类创建完成}}
