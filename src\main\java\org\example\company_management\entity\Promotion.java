package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * {{CHENGQI: 推广信息实体类}}
 * {{CHENGQI: 任务ID: P4-LD-001}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 更新时间: 2025-01-27 16:45:00 +08:00}}
 * {{CHENGQI: 描述: 推广信息实体类，对应数据库promotion表}}
 * {{CHENGQI: 更新内容: 添加富文本支持字段(contentType, attachments, contentSummary)}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Promotion {

    /**
     * 推广ID
     */
    private Long id;

    /**
     * 推广标题
     */
    @NotBlank(message = "推广标题不能为空")
    @Size(max = 100, message = "推广标题长度不能超过100个字符")
    private String title;

    /**
     * 推广内容（支持富文本HTML，最多50000字符）
     */
    @NotBlank(message = "推广内容不能为空")
    @Size(max = 50000, message = "推广内容长度不能超过50000个字符")
    private String content;

    /**
     * 内容类型：TEXT-纯文本，HTML-富文本
     */
    @Pattern(regexp = "^(TEXT|HTML)$", message = "内容类型必须为TEXT或HTML")
    private String contentType = "TEXT";

    /**
     * 附件文件信息列表（JSON格式存储）
     */
    private String attachments;

    /**
     * 内容摘要（从富文本中提取的纯文本，用于搜索和列表展示）
     */
    @Size(max = 500, message = "内容摘要长度不能超过500个字符")
    private String contentSummary;

    /**
     * 推广图片URL列表（JSON格式存储）
     */
    private String images;

    /**
     * 发布人员工ID（推广所属部门通过此字段动态关联到员工的当前部门）
     */
    @NotNull(message = "发布人员工ID不能为空")
    private Integer authorId;

    /**
     * 审核状态：待审核、审核中、已通过、已拒绝
     */
    @NotBlank(message = "审核状态不能为空")
    @Pattern(regexp = "^(待审核|审核中|已通过|已拒绝)$", message = "审核状态必须为：待审核、审核中、已通过、已拒绝")
    private String status;

    /**
     * 提交审核时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitTime;

    /**
     * 审核完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    /**
     * 审核人员工ID（部门负责人）
     */
    private Integer auditorId;

    /**
     * 拒绝理由
     */
    @Size(max = 500, message = "拒绝理由长度不能超过500个字符")
    private String rejectReason;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // ==================== 非数据库字段（用于查询结果展示） ====================

    /**
     * 发布人姓名（非数据库字段）
     */
    private String authorName;

    /**
     * 发布人所属部门名称（非数据库字段，通过author_id动态关联获取）
     */
    private String departmentName;

    /**
     * 审核人姓名（非数据库字段）
     */
    private String auditorName;
}

// {{CHENGQI: 推广实体类创建完成}}
