package org.example.company_management.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import jakarta.servlet.MultipartConfigElement;

/**
 * 文件上传配置类
 * 确保文件上传大小限制正确生效
 */
@Slf4j
@Configuration
public class MultipartConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小为 50MB
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        
        // 设置整个请求最大大小为 50MB
        factory.setMaxRequestSize(DataSize.ofMegabytes(50));
        
        // 设置临时文件存储位置
        factory.setLocation(System.getProperty("java.io.tmpdir"));
        
        log.info("文件上传配置已生效:");
        log.info("  - 单个文件最大大小: 50MB");
        log.info("  - 整个请求最大大小: 50MB");
        log.info("  - 临时文件存储位置: {}", System.getProperty("java.io.tmpdir"));
        
        return factory.createMultipartConfig();
    }
}
