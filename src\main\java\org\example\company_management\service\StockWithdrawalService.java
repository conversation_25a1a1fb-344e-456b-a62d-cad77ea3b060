package org.example.company_management.service;

import org.example.company_management.dto.StockWithdrawalDTO;
import org.example.company_management.dto.StockWithdrawalQueryDTO;
import org.example.company_management.dto.StockWithdrawalSubmitDTO;
import org.example.company_management.utils.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 股票提现服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface StockWithdrawalService {
    
    /**
     * 提交股票提现申请
     * 
     * @param submitDTO 提现申请数据
     * @return 提现申请信息
     */
    StockWithdrawalDTO submitWithdrawal(StockWithdrawalSubmitDTO submitDTO);
    
    /**
     * 分页查询股票提现申请记录
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<StockWithdrawalDTO> getStockWithdrawalPage(StockWithdrawalQueryDTO queryDTO);
    
    /**
     * 根据ID查询股票提现申请详情
     * 
     * @param id 提现申请ID
     * @return 提现申请详情
     */
    StockWithdrawalDTO getStockWithdrawalById(Long id);
    
    /**
     * 查询当前员工的股票提现申请记录
     * 
     * @param employeeId 员工ID
     * @param page 页码
     * @param size 每页大小
     * @param status 申请状态（可选）
     * @return 分页结果
     */
    PageResult<StockWithdrawalDTO> getMyStockWithdrawals(Integer employeeId, Integer page, Integer size, String status);
    
    /**
     * 审核股票提现申请
     * 
     * @param id 提现申请ID
     * @param action 审核动作（APPROVE/REJECT）
     * @param rejectReason 拒绝理由（当action为REJECT时必填）
     * @param auditorId 审核人ID
     * @return 审核后的提现申请信息
     */
    StockWithdrawalDTO auditStockWithdrawal(Long id, String action, String rejectReason, Integer auditorId);
    
    /**
     * 批量审核股票提现申请
     * 
     * @param ids 提现申请ID列表
     * @param action 审核动作（APPROVE/REJECT）
     * @param rejectReason 拒绝理由（当action为REJECT时必填）
     * @param auditorId 审核人ID
     * @return 批量审核结果
     */
    Map<String, Object> batchAuditStockWithdrawal(List<Long> ids, String action, String rejectReason, Integer auditorId);
    
    /**
     * 取消股票提现申请（仅限待审核状态）
     * 
     * @param id 提现申请ID
     * @param employeeId 员工ID（用于权限验证）
     * @return 是否取消成功
     */
    boolean cancelStockWithdrawal(Long id, Integer employeeId);
    
    /**
     * 查询股票提现统计信息
     * 
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 统计信息
     */
    Map<String, Object> getStockWithdrawalStatistics(String startDate, String endDate);
    
    /**
     * 查询员工的股票提现统计信息
     * 
     * @param employeeId 员工ID
     * @return 员工提现统计信息
     */
    Map<String, Object> getEmployeeWithdrawalStatistics(Integer employeeId);
}
