import request from '@/utils/request'

// 股票价格管理API

/**
 * 分页查询股票价格记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStockPricePage = (params) => {
  return request({
    url: '/stock-price/page',
    method: 'get',
    params
  })
}

/**
 * 查询所有股票价格记录（不分页）
 * @returns {Promise}
 */
export const getAllStockPrices = () => {
  return request({
    url: '/stock-price/all',
    method: 'get'
  })
}

// 删除：getStockPriceById - 没有单独查看详情的需求

/**
 * 添加股票价格记录
 * @param {Object} data 股票价格数据
 * @returns {Promise}
 */
export const addStockPrice = (data) => {
  return request({
    url: '/stock-price',
    method: 'post',
    data
  })
}

/**
 * 更新股票价格记录
 * @param {Object} data 股票价格数据
 * @returns {Promise}
 */
export const updateStockPrice = (data) => {
  return request({
    url: '/stock-price',
    method: 'put',
    data
  })
}

/**
 * 删除股票价格记录
 * @param {number} id 股票价格ID
 * @returns {Promise}
 */
export const deleteStockPrice = (id) => {
  return request({
    url: `/stock-price/${id}`,
    method: 'delete'
  })
}

/**
 * 查询最新股票价格记录
 * 用途：首页显示最新股价、统计面板等
 * @returns {Promise}
 */
export const getLatestStockPrice = () => {
  return request({
    url: '/stock-price/latest',
    method: 'get'
  })
}

/**
 * 查询指定日期范围内的股票价格记录
 * 用途：图表的日期范围筛选功能
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @returns {Promise}
 */
export const getStockPricesByDateRange = (startDate, endDate) => {
  return request({
    url: '/stock-price/date-range',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

// 删除：getStockPriceByTime - 功能重复，可用日期范围查询替代
// 删除：checkTimeExists - 后端内部验证，前端不需要直接调用

// 员工股票管理API

/**
 * 分页查询员工股票记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getEmployeeStockPage = (params) => {
  return request({
    url: '/employee-stock/page',
    method: 'get',
    params
  })
}

// 删除：getEmployeeStockById - 没有单独查看详情的需求

/**
 * 添加员工股票记录
 * @param {Object} data 员工股票数据
 * @returns {Promise}
 */
export const addEmployeeStock = (data) => {
  return request({
    url: '/employee-stock',
    method: 'post',
    data
  })
}

/**
 * 批量添加员工股票记录
 * @param {Object} data 批量员工股票数据
 * @returns {Promise}
 */
export const addEmployeeStockBatch = (data) => {
  return request({
    url: '/employee-stock/batch',
    method: 'post',
    data
  })
}

/**
 * 更新员工股票记录
 * @param {Object} data 员工股票数据
 * @returns {Promise}
 */
export const updateEmployeeStock = (data) => {
  return request({
    url: '/employee-stock',
    method: 'put',
    data
  })
}

/**
 * 删除员工股票记录
 * @param {number} id 员工股票记录ID
 * @returns {Promise}
 */
export const deleteEmployeeStock = (id) => {
  return request({
    url: `/employee-stock/${id}`,
    method: 'delete'
  })
}

/**
 * 根据员工ID查询股票记录
 * 用途：员工个人查看自己的股票持有情况
 * @param {number} employeeId 员工ID
 * @returns {Promise}
 */
export const getEmployeeStocksByEmployeeId = (employeeId) => {
  return request({
    url: `/employee-stock/by-employee/${employeeId}`,
    method: 'get'
  })
}

// 删除：getEmployeeStocksByDepartmentId - 可通过分页查询+部门筛选实现

/**
 * 查询员工股票总数量
 * 用途：统计功能，显示员工总持股数量
 * @param {number} employeeId 员工ID
 * @returns {Promise}
 */
export const getTotalQuantityByEmployeeId = (employeeId) => {
  return request({
    url: `/employee-stock/quantity/${employeeId}`,
    method: 'get'
  })
}

/**
 * 查询员工股票总价值
 * 用途：统计功能，显示员工总持股价值
 * @param {number} employeeId 员工ID
 * @returns {Promise}
 */
export const getTotalValueByEmployeeId = (employeeId) => {
  return request({
    url: `/employee-stock/value/${employeeId}`,
    method: 'get'
  })
}

/**
 * 查询已解禁股票记录
 * 用途：解禁股票管理功能
 * @returns {Promise}
 */
export const getUnlockedEmployeeStocks = () => {
  return request({
    url: '/employee-stock/unlocked',
    method: 'get'
  })
}

/**
 * 查询未解禁股票记录
 * 用途：未解禁股票管理功能
 * @returns {Promise}
 */
export const getLockedEmployeeStocks = () => {
  return request({
    url: '/employee-stock/locked',
    method: 'get'
  })
}

// 删除：getEmployeeStocksByStockId - 使用场景较少

/**
 * 查询股票统计信息
 * 用途：统计报表功能，整体股票分布统计
 * @returns {Promise}
 */
export const getStockStatistics = () => {
  return request({
    url: '/employee-stock/statistics',
    method: 'get'
  })
}

// 删除：checkEmployeeHasStock - 后端内部验证，前端不需要直接调用

// 股票提现管理API（管理员端）

/**
 * 分页查询股票提现申请记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @param {string} params.status - 申请状态（可选）
 * @param {number} params.employeeId - 员工ID（可选）
 * @param {string} params.startDate - 开始日期（可选）
 * @param {string} params.endDate - 结束日期（可选）
 * @returns {Promise} API响应
 */
export const getStockWithdrawalPage = (params) => {
  return request({
    url: '/stock-withdrawal/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID查询股票提现申请详情
 * @param {number} id - 提现申请ID
 * @returns {Promise} API响应
 */
export const getStockWithdrawalById = (id) => {
  return request({
    url: `/stock-withdrawal/${id}`,
    method: 'get'
  })
}

/**
 * 审核股票提现申请
 * @param {number} id - 提现申请ID
 * @param {Object} data - 审核数据
 * @param {string} data.action - 审核动作（APPROVE/REJECT）
 * @param {string} data.rejectReason - 拒绝理由（当action为REJECT时必填）
 * @returns {Promise} API响应
 */
export const auditStockWithdrawal = (id, data) => {
  return request({
    url: `/stock-withdrawal/${id}/audit`,
    method: 'put',
    data
  })
}

/**
 * 批量审核股票提现申请
 * @param {Object} data - 批量审核数据
 * @param {Array} data.ids - 提现申请ID数组
 * @param {string} data.action - 审核动作（APPROVE/REJECT）
 * @param {string} data.rejectReason - 拒绝理由（当action为REJECT时必填）
 * @returns {Promise} API响应
 */
export const batchAuditStockWithdrawal = (data) => {
  return request({
    url: '/stock-withdrawal/batch-audit',
    method: 'put',
    data
  })
}

/**
 * 查询股票提现统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期（可选）
 * @param {string} params.endDate - 结束日期（可选）
 * @returns {Promise} API响应
 */
export const getStockWithdrawalStatistics = (params = {}) => {
  return request({
    url: '/stock-withdrawal/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取持股总览数据（分页）
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.employeeName - 员工姓名（可选）
 * @param {number} params.departmentId - 部门ID（可选）
 * @returns {Promise} API响应
 */
export const getStockOverviewPage = (params) => {
  return request({
    url: '/employee-stock/overview',
    method: 'get',
    params
  })
}
