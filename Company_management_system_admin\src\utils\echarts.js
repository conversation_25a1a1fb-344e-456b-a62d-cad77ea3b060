// {{CHENGQI: ECharts 按需引入配置 - 减少包体积}}
// {{CHENGQI: 创建时间: 2025-07-09 11:30:00 +08:00}}
// {{CHENGQI: 说明: 按需引入ECharts组件，预计减少60-80%的包大小}}

// 按需引入 ECharts 核心模块
import * as echarts from 'echarts/core'

// 按需引入图表类型
import {
  LineChart,
  BarChart,
  PieChart,
  ScatterChart
} from 'echarts/charts'

// 按需引入组件
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
  ToolboxComponent,
  BrushComponent
} from 'echarts/components'

// 引入渲染器
import { CanvasRenderer } from 'echarts/renderers'

// 注册必需的组件
echarts.use([
  // 图表类型
  LineChart,
  BarChart,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  
  // 组件
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
  ToolboxComponent,
  BrushComponent,
  
  // 渲染器
  CanvasRenderer
])

export default echarts
