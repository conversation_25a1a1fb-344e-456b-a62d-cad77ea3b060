package org.example.company_management.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.StockWithdrawalDTO;
import org.example.company_management.dto.StockWithdrawalQueryDTO;
import org.example.company_management.dto.StockWithdrawalSubmitDTO;
import org.example.company_management.entity.StockWithdrawal;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.EmployeeStockSummaryMapper;
import org.example.company_management.mapper.StockWithdrawalMapper;
import org.example.company_management.service.StockPriceService;
import org.example.company_management.service.StockWithdrawalService;
import org.example.company_management.utils.BeanCopyUtils;
import org.example.company_management.utils.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 股票提现服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockWithdrawalServiceImpl implements StockWithdrawalService {
    
    private final StockWithdrawalMapper stockWithdrawalMapper;
    private final EmployeeStockSummaryMapper employeeStockSummaryMapper;
    private final StockPriceService stockPriceService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StockWithdrawalDTO submitWithdrawal(StockWithdrawalSubmitDTO submitDTO) {
        log.info("提交股票提现申请: {}", submitDTO);
        
        Integer employeeId = submitDTO.getEmployeeId();
        Integer quantity = submitDTO.getQuantity();
        
        // 1. 检查员工是否有待审核的申请
        Integer pendingCount = stockWithdrawalMapper.countPendingByEmployeeId(employeeId);
        if (pendingCount > 0) {
            throw new BusinessException("您还有待审核的提现申请，请等待审核完成后再提交新申请");
        }
        
        // 2. 检查可提现数量
        Integer availableQuantity = employeeStockSummaryMapper.selectByEmployeeId(employeeId).getAvailableQuantity();
        if (availableQuantity == null || availableQuantity < quantity) {
            throw new BusinessException("可提现数量不足，当前可提现数量：" + (availableQuantity != null ? availableQuantity : 0));
        }
        
        // 3. 获取当前股票价格
        var latestStockPrice = stockPriceService.getLatestStockPrice();
        if (latestStockPrice == null) {
            throw new BusinessException("系统中没有股票价格记录，无法计算提现金额");
        }
        
        BigDecimal unitPrice = latestStockPrice.getUnitPrice();
        BigDecimal totalAmount = unitPrice.multiply(new BigDecimal(quantity));
        
        // 4. 创建提现申请记录
        StockWithdrawal stockWithdrawal = new StockWithdrawal();
        stockWithdrawal.setEmployeeId(employeeId);
        stockWithdrawal.setQuantity(quantity);
        stockWithdrawal.setUnitPrice(unitPrice);
        stockWithdrawal.setTotalAmount(totalAmount);
        stockWithdrawal.setRemark(submitDTO.getRemark()); // 只使用备注字段
        stockWithdrawal.setStatus("PENDING");
        stockWithdrawal.setApplyTime(LocalDateTime.now());
        
        int result = stockWithdrawalMapper.insert(stockWithdrawal);
        if (result <= 0) {
            throw new BusinessException("提交提现申请失败");
        }
        
        // 5. 查询并返回完整信息
        StockWithdrawal inserted = stockWithdrawalMapper.selectByIdWithDetails(stockWithdrawal.getId());
        StockWithdrawalDTO dto = BeanCopyUtils.copyBean(inserted, StockWithdrawalDTO.class);
        
        log.info("股票提现申请提交成功: id={}, employeeId={}, quantity={}, totalAmount={}", 
                stockWithdrawal.getId(), employeeId, quantity, totalAmount);
        
        return dto;
    }
    
    @Override
    public PageResult<StockWithdrawalDTO> getStockWithdrawalPage(StockWithdrawalQueryDTO queryDTO) {
        log.info("分页查询股票提现申请: {}", queryDTO);
        
        try {
            // 设置分页参数
            PageHelper.startPage(queryDTO.getPage(), queryDTO.getSize());
            
            // 查询数据
            List<StockWithdrawal> list = stockWithdrawalMapper.selectByCondition(queryDTO);
            PageInfo<StockWithdrawal> pageInfo = new PageInfo<>(list);
            
            // 转换DTO
            List<StockWithdrawalDTO> dtoList = BeanCopyUtils.copyBeanList(list, StockWithdrawalDTO.class);
            
            // 设置状态描述
            dtoList.forEach(this::setStatusDescription);
            
            PageResult<StockWithdrawalDTO> result = new PageResult<>();
            result.setList(dtoList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            return result;
            
        } catch (Exception e) {
            log.error("分页查询股票提现申请失败", e);
            throw new BusinessException("查询提现申请失败");
        }
    }
    
    @Override
    public StockWithdrawalDTO getStockWithdrawalById(Long id) {
        log.info("根据ID查询股票提现申请: id={}", id);
        
        StockWithdrawal stockWithdrawal = stockWithdrawalMapper.selectByIdWithDetails(id);
        if (stockWithdrawal == null) {
            throw new BusinessException("提现申请不存在");
        }
        
        StockWithdrawalDTO dto = BeanCopyUtils.copyBean(stockWithdrawal, StockWithdrawalDTO.class);
        setStatusDescription(dto);
        
        return dto;
    }
    
    @Override
    public PageResult<StockWithdrawalDTO> getMyStockWithdrawals(Integer employeeId, Integer page, Integer size, String status) {
        log.info("查询员工股票提现申请: employeeId={}, page={}, size={}, status={}", employeeId, page, size, status);
        
        try {
            // 设置分页参数
            PageHelper.startPage(page, size);
            
            // 查询数据
            List<StockWithdrawal> list = stockWithdrawalMapper.selectByEmployeeId(employeeId, status);
            PageInfo<StockWithdrawal> pageInfo = new PageInfo<>(list);
            
            // 转换DTO
            List<StockWithdrawalDTO> dtoList = BeanCopyUtils.copyBeanList(list, StockWithdrawalDTO.class);
            
            // 设置状态描述
            dtoList.forEach(this::setStatusDescription);
            
            PageResult<StockWithdrawalDTO> result = new PageResult<>();
            result.setList(dtoList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            return result;
            
        } catch (Exception e) {
            log.error("查询员工股票提现申请失败", e);
            throw new BusinessException("查询提现申请失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StockWithdrawalDTO auditStockWithdrawal(Long id, String action, String rejectReason, Integer auditorId) {
        log.info("审核股票提现申请: id={}, action={}, auditorId={}", id, action, auditorId);
        
        // 1. 查询提现申请
        StockWithdrawal stockWithdrawal = stockWithdrawalMapper.selectById(id);
        if (stockWithdrawal == null) {
            throw new BusinessException("提现申请不存在");
        }
        
        if (!"PENDING".equals(stockWithdrawal.getStatus())) {
            throw new BusinessException("只能审核待审核状态的申请");
        }
        
        // 2. 验证审核动作
        if (!"APPROVE".equals(action) && !"REJECT".equals(action)) {
            throw new BusinessException("无效的审核动作");
        }
        
        if ("REJECT".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            throw new BusinessException("拒绝申请时必须填写拒绝理由");
        }
        
        // 3. 如果是批准，需要再次检查可提现数量
        if ("APPROVE".equals(action)) {
            var summary = employeeStockSummaryMapper.selectByEmployeeId(stockWithdrawal.getEmployeeId());
            if (summary == null || summary.getAvailableQuantity() < stockWithdrawal.getQuantity()) {
                throw new BusinessException("员工可提现数量不足，无法批准申请");
            }
        }
        
        // 4. 更新申请状态
        stockWithdrawal.setStatus("APPROVE".equals(action) ? "APPROVED" : "REJECTED");
        stockWithdrawal.setAuditTime(LocalDateTime.now());
        stockWithdrawal.setAuditorId(auditorId);
        if ("REJECT".equals(action)) {
            stockWithdrawal.setRejectReason(rejectReason);
        }
        
        int result = stockWithdrawalMapper.update(stockWithdrawal);
        if (result <= 0) {
            throw new BusinessException("审核失败");
        }
        
        // 5. 查询并返回更新后的信息
        StockWithdrawal updated = stockWithdrawalMapper.selectByIdWithDetails(id);
        StockWithdrawalDTO dto = BeanCopyUtils.copyBean(updated, StockWithdrawalDTO.class);
        setStatusDescription(dto);
        
        log.info("股票提现申请审核完成: id={}, status={}, auditorId={}", id, dto.getStatus(), auditorId);
        
        return dto;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchAuditStockWithdrawal(List<Long> ids, String action, String rejectReason, Integer auditorId) {
        log.info("批量审核股票提现申请: ids={}, action={}, auditorId={}", ids, action, auditorId);
        
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("请选择要审核的申请");
        }
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        for (Long id : ids) {
            try {
                auditStockWithdrawal(id, action, rejectReason, auditorId);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("ID ").append(id).append(": ").append(e.getMessage()).append("; ");
                log.error("批量审核失败: id={}", id, e);
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages.toString());
        
        log.info("批量审核完成: 成功{}个, 失败{}个", successCount, failCount);
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelStockWithdrawal(Long id, Integer employeeId) {
        log.info("取消股票提现申请: id={}, employeeId={}", id, employeeId);
        
        // 1. 查询提现申请
        StockWithdrawal stockWithdrawal = stockWithdrawalMapper.selectById(id);
        if (stockWithdrawal == null) {
            throw new BusinessException("提现申请不存在");
        }
        
        // 2. 验证权限
        if (!stockWithdrawal.getEmployeeId().equals(employeeId)) {
            throw new BusinessException("无权取消此申请");
        }
        
        // 3. 验证状态
        if (!"PENDING".equals(stockWithdrawal.getStatus())) {
            throw new BusinessException("只能取消待审核状态的申请");
        }
        
        // 4. 更新状态为已取消（用户主动取消）
        stockWithdrawal.setStatus("CANCELLED");
        stockWithdrawal.setAuditTime(LocalDateTime.now());
        // 不设置拒绝理由，因为这是用户主动取消
        
        int result = stockWithdrawalMapper.update(stockWithdrawal);
        
        log.info("股票提现申请取消成功: id={}", id);
        
        return result > 0;
    }
    
    @Override
    public Map<String, Object> getStockWithdrawalStatistics(String startDate, String endDate) {
        // TODO: 实现统计功能
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalApplications", 0);
        statistics.put("pendingApplications", 0);
        statistics.put("approvedApplications", 0);
        statistics.put("rejectedApplications", 0);
        statistics.put("totalAmount", BigDecimal.ZERO);
        return statistics;
    }
    
    @Override
    public Map<String, Object> getEmployeeWithdrawalStatistics(Integer employeeId) {
        // TODO: 实现员工统计功能
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalApplications", 0);
        statistics.put("approvedApplications", 0);
        statistics.put("totalWithdrawnAmount", BigDecimal.ZERO);
        return statistics;
    }
    
    /**
     * 设置状态描述
     */
    private void setStatusDescription(StockWithdrawalDTO dto) {
        switch (dto.getStatus()) {
            case "PENDING":
                dto.setStatusDescription("待审核");
                break;
            case "APPROVED":
                dto.setStatusDescription("已批准");
                break;
            case "REJECTED":
                dto.setStatusDescription("已拒绝");
                break;
            default:
                dto.setStatusDescription("未知状态");
        }
    }
}
