package org.example.company_management.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.StockPriceDTO;
import org.example.company_management.dto.StockPriceQueryDTO;
import org.example.company_management.entity.StockPrice;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.StockPriceMapper;
import org.example.company_management.service.StockPriceService;
import org.example.company_management.utils.BeanCopyUtils;
import org.example.company_management.utils.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 股票价格服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockPriceServiceImpl implements StockPriceService {

    private final StockPriceMapper stockPriceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StockPriceDTO addStockPrice(StockPriceDTO stockPriceDTO) {
        log.info("添加股票价格记录: {}", stockPriceDTO);

        // 检查时间是否已存在
        if (isTimeExists(stockPriceDTO.getTime(), null)) {
            throw new BusinessException("该日期已存在股票价格记录");
        }

        // DTO转Entity
        StockPrice stockPrice = BeanCopyUtils.copyBean(stockPriceDTO, StockPrice.class);

        // 插入数据库
        int result = stockPriceMapper.insert(stockPrice);
        if (result <= 0) {
            throw new BusinessException("添加股票价格记录失败");
        }

        // 返回添加后的数据
        return BeanCopyUtils.copyBean(stockPrice, StockPriceDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStockPrice(Long id) {
        log.info("删除股票价格记录: {}", id);

        // 检查记录是否存在
        StockPrice stockPrice = stockPriceMapper.selectById(id);
        if (stockPrice == null) {
            throw new BusinessException("股票价格记录不存在");
        }

        // 删除记录
        int result = stockPriceMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StockPriceDTO updateStockPrice(StockPriceDTO stockPriceDTO) {
        log.info("更新股票价格记录: {}", stockPriceDTO);

        // 检查记录是否存在
        StockPrice existingStock = stockPriceMapper.selectById(stockPriceDTO.getId());
        if (existingStock == null) {
            throw new BusinessException("股票价格记录不存在");
        }

        // 检查时间是否已被其他记录使用
        if (isTimeExists(stockPriceDTO.getTime(), stockPriceDTO.getId())) {
            throw new BusinessException("该日期已存在其他股票价格记录");
        }

        // DTO转Entity
        StockPrice stockPrice = BeanCopyUtils.copyBean(stockPriceDTO, StockPrice.class);

        // 更新数据库
        int result = stockPriceMapper.updateById(stockPrice);
        if (result <= 0) {
            throw new BusinessException("更新股票价格记录失败");
        }

        // 返回更新后的数据
        StockPrice updatedStock = stockPriceMapper.selectById(stockPrice.getId());
        return BeanCopyUtils.copyBean(updatedStock, StockPriceDTO.class);
    }

    // 删除：getStockPriceById - 前端不需要单独查看详情

    @Override
    public PageResult<StockPriceDTO> getStockPriceByPage(StockPriceQueryDTO queryDTO) {
        log.info("分页查询股票价格记录: {}", queryDTO);

        // 设置分页参数
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询参数（不包含分页参数）
        Map<String, Object> params = buildQueryParamsWithoutPagination(queryDTO);

        // 查询数据
        List<StockPrice> stockPrices = stockPriceMapper.selectByPageWithPageHelper(params);
        PageInfo<StockPrice> pageInfo = new PageInfo<>(stockPrices);

        // 转换为DTO
        List<StockPriceDTO> stockPriceDTOs = stockPrices.stream()
                .map(stockPrice -> BeanCopyUtils.copyBean(stockPrice, StockPriceDTO.class))
                .collect(Collectors.toList());

        return new PageResult<StockPriceDTO>(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getTotal(), stockPriceDTOs);
    }

    @Override
    public List<StockPriceDTO> getAllStockPrices() {
        log.info("查询所有股票价格记录");

        List<StockPrice> stockPrices = stockPriceMapper.selectAll();
        return stockPrices.stream()
                .map(stockPrice -> BeanCopyUtils.copyBean(stockPrice, StockPriceDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<StockPriceDTO> getStockPricesByDateRange(LocalDate startDate, LocalDate endDate) {
        log.info("查询日期范围内的股票价格记录: {} - {}", startDate, endDate);

        List<StockPrice> stockPrices = stockPriceMapper.selectByDateRange(startDate, endDate);
        return stockPrices.stream()
                .map(stockPrice -> BeanCopyUtils.copyBean(stockPrice, StockPriceDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public StockPriceDTO getLatestStockPrice() {
        log.info("查询最新的股票价格记录");

        StockPrice stockPrice = stockPriceMapper.selectLatest();
        if (stockPrice == null) {
            return null;
        }

        return BeanCopyUtils.copyBean(stockPrice, StockPriceDTO.class);
    }

    // 删除：getStockPricesByRemark - 通过分页查询的remark参数实现

    @Override
    public boolean isTimeExists(LocalDate time, Long excludeId) {
        int count = stockPriceMapper.countByTime(time, excludeId);
        return count > 0;
    }

    // 删除：getStockPriceByTime - 功能重复，可用日期范围查询替代

    /**
     * 构建查询参数（包含分页参数，用于原有的手动分页方法）
     */
    private Map<String, Object> buildQueryParams(StockPriceQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();

        // 分页参数
        if (queryDTO.getPageNum() != null && queryDTO.getPageSize() != null) {
            params.put("offset", (queryDTO.getPageNum() - 1) * queryDTO.getPageSize());
            params.put("limit", queryDTO.getPageSize());
        }

        // 查询条件
        if (queryDTO.getStartDate() != null) {
            params.put("startDate", queryDTO.getStartDate());
        }
        if (queryDTO.getEndDate() != null) {
            params.put("endDate", queryDTO.getEndDate());
        }
        if (queryDTO.getRemark() != null && !queryDTO.getRemark().trim().isEmpty()) {
            params.put("remark", queryDTO.getRemark().trim());
        }

        // 排序参数
        if (queryDTO.getOrderBy() != null && !queryDTO.getOrderBy().trim().isEmpty()) {
            params.put("orderBy", queryDTO.getOrderBy());
        }
        if (queryDTO.getOrderDirection() != null && !queryDTO.getOrderDirection().trim().isEmpty()) {
            params.put("orderDirection", queryDTO.getOrderDirection());
        }

        return params;
    }

    /**
     * 构建查询参数（不包含分页参数，用于PageHelper分页）
     */
    private Map<String, Object> buildQueryParamsWithoutPagination(StockPriceQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();

        // 查询条件
        if (queryDTO.getStartDate() != null) {
            params.put("startDate", queryDTO.getStartDate());
        }
        if (queryDTO.getEndDate() != null) {
            params.put("endDate", queryDTO.getEndDate());
        }
        if (queryDTO.getRemark() != null && !queryDTO.getRemark().trim().isEmpty()) {
            params.put("remark", queryDTO.getRemark().trim());
        }

        // 排序参数
        if (queryDTO.getOrderBy() != null && !queryDTO.getOrderBy().trim().isEmpty()) {
            params.put("orderBy", queryDTO.getOrderBy());
        }
        if (queryDTO.getOrderDirection() != null && !queryDTO.getOrderDirection().trim().isEmpty()) {
            params.put("orderDirection", queryDTO.getOrderDirection());
        }

        return params;
    }
}
