package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.EmployeeStockSummary;

import java.util.List;
import java.util.Map;

/**
 * 员工股票汇总数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Mapper
public interface EmployeeStockSummaryMapper {
    
    /**
     * 根据员工ID查询股票汇总信息
     * 
     * @param employeeId 员工ID
     * @return 股票汇总信息
     */
    EmployeeStockSummary selectByEmployeeId(@Param("employeeId") Integer employeeId);
    
    /**
     * 根据员工ID查询股票汇总信息（包含关联信息）
     * 
     * @param employeeId 员工ID
     * @return 股票汇总信息（包含员工姓名、部门名称等）
     */
    EmployeeStockSummary selectByEmployeeIdWithDetails(@Param("employeeId") Integer employeeId);
    
    /**
     * 插入或更新员工股票汇总信息
     * 
     * @param summary 股票汇总信息
     * @return 影响行数
     */
    int insertOrUpdate(EmployeeStockSummary summary);
    
    /**
     * 更新员工股票汇总信息
     * 
     * @param summary 股票汇总信息
     * @return 影响行数
     */
    int update(EmployeeStockSummary summary);
    
    /**
     * 删除员工股票汇总信息
     * 
     * @param employeeId 员工ID
     * @return 影响行数
     */
    int deleteByEmployeeId(@Param("employeeId") Integer employeeId);
    
    /**
     * 查询所有员工股票汇总信息
     * 
     * @return 股票汇总信息列表
     */
    List<EmployeeStockSummary> selectAll();
    
    /**
     * 查询所有员工股票汇总信息（包含关联信息）
     * 
     * @return 股票汇总信息列表（包含员工姓名、部门名称等）
     */
    List<EmployeeStockSummary> selectAllWithDetails();
    
    /**
     * 根据部门ID查询员工股票汇总信息
     * 
     * @param departmentId 部门ID
     * @return 股票汇总信息列表
     */
    List<EmployeeStockSummary> selectByDepartmentId(@Param("departmentId") Integer departmentId);
    
    /**
     * 重新计算指定员工的股票汇总数据
     * 
     * @param employeeId 员工ID
     * @return 影响行数
     */
    int recalculateByEmployeeId(@Param("employeeId") Integer employeeId);
    
    /**
     * 重新计算所有员工的股票汇总数据
     * 
     * @return 影响行数
     */
    int recalculateAll();
    
    /**
     * 检查数据一致性
     * 
     * @return 不一致的记录列表
     */
    List<Map<String, Object>> checkDataConsistency();
    
    /**
     * 统计股票汇总信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getStatistics();
    
    /**
     * 查询有股票的员工数量
     * 
     * @return 员工数量
     */
    Integer countEmployeesWithStock();
    
    /**
     * 查询总股票数量
     * 
     * @return 总股票数量
     */
    Integer getTotalStockQuantity();
    
    /**
     * 查询总可提现数量
     * 
     * @return 总可提现数量
     */
    Integer getTotalAvailableQuantity();
    
    /**
     * 查询需要重新计算的记录（基于最后计算时间）
     * 
     * @param hours 小时数，查询超过指定小时未更新的记录
     * @return 需要重新计算的员工ID列表
     */
    List<Integer> selectNeedRecalculate(@Param("hours") Integer hours);
}
