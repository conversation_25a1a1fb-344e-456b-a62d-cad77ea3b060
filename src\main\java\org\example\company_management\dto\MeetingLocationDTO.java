package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;
import org.example.company_management.validation.ValidDateRange;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.LocalDate;

/**
 * 会议地点数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ValidDateRange(groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
public class MeetingLocationDTO {
    
    /**
     * 地点ID
     */
    private Integer id;
    
    /**
     * 地点名称
     */
    @NotBlank(message = "地点名称不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 100, message = "地点名称长度不能超过100个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String name;
    
    /**
     * 地点描述
     */
    @Size(max = 500, message = "地点描述长度不能超过500个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String description;
    
    /**
     * 容纳人数
     */
    private Integer capacity;
    
    /**
     * 设施设备描述
     */
    @Size(max = 1000, message = "设施设备描述长度不能超过1000个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String facilities;
    
    /**
     * 开放开始时间
     */
    @NotNull(message = "开放开始时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime openTime;
    
    /**
     * 开放结束时间
     */
    @NotNull(message = "开放结束时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime closeTime;
    
    /**
     * 可用星期（1-7，逗号分隔）
     */
    @NotBlank(message = "可用星期不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String availableDays;

    /**
     * 开放开始日期（可选，NULL表示无限制）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate availableStartDate;

    /**
     * 开放结束日期（可选，NULL表示无限制）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate availableEndDate;

    /**
     * 状态：ACTIVE-启用、INACTIVE-禁用
     */
    @NotBlank(message = "状态不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 开放时间段显示文本（非数据库字段）
     */
    private String openTimeDisplay;
    
    /**
     * 可用天数显示文本（非数据库字段）
     */
    private String availableDaysDisplay;

    /**
     * 日期范围显示文本（非数据库字段）
     */
    private String dateRangeDisplay;
    
    /**
     * 完整显示文本（非数据库字段）
     */
    private String fullDisplay;
}
