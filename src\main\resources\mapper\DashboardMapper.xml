<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.DashboardMapper">

    <!-- 统计员工总数 -->
    <select id="countTotalEmployees" resultType="int">
        SELECT COUNT(*) FROM employee
    </select>

    <!-- 统计在职员工数量 -->
    <select id="countActiveEmployees" resultType="int">
        SELECT COUNT(*) FROM employee WHERE status = 'Active'
    </select>

    <!-- 统计部门总数 -->
    <select id="countTotalDepartments" resultType="int">
        SELECT COUNT(*) FROM department
    </select>

    <!-- 统计职位总数 -->
    <select id="countTotalPositions" resultType="int">
        SELECT COUNT(*) FROM position
    </select>

    <!-- 计算总业绩 -->
    <select id="sumTotalPerformance" resultType="double">
        SELECT COALESCE(SUM(actual_performance), 0) FROM performance
    </select>

    <!-- ==================== 成就相关查询方法 ==================== -->

    <!-- 获取员工第一次获得新客户的时间 -->
    <select id="getFirstClientAchievementDate" parameterType="int" resultType="java.time.LocalDateTime">
        SELECT MIN(create_time) 
        FROM client 
        WHERE employee_id = #{employeeId} AND status = '审核通过'
    </select>

    <!-- 获取员工第一次获得提成的时间（业绩达到1万） -->
    <select id="getFirstCommissionAchievementDate" parameterType="int" resultType="string">
        SELECT MIN(date) 
        FROM performance 
        WHERE employee_id = #{employeeId} AND estimated_performance >= 10000
    </select>

    <!-- 获取员工第一次达到3万业绩的时间 -->
    <select id="getFirst30kPerformanceAchievementDate" parameterType="int" resultType="string">
        SELECT MIN(date) 
        FROM performance 
        WHERE employee_id = #{employeeId} AND estimated_performance >= 30000
    </select>

    <!-- 获取员工的所有业绩记录（按日期升序） -->
    <select id="getPerformanceRecordsForCapability" parameterType="int" resultType="java.util.HashMap">
        SELECT date, estimated_performance
        FROM performance
        WHERE employee_id = #{employeeId}
        ORDER BY date ASC
    </select>

    <!-- 获取员工指定月份的工资信息 -->
    <select id="getSalaryByEmployeeAndDate" resultType="java.util.HashMap">
        SELECT basic_salary, performance_bonus
        FROM salary
        WHERE employee_id = #{employeeId} AND date = #{date}
    </select>

</mapper>
