package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.MeetingLocationDTO;
import org.example.company_management.dto.LocationAvailabilityDTO;
import org.example.company_management.entity.MeetingLocation;
import org.example.company_management.service.MeetingLocationService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议地点管理控制器（管理端）
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/admin/meeting-location")
public class MeetingLocationController {
    
    @Autowired
    private MeetingLocationService meetingLocationService;
    
    /**
     * 创建会议地点
     * 
     * @param meetingLocationDTO 会议地点信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Void> createMeetingLocation(@Validated(ValidationGroups.Add.class) @RequestBody MeetingLocationDTO meetingLocationDTO) {
        meetingLocationService.createMeetingLocation(meetingLocationDTO);
        return Result.success();
    }
    
    /**
     * 根据ID删除会议地点
     * 
     * @param id 地点ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteMeetingLocation(@PathVariable Integer id) {
        meetingLocationService.deleteMeetingLocation(id);
        return Result.success();
    }
    
    /**
     * 更新会议地点
     * 
     * @param id 地点ID
     * @param meetingLocationDTO 会议地点信息
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public Result<Void> updateMeetingLocation(@PathVariable Integer id, 
                                              @Validated(ValidationGroups.Update.class) @RequestBody MeetingLocationDTO meetingLocationDTO) {
        meetingLocationService.updateMeetingLocation(id, meetingLocationDTO);
        return Result.success();
    }
    
    /**
     * 根据ID获取会议地点详情
     * 
     * @param id 地点ID
     * @return 会议地点信息
     */
    @GetMapping("/{id}")
    public Result<MeetingLocation> getMeetingLocationById(@PathVariable Integer id) {
        MeetingLocation meetingLocation = meetingLocationService.getMeetingLocationById(id);
        return Result.success(meetingLocation);
    }
    
    /**
     * 分页查询会议地点列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 地点名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    @GetMapping
    public Result<PageResult<MeetingLocation>> getMeetingLocationPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        PageResult<MeetingLocation> pageResult = meetingLocationService.getMeetingLocationPage(pageNum, pageSize, name, status);
        return Result.success(pageResult);
    }
    
    /**
     * 获取所有启用的会议地点列表
     *
     * @return 启用的会议地点列表
     */
    @GetMapping("/active")
    public Result<List<MeetingLocation>> getActiveLocations() {
        List<MeetingLocation> locations = meetingLocationService.getActiveLocations();
        return Result.success(locations);
    }

    /**
     * 获取所有会议地点列表（包括禁用的）
     *
     * @return 所有会议地点列表
     */
    @GetMapping("/all")
    public Result<List<MeetingLocation>> getAllLocations() {
        List<MeetingLocation> locations = meetingLocationService.getAllLocations();
        return Result.success(locations);
    }

    /**
     * 查询地点可用性信息
     *
     * @param locationId 地点ID
     * @param startDate 查询开始日期时间
     * @param endDate 查询结束日期时间
     * @param excludeMeetingId 排除的会议ID（编辑模式下使用，可选）
     * @return 地点可用性信息
     */
    @GetMapping("/{locationId}/availability")
    public Result<LocationAvailabilityDTO> getLocationAvailability(
            @PathVariable Integer locationId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate,
            @RequestParam(required = false) Long excludeMeetingId) {
        LocationAvailabilityDTO availability = meetingLocationService.getLocationAvailability(
            locationId, startDate, endDate, excludeMeetingId);
        return Result.success(availability);
    }
}
