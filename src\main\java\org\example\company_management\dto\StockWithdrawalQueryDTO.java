package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 股票提现查询DTO
 * 用于封装查询条件
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockWithdrawalQueryDTO {
    
    /**
     * 员工ID
     */
    private Integer employeeId;
    
    /**
     * 员工姓名（模糊查询）
     */
    private String employeeName;
    
    /**
     * 申请状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝
     */
    private String status;
    
    /**
     * 申请开始时间（格式：yyyy-MM-dd）
     */
    private String startDate;
    
    /**
     * 申请结束时间（格式：yyyy-MM-dd）
     */
    private String endDate;
    
    /**
     * 审核人ID
     */
    private Integer auditorId;
    
    /**
     * 页码
     */
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "apply_time";
    
    /**
     * 排序方向：ASC-升序，DESC-降序
     */
    private String orderDirection = "DESC";
}
