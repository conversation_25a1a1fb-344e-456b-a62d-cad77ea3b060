package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 员工费用数据传输对象
 */
@Data
public class EmployeeExpenseDTO {
    // @NotNull(message = "更新时ID不能为空", groups = ValidationGroups.Update.class) // Keep Update group for ID
    // private Long id; // Assuming id is handled by a separate UpdateDTO or different mechanism for create

    // --- Fields for single or batch operations ---
    private Long employeeId; // Optional: for single add

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate expenseDate; // Optional: for single add

    private List<String> expenseMonths; // Optional: for batch add
    
    private List<Long> employeeIds; // Optional: for batch add

    // --- 共用字段 (These can be validated with default group or a common create group) ---
    @NotBlank(message = "项目名称不能为空") // No specific group, will apply if DTO is @Validated
    @Size(max = 255, message = "项目名称长度不能超过255个字符")
    private String itemName;

    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    // --- 显示字段 (由service/mapper填充，用于响应) ---
    private String employeeName;
    private Long departmentId; // 部门ID
    private String departmentName; // 部门名称

    // ID is needed for update operations, but for create, it should not be present.
    // If this DTO is also used for update, the @NotNull on ID with Update group is fine.
    // For create, we assume ID is null or not set by client.
    private Long id;
} 