package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 部门实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Department {
    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门负责人ID（兼容性字段，优先从负责人列表中获取主要负责人）
     */
    private Integer leaderId;

    /**
     * 部门负责人姓名（非数据库字段，兼容性字段）
     */
    private String departmentLeader;

    /**
     * 部门描述
     */
    private String departmentDescription;

    /**
     * 父部门ID
     */
    private Integer parentDepartmentId;

    /**
     * 部门状态(Active/Inactive)
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 员工数量
     */
    private Integer employeeCount;

    // ========== 多负责人相关字段（非数据库字段） ==========

    /**
     * 部门负责人列表（非数据库字段）
     */
    private List<DepartmentLeader> leaders;

    /**
     * 负责人员工ID列表（非数据库字段，用于前端传递）
     */
    private List<Integer> leaderIds;

    /**
     * 主要负责人ID（非数据库字段，用于前端传递）
     */
    private Integer primaryLeaderId;

    /**
     * 负责人姓名列表（非数据库字段，用于前端显示）
     */
    private List<String> leaderNames;

    /**
     * 主要负责人姓名（非数据库字段，用于前端显示）
     */
    private String primaryLeaderName;

    // ========== 业务方法 ==========

    /**
     * 获取主要负责人ID（兼容性方法）
     * 优先从负责人列表中获取，如果没有则返回leaderId字段
     * @return 主要负责人ID
     */
    public Integer getEffectiveLeaderId() {
        if (leaders != null && !leaders.isEmpty()) {
            for (DepartmentLeader leader : leaders) {
                if ("PRIMARY".equals(leader.getLeaderRole()) && Boolean.TRUE.equals(leader.getIsActive())) {
                    return leader.getEmployeeId();
                }
            }
        }
        return leaderId;
    }

    /**
     * 获取主要负责人姓名（兼容性方法）
     * 优先从负责人列表中获取，如果没有则返回departmentLeader字段
     * @return 主要负责人姓名
     */
    public String getEffectiveLeaderName() {
        if (leaders != null && !leaders.isEmpty()) {
            for (DepartmentLeader leader : leaders) {
                if ("PRIMARY".equals(leader.getLeaderRole()) && Boolean.TRUE.equals(leader.getIsActive())) {
                    return leader.getLeaderName();
                }
            }
        }
        return departmentLeader;
    }

    /**
     * 获取所有激活状态的负责人ID列表
     * @return 负责人ID列表
     */
    public List<Integer> getActiveLeaderIds() {
        if (leaders == null || leaders.isEmpty()) {
            return List.of();
        }
        return leaders.stream()
                .filter(leader -> Boolean.TRUE.equals(leader.getIsActive()))
                .map(DepartmentLeader::getEmployeeId)
                .toList();
    }

    /**
     * 获取所有激活状态的负责人姓名列表
     * @return 负责人姓名列表
     */
    public List<String> getActiveLeaderNames() {
        if (leaders == null || leaders.isEmpty()) {
            return List.of();
        }
        return leaders.stream()
                .filter(leader -> Boolean.TRUE.equals(leader.getIsActive()))
                .map(DepartmentLeader::getLeaderName)
                .toList();
    }
}