package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.MeetingDTO;
import org.example.company_management.entity.Meeting;
import org.example.company_management.service.MeetingService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会议管理控制器（管理端）
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@RestController
@RequestMapping("/admin/meeting")
public class MeetingController {
    
    @Autowired
    private MeetingService meetingService;
    
    /**
     * 创建会议
     * 
     * @param meetingDTO 会议信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Void> createMeeting(@Validated(ValidationGroups.Add.class) @RequestBody MeetingDTO meetingDTO) {
        meetingService.createMeeting(meetingDTO);
        return Result.success();
    }
    
    /**
     * 更新会议信息
     * 
     * @param id 会议ID
     * @param meetingDTO 会议信息
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public Result<Void> updateMeeting(@PathVariable Long id, 
                                      @Validated(ValidationGroups.Update.class) @RequestBody MeetingDTO meetingDTO) {
        meetingDTO.setId(id);
        meetingService.updateMeeting(meetingDTO);
        return Result.success();
    }
    
    /**
     * 删除会议
     * 
     * @param id 会议ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteMeeting(@PathVariable Long id) {
        meetingService.deleteMeeting(id);
        return Result.success();
    }
    
    /**
     * 获取会议详情
     * 
     * @param id 会议ID
     * @return 会议信息
     */
    @GetMapping("/{id}")
    public Result<Meeting> getMeetingById(@PathVariable Long id) {
        Meeting meeting = meetingService.getMeetingById(id);
        return Result.success(meeting);
    }
    
    /**
     * 分页查询会议列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param title 会议主题（可选）
     * @param status 会议状态（可选）
     * @param creatorId 发起人ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @GetMapping
    public Result<PageResult<Meeting>> getMeetingPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Integer creatorId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        PageResult<Meeting> pageResult = meetingService.getMeetingPage(
            pageNum, pageSize, title, status, creatorId, startTime, endTime);
        return Result.success(pageResult);
    }
    
    // 状态更新接口已移除，现在由系统定时任务根据时间自动管理
}
