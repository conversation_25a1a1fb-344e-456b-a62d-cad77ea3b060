package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议实体类
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Meeting {
    
    /**
     * 会议ID
     */
    private Long id;
    
    /**
     * 会议主题
     */
    private String title;
    
    /**
     * 会议内容描述
     */
    private String content;
    
    /**
     * 会议开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    /**
     * 会议地点（文本描述，保留兼容性）
     */
    private String location;

    /**
     * 会议地点ID
     */
    private Integer locationId;

    /**
     * 会议地点信息（非数据库字段）
     */
    private MeetingLocation locationInfo;

    /**
     * 发起人ID（管理员，保留原有功能）
     */
    private Integer creatorId;

    /**
     * 发起人姓名（非数据库字段）
     */
    private String creatorName;

    /**
     * 会议负责人ID
     */
    private Integer responsibleId;

    /**
     * 会议负责人姓名（非数据库字段）
     */
    private String responsibleName;
    
    /**
     * 会议状态：NOT_STARTED-未开始、IN_PROGRESS-进行中、FINISHED-已结束
     * 注意：此字段由查询时实时计算，不存储在数据库中
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 参与者列表（非数据库字段）
     */
    private List<MeetingParticipant> participants;
    
    /**
     * 会议总结列表（非数据库字段）
     */
    private List<MeetingSummary> summaries;
    
    /**
     * 参与者数量（非数据库字段）
     */
    private Integer participantCount;
    
    /**
     * 总结数量（非数据库字段）
     */
    private Integer summaryCount;
}
