package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

import java.time.LocalDateTime;

/**
 * 会议总结数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingSummaryDTO {
    
    /**
     * 总结ID
     */
    private Long id;
    
    /**
     * 会议ID
     */
    private Long meetingId;
    
    /**
     * 总结人ID
     */
    private Integer employeeId;
    
    /**
     * 总结人姓名（非数据库字段）
     */
    private String employeeName;
    
    /**
     * 总结人部门（非数据库字段）
     */
    private String departmentName;
    
    /**
     * 总结内容
     */
    @NotBlank(message = "总结内容不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(min = 1, message = "总结内容至少需要1个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 5000, message = "总结内容长度不能超过5000个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String summaryContent;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
