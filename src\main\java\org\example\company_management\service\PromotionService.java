package org.example.company_management.service;

import org.example.company_management.dto.PromotionDTO;
import org.example.company_management.entity.Promotion;
import org.example.company_management.utils.ImageUploadUtil;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 推广管理业务逻辑接口}}
 * {{CHENGQI: 任务ID: P4-LD-006}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 描述: 推广管理业务逻辑接口，定义核心业务方法}}
 */
public interface PromotionService {

    /**
     * 创建推广
     *
     * @param promotionDTO 推广DTO
     * @return 操作结果
     */
    Result<PromotionDTO> createPromotion(PromotionDTO promotionDTO);

    /**
     * 更新推广
     *
     * @param id 推广ID
     * @param promotionDTO 推广DTO
     * @return 操作结果
     */
    Result<PromotionDTO> updatePromotion(Long id, PromotionDTO promotionDTO);

    /**
     * 删除推广
     *
     * @param id 推广ID
     * @return 操作结果
     */
    Result<Void> deletePromotion(Long id);

    /**
     * 根据ID获取推广详情
     *
     * @param id 推广ID
     * @return 推广详情
     */
    Result<PromotionDTO> getPromotionById(Long id);



    /**
     * 获取我的推广列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param status 状态筛选（可选）
     * @param titleKeyword 标题关键词（可选）
     * @param createTimeStart 创建时间开始（可选）
     * @param createTimeEnd 创建时间结束（可选）
     * @return 分页结果
     */
    Result<PageResult<PromotionDTO>> getMyPromotions(Integer pageNum, Integer pageSize, String status, String titleKeyword, LocalDateTime createTimeStart, LocalDateTime createTimeEnd);

    /**
     * 获取已通过的推广列表（公开展示）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param promotionDTO 查询条件
     * @return 分页结果
     */
    Result<PageResult<PromotionDTO>> getApprovedPromotions(Integer pageNum, Integer pageSize, PromotionDTO promotionDTO);

    /**
     * 获取待审核推广列表（部门主管使用）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Result<PageResult<PromotionDTO>> getPendingAuditPromotions(Integer pageNum, Integer pageSize, PromotionDTO queryDTO);

    // {{FUTURE_EXTENSION: 提交审核接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于工作流优化，当前通过auditPromotion实现审核}}
    /**
     * 提交审核
     *
     * @param id 推广ID
     * @return 操作结果
     */
    Result<Void> submitForAudit(Long id);

    // {{FUTURE_EXTENSION: 推广状态修改接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于批量状态管理或管理员直接状态修改}}
    /**
     * 修改推广状态
     *
     * @param id 推广ID
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> updatePromotionStatus(Long id, String status);

    /**
     * 审核推广
     *
     * @param id 推广ID
     * @param promotionDTO 审核信息（包含审核状态和拒绝理由）
     * @return 操作结果
     */
    Result<Void> auditPromotion(Long id, PromotionDTO promotionDTO);

    /**
     * 上传单个图片
     *
     * @param file 图片文件
     * @return 上传结果
     */
    Result<String> uploadImage(MultipartFile file);

    // {{FUTURE_EXTENSION: 批量图片上传接口 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前使用单图片上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用 uploadImage 进行单图片上传

    // {{FUTURE_EXTENSION: 图片删除接口 - 删除}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 当前图片管理通过富文本编辑器处理，独立删除功能未使用}}
    // 已删除 - 图片删除通过富文本编辑器内部处理

    // {{FUTURE_EXTENSION: 权限检查接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于复杂权限控制场景，当前通过前端计算属性实现}}
    /**
     * 检查用户是否有权限操作推广
     *
     * @param promotionId 推广ID
     * @param operation 操作类型（view, edit, delete, audit）
     * @return 是否有权限
     */
    boolean hasPermission(Long promotionId, String operation);

    // {{FUTURE_EXTENSION: 推广统计接口 - 保留用于未来扩展}}
    // {{CREATED: 2025-06-13 10:30:14 +08:00}}
    // {{REASON: 可能用于管理后台的数据统计和分析功能}}
    /**
     * 获取推广统计信息
     *
     * @param authorId 作者ID（可选，为空则统计当前用户）
     * @return 统计结果
     */
    Result<java.util.Map<String, Object>> getPromotionStatistics(Integer authorId);

    /**
     * 将临时附件转换为正式附件
     *
     * @param promotionId 推广ID
     * @param tempAttachmentPaths 临时附件路径列表
     * @return 操作结果
     */
    Result<Void> convertTempAttachments(Long promotionId, List<String> tempAttachmentPaths);
}

// {{CHENGQI: 推广管理业务逻辑接口创建完成}}
