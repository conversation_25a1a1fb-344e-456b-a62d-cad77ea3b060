<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    CopyDocument,
} from '@element-plus/icons-vue';
import { getDepartmentList } from '@/api/department';
import {
    getDepartmentExpensePage,
    updateDepartmentExpense,
    deleteDepartmentExpense,
    batchDeleteDepartmentExpense,
    getDepartmentExpenseById,
    addBatchDepartmentExpense,
    extendBatchDepartmentExpense
} from '@/api/departmentExpense';
import BatchExtendDialog from '@/components/BatchExtendDialog.vue';

// Data
const departmentExpenseData = ref([]);
const loading = ref(true);
const searchDepartmentId = ref(null);
const searchItemName = ref('');
const searchMonth = ref('');

// Department list for dropdowns
const departmentList = ref([]);
const loadingDepartments = ref(false);

// Selected records
const selectedExpenses = ref([]);

// Pagination
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0,
});

// Dialog control
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' or 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// Form data
const form = reactive({
    id: null,
    departmentId: null,
    expenseDate: '',
    departmentIds: [],
    selectedMonths: [],
    itemName: '',
    amount: null,
    remark: '',
});

// NEW: Month options for el-select in batch add mode
const monthOptions = ref([]);

const generateMonthOptions = () => {
    const optionsMap = new Map(); // Using a Map to store unique month strings
    const currentDate = new Date();

    // Generate past 12 months (current month and 11 previous months)
    for (let i = 0; i < 12; i++) {
        const targetDate = new Date(currentDate);
        targetDate.setDate(1); // Normalize to the 1st of the month
        targetDate.setMonth(currentDate.getMonth() - i);

        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1; // getMonth() is 0-indexed
        const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
        // Map ensures uniqueness if monthStr is somehow regenerated, though loop logic should prevent it here.
        if (!optionsMap.has(monthStr)) {
            optionsMap.set(monthStr, { value: monthStr, label: monthStr });
        }
    }

    // Generate future 12 months (next 12 months, current month already included in past loop)
    for (let i = 1; i <= 12; i++) { // i starts from 1 to get NEXT months
        const targetDate = new Date(currentDate);
        targetDate.setDate(1); // Normalize to the 1st of the month
        targetDate.setMonth(currentDate.getMonth() + i);

        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1; // getMonth() is 0-indexed
        const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
        if (!optionsMap.has(monthStr)) {
            optionsMap.set(monthStr, { value: monthStr, label: monthStr });
        }
    }

    // Convert Map values to an array and sort them
    // Sorting descending (e.g., 2023-12, 2023-11, ...)
    monthOptions.value = Array.from(optionsMap.values())
        .sort((a, b) => b.value.localeCompare(a.value));
};

// --- Refs for new BatchExtendDialog ---
const batchExtendDialogVisible = ref(false);
const preparedItemsForDialog = ref([]);
const batchExtendDialogRef = ref(null);

const departmentExpenseDisplayFields = ref([
    { label: '部门', prop: 'departmentName', minWidth: '120px' },
    { label: '原开销年月', prop: 'originalExpenseDate', width: '110px', align: 'center' },
    { label: '项目名称', prop: 'itemName', minWidth: '150px' },
    { label: '金额', prop: 'amount', width: '100px', align: 'right', isCurrency: true },
    { label: '备注', prop: 'remark', minWidth: '130px' }
]);
// --- End Refs for new BatchExtendDialog ---

// Form rules
const rules = reactive({
    departmentId: [{ required: true, message: '请选择部门', trigger: 'change' }],
    expenseDate: [{ required: true, message: '请选择开销月份', trigger: 'change' }],
    departmentIds: [
        { type: 'array', required: true, message: '请至少选择一个部门', trigger: 'change' },
        { validator: (rule, value, callback) => {
                if (!value || value.length === 0) {
                    callback(new Error('请至少选择一个部门'));
                } else {
                    callback();
                }
            }, trigger: 'change'
        }
    ],
    selectedMonths: [
        { type: 'array', required: true, message: '请至少选择一个开销月份', trigger: 'change' },
        { validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
                callback(new Error('请至少选择一个开销月份'));
            } else {
                callback();
            }
          }, trigger: 'change'
        }
    ],
    itemName: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
        { max: 255, message: '项目名称不能超过255个字符', trigger: 'blur' },
    ],
    amount: [
        { required: true, message: '请输入金额', trigger: 'blur' },
        { validator: (rule, value, callback) => {
            if (value === null || value === undefined) callback(new Error('金额不能为空'));
            else callback();
        }, trigger: 'blur' }
    ],
    remark: [{ max: 255, message: '备注不能超过255个字符', trigger: 'blur' }],
});

// Load data
const loadExpenseData = async () => {
    loading.value = true;
    try {
        let startDate = null;
        let endDate = null;
        if (searchMonth.value) {
            const year = searchMonth.value.substring(0, 4);
            const month = searchMonth.value.substring(5, 7);
            startDate = `${year}-${month}-01`;
            const lastDay = new Date(year, month, 0).getDate(); // Last day of the month
            endDate = `${year}-${month}-${lastDay}`;
        }

        const params = {
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            departmentId: searchDepartmentId.value || undefined,
            description: searchItemName.value || undefined,
            startDate: startDate || undefined,
            endDate: endDate || undefined,
        };
        const res = await getDepartmentExpensePage(params);
        if (res.code === 200 && res.data) {
            departmentExpenseData.value = res.data.records || res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取部门开销数据失败');
        }
    } catch (error) {
        console.error("Failed to load department expenses:", error);
        ElMessage.error('加载部门开销数据失败: ' + (error.message || '网络错误'));
    } finally {
        loading.value = false;
    }
};

// Reset form
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.departmentId = null;
    form.expenseDate = '';
    form.departmentIds = [];
    form.selectedMonths = [];
    form.itemName = '';
    form.amount = null;
    form.remark = '';
};

// Close dialog
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// Open add dialog (Batch Add)
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    if (departmentList.value.length === 0) loadDepartmentListForDropdowns();
    dialogVisible.value = true;
};

// Open edit dialog (Single Edit)
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();
    if (departmentList.value.length === 0) await loadDepartmentListForDropdowns();

    formLoading.value = true;
    try {
        const res = await getDepartmentExpenseById(row.id);
        if (res.code === 200 && res.data) {
            form.id = res.data.id;
            form.departmentId = res.data.departmentId;
            form.expenseDate = res.data.expenseDate ? res.data.expenseDate.substring(0, 7) : '';
            form.itemName = res.data.itemName;
            form.amount = Number(res.data.amount) || null;
            form.remark = res.data.remark || '';
            form.departmentIds = [];
            form.selectedMonths = [];
        } else {
            ElMessage.error(res.message || '获取开销详情失败');
            closeDialog();
            return;
        }
    } catch (error) {
        console.error("Failed to fetch expense details for edit:", error);
        ElMessage.error('获取开销详情失败: ' + (error.message || '网络错误'));
        closeDialog();
        return;
    } finally {
        formLoading.value = false;
    }
    dialogVisible.value = true;
};

// Confirm delete
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除部门 "${row.departmentName}" 在 "${row.expenseDate}" 关于 "${row.itemName || row.description}" 的开销记录吗？`,
        '删除确认',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    ).then(async () => {
        loading.value = true;
        try {
            const res = await deleteDepartmentExpense(row.id);
            if (res.code === 200) {
                ElMessage.success('删除成功');
                loadExpenseData();
            } else {
                ElMessage.error(res.message || '删除失败');
            }
        } catch (error) {
            ElMessage.error('删除操作失败: ' + (error.message || '网络错误'));
        } finally {
            loading.value = false;
        }
    }).catch(() => { /* User cancelled */ });
};

// Batch delete
const handleBatchDelete = () => {
    if (selectedExpenses.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }
    const ids = selectedExpenses.value.map((item) => item.id);
    ElMessageBox.confirm(
        `确定要批量删除选中的 ${ids.length} 条开销记录吗？`,
        '批量删除确认',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    ).then(async () => {
        loading.value = true;
        try {
            const res = await batchDeleteDepartmentExpense(ids);
            if (res.code === 200) {
                ElMessage.success('批量删除成功');
                selectedExpenses.value = [];
                loadExpenseData();
            } else {
                ElMessage.error(res.message || '批量删除失败');
            }
        } catch (error) {
            ElMessage.error('批量删除操作失败: ' + (error.message || '网络错误'));
        } finally {
            loading.value = false;
        }
    }).catch(() => { /* User cancelled */ });
};

// Submit form
const submitForm = async (formEl) => {
    if (!formEl) return;
    
    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                let res;
                const dto = {}; 

                if (dialogType.value === 'add') { // Batch Add
                    dto.departmentIds = form.departmentIds;
                    dto.expenseMonths = form.selectedMonths;
                    dto.itemName = form.itemName;
                    dto.amount = Number(form.amount);
                    dto.remark = form.remark;
                    
                    res = await addBatchDepartmentExpense(dto);
                } else { // 'edit' mode (Single Record Update)
                    dto.id = form.id;
                    dto.departmentId = form.departmentId;
                    dto.expenseDate = form.expenseDate ? `${form.expenseDate}-01` : null;
                    dto.itemName = form.itemName;
                    dto.amount = Number(form.amount);
                    dto.remark = form.remark;

                    res = await updateDepartmentExpense(dto);
                }

                if (res.code === 200) {
                    ElMessage.success(dialogType.value === 'edit' ? '更新成功' : '批量添加成功');
                    dialogVisible.value = false;
                    loadExpenseData(); 
                } else {
                    ElMessage.error(res.message || (dialogType.value === 'edit' ? '更新失败' : '批量添加失败'));
                }
            } catch (error) {
                console.error("Submit form error:", error);
                let errorMsg = '提交操作失败';
                if (error.response && error.response.data && error.response.data.message) {
                    errorMsg = error.response.data.message;
                } else if (error.message) {
                    errorMsg = error.message;
                }
                ElMessage.error(errorMsg);
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

// Table selection change
const handleSelectionChange = (selection) => {
    selectedExpenses.value = selection;
};

// Search
const handleSearch = () => {
    pagination.pageNum = 1;
    loadExpenseData();
};

// Reset search
const handleReset = () => {
    searchDepartmentId.value = null;
    searchItemName.value = '';
    searchMonth.value = '';
    pagination.pageNum = 1;
    loadExpenseData();
};

// Page number change
const handleCurrentChange = (page) => {
    pagination.pageNum = page;
    loadExpenseData();
};

// Page size change
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    pagination.pageNum = 1;
    loadExpenseData();
};

// Format currency
const formatCurrency = (value) => {
    if (value === null || value === undefined || isNaN(parseFloat(value))) return '¥0.00';
    return '¥' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Format date for display (assuming createTime/updateTime are full ISO strings)
const formatDate = (dateTimeString) => {
    if (!dateTimeString) return '-';
    try {
        const date = new Date(dateTimeString);
        if (isNaN(date.getTime())) return '-'; // Invalid date
        return date.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
    } catch (e) {
        return '-';
    }
};

const loadDepartmentListForDropdowns = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200 && Array.isArray(res.data)) {
            departmentList.value = res.data.map(dep => ({
                departmentId: dep.id || dep.departmentId,
                departmentName: dep.name || dep.departmentName
            }));
        } else if (res.code === 200 && res.data && Array.isArray(res.data.list)) {
            departmentList.value = res.data.list.map(dep => ({
                departmentId: dep.id || dep.departmentId,
                departmentName: dep.name || dep.departmentName
            }));
        } else {
            ElMessage.error(res.message || '获取部门列表失败');
            departmentList.value = [];
        }
    } catch (error) {
        console.error("Failed to load departments for dropdown:", error);
        ElMessage.error('加载部门列表失败: ' + (error.message || '网络错误'));
        departmentList.value = [];
    } finally {
        loadingDepartments.value = false;
    }
};

// --- New Batch Extend Functions using BatchExtendDialog.vue ---
const handleOpenBatchExtendDialog_Dept = () => {
    if (selectedExpenses.value.length === 0) {
        ElMessage.warning('请至少选择一条开销记录进行延用');
        return;
    }

    const uniqueCheckSet = new Set();
    for (const item of selectedExpenses.value) {
        const key = `${item.departmentId}_${item.itemName}`;
        if (uniqueCheckSet.has(key)) {
            ElMessage.error('检测到对相同部门的相同项目选择了多个原始记录。请一次只为一种"部门-项目"组合进行延用，或分别操作。');
            return;
        }
        uniqueCheckSet.add(key);
    }

    preparedItemsForDialog.value = selectedExpenses.value.map(item => ({
        id: item.id, // itemKeyField for BatchExtendDialog
        departmentId: item.departmentId,
        departmentName: item.departmentName,
        originalExpenseDate: item.expenseDate ? item.expenseDate.substring(0, 7) : '-', // For display
        itemName: item.itemName,
        amount: Number(item.amount) || 0,
        remark: item.remark || ''
    }));
    
    // Ensure monthOptions are available (already generated onMounted)
    if (monthOptions.value.length === 0) {
         generateMonthOptions(); 
    }
    batchExtendDialogVisible.value = true;
};

const handleConfirmBatchExtend_Dept = async (payload) => {
    if (!batchExtendDialogRef.value) {
        console.error("BatchExtendDialog ref is not available for DepartmentExpense");
        ElMessage.error('批量延用组件引用错误');
        return;
    }

    const payloadItems = [];
    payload.selectedItems.forEach(item => {
        payload.targetMonths.forEach(monthYYYYMM => {
            const targetDate = `${monthYYYYMM}-01`; // Backend expects YYYY-MM-DD
            payloadItems.push({
                departmentId: item.departmentId,
                // departmentName: item.departmentName, // Optional for backend
                expenseDate: targetDate,
                itemName: item.itemName,
                amount: item.amount,
                remark: item.remark
            });
        });
    });

    if (payloadItems.length === 0) {
         ElMessage.warning('未能构建任何有效的延用数据');
         if (batchExtendDialogRef.value && typeof batchExtendDialogRef.value.resetLoadingState === 'function') {
            batchExtendDialogRef.value.resetLoadingState();
        }
         return;
    }
    
    try {
        const res = await extendBatchDepartmentExpense({ items: payloadItems }); // Actual API call

        if (res.code === 200 && res.data) {
            const { successfullyExtendedItems, skippedDuplicateItems } = res.data;
            const succeededCount = successfullyExtendedItems ? successfullyExtendedItems.length : 0;
            const skippedCount = skippedDuplicateItems ? skippedDuplicateItems.length : 0;

            const primaryMessage = `批量延用处理完成：成功 ${succeededCount} 项，失败/跳过 ${skippedCount} 项。`;

            if (succeededCount > 0 && skippedCount === 0) {
                ElMessage.success(primaryMessage);
            } else if (succeededCount > 0 && skippedCount > 0) {
                 ElMessage.warning(primaryMessage);
            } else if (succeededCount === 0 && skippedCount > 0) {
                 ElMessage.warning(primaryMessage);
            } else if (succeededCount === 0 && skippedCount === 0 && payloadItems.length > 0) {
                 ElMessage.warning(`批量延用尝试处理 ${payloadItems.length} 项，但均未成功也未被标记为跳过。`);
            }
             else { // No items processed or an unusual case
                ElMessage.info(res.message || '批量延用已处理，但未返回明确的成功或跳过信息。');
            }


            if (skippedCount > 0 && skippedDuplicateItems) {
                let skippedDetails = "<strong>被跳过或延用失败的项目详情：</strong><br/>";
                skippedDuplicateItems.forEach(itemMsg => {
                    skippedDetails += `- ${itemMsg}<br/>`;
                });
                ElMessageBox.alert(skippedDetails, '跳过项目详情', {
                    confirmButtonText: '我知道了',
                    type: 'warning',
                    dangerouslyUseHTMLString: true, 
                });
            }

            batchExtendDialogVisible.value = false; 
            loadExpenseData(); 
            selectedExpenses.value = []; 
        } else {
            ElMessage.error(res.message || '批量延用失败，未返回有效数据');
        }
    } catch (error) {
        console.error("批量延用部门开销失败:", error);
        ElMessage.error('批量延用操作失败: ' + (error.message || '网络错误'));
    } finally {
        if (batchExtendDialogRef.value && typeof batchExtendDialogRef.value.resetLoadingState === 'function') {
            batchExtendDialogRef.value.resetLoadingState();
        }
    }
};
// --- End New Batch Extend Functions ---

onMounted(() => {
    loadDepartmentListForDropdowns();
    loadExpenseData();
    generateMonthOptions(); // Generate month options on mount
});
</script>

<template>
    <div class="expense-container">
        <!-- Search Toolbar -->
        <div class="toolbar">
            <div class="search-box">
                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门筛选"
                    clearable
                    filterable
                    :loading="loadingDepartments"
                    @change="handleSearch"
                >
                    <el-option v-for="item in departmentList" :key="item.departmentId" :label="item.departmentName" :value="item.departmentId" />
                </el-select>
                <el-input
                    v-model="searchItemName"
                    placeholder="搜索项目名称"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix><el-icon><Search /></el-icon></template>
                </el-input>
                <el-date-picker
                    v-model="searchMonth"
                    type="month"
                    placeholder="选择开销月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    clearable
                    @change="handleSearch"
                />
                <el-button type="primary" @click="handleSearch"><el-icon><Search /></el-icon>搜索</el-button>
                <el-button @click="handleReset"><el-icon><RefreshRight /></el-icon>重置</el-button>
            </div>
            <div class="action-box">
                <el-button type="danger" :disabled="selectedExpenses.length === 0" @click="handleBatchDelete">
                    <el-icon><Delete /></el-icon>批量删除
                </el-button>
                <el-button type="primary" class="add-btn" @click="handleAdd">
                    <el-icon><Plus /></el-icon>添加开销
                </el-button>
                <el-button
                    type="success" 
                    :disabled="selectedExpenses.length === 0"
                    @click="handleOpenBatchExtendDialog_Dept"
                >
                    <el-icon><CopyDocument /></el-icon>延用选中开销
                </el-button>
            </div>
        </div>

        <!-- Expense Table -->
        <el-table
            v-loading="loading"
            :data="departmentExpenseData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="部门" prop="departmentName" min-width="150" show-overflow-tooltip />
            <el-table-column label="年月" prop="expenseDate" width="120" align="center">
                <template #default="{ row }"><el-tag type="info">{{ row.expenseDate ? row.expenseDate.substring(0, 7) : '-' }}</el-tag></template>
            </el-table-column>
            <el-table-column label="项目名称" prop="itemName" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">{{ row.itemName || '-' }}</template>
            </el-table-column>
            <el-table-column label="金额" prop="amount" min-width="120" align="right" show-overflow-tooltip>
                <template #default="{ row }">{{ formatCurrency(row.amount) }}</template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" min-width="180" show-overflow-tooltip>
                <template #default="{ row }">{{ row.remark || '-' }}</template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" width="180" align="center" show-overflow-tooltip>
                <template #default="{ row }">{{ formatDate(row.createTime) }}</template>
            </el-table-column>
            <el-table-column label="更新时间" prop="updateTime" width="180" align="center" show-overflow-tooltip>
                <template #default="{ row }">{{ formatDate(row.updateTime) }}</template>
            </el-table-column>
            <el-table-column label="操作" width="190" align="center" fixed="right" class-name="operation-column">
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button class="edit-btn" @click="handleEdit(row)" title="编辑"><el-icon><Edit /></el-icon></el-button>
                        <el-button class="delete-btn" @click="handleDelete(row)" title="删除"><el-icon><Delete /></el-icon></el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.pageNum"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- Add/Edit Dialog -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加部门开销' : '编辑部门开销'" 
            width="500px"
            destroy-on-close
            class="custom-dialog"
            :close-on-click-modal="false"
        >
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="dialog-form">
                <!-- Department Selection -->
                <el-form-item v-if="dialogType === 'add'" label="所属部门" prop="departmentIds">
                    <el-select 
                        v-model="form.departmentIds" 
                        placeholder="请选择一个或多个部门" 
                        style="width: 100%" 
                        :loading="loadingDepartments" 
                        filterable 
                        multiple 
                        clearable
                    >
                        <el-option v-for="item in departmentList" :key="item.departmentId" :label="item.departmentName" :value="item.departmentId" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="dialogType === 'edit'" label="所属部门" prop="departmentId">
                    <el-select v-model="form.departmentId" placeholder="请选择部门" style="width: 100%" :loading="loadingDepartments" filterable>
                        <el-option v-for="item in departmentList" :key="item.departmentId" :label="item.departmentName" :value="item.departmentId" />
                    </el-select>
                </el-form-item>

                <!-- Expense Date/Month Selection -->
                <el-form-item v-if="dialogType === 'add'" label="开销月份" prop="selectedMonths">
                    <el-select
                        v-model="form.selectedMonths"
                        multiple
                        filterable
                        placeholder="请选择一个或多个开销月份"
                        style="width: 100%"
                        clearable
                    >
                        <el-option
                            v-for="item in monthOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="dialogType === 'edit'" label="开销月份" prop="expenseDate">
                    <el-date-picker
                        v-model="form.expenseDate"
                        type="month"
                        placeholder="选择开销月份"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                        :disabled="true" 
                    />
                </el-form-item>

                <!-- Common Fields: Item Name, Amount, Remark -->
                <el-form-item label="项目名称" prop="itemName">
                    <el-input v-model="form.itemName" placeholder="请输入项目名称" />
                </el-form-item>
                <el-form-item label="金额" prop="amount">
                    <el-input-number v-model="form.amount" :precision="2" :step="100" style="width: 100%" controls-position="right" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息 (可选)" maxlength="255" show-word-limit />
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" :loading="formLoading" @click="submitForm(formRef)">确定</el-button>
            </div>
        </el-dialog>

        <!-- New BatchExtendDialog -->
        <BatchExtendDialog
            ref="batchExtendDialogRef"
            v-model:isVisible="batchExtendDialogVisible"
            :items-to-extend="preparedItemsForDialog"
            :month-options="monthOptions"
            entity-name="部门开销"
            item-key-field="id" 
            :item-display-fields="departmentExpenseDisplayFields"
            @submit-extend="handleConfirmBatchExtend_Dept"
        />
    </div>
</template>

<style scoped>
/* Styles are copied from Salary.vue for consistency */
.expense-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px); /* Adjust based on your layout's header/footer */
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.search-box .el-input,
.search-box .el-select,
.search-box .el-date-picker {
    width: 200px; /* Adjust as needed */
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff; /* Ensure it's above the table if overlapping */
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1; /* Ensure it's above other elements */
}

:deep(.custom-table) {
    margin-bottom: 60px; /* Space for pagination */
    border-radius: 6px;
    overflow: hidden; /* For rounded corners on table */
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto; /* Allow horizontal scroll if content overflows */
}
:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}


:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center; /* Default alignment, can be overridden per column */
}
:deep(.el-table th.el-table__cell>.cell) { /* Header text center */
    text-align: center;
}


:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Operation column specific styling */
:deep(.operation-column) {
    background-color: #f9f9f9; /* Light background for fixed column */
}
.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}
.edit-btn, .delete-btn {
    border: none;
    width: 32px; /* Fixed size for icon buttons */
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}
.edit-btn { background-color: #e6f1fc; color: #409eff; }
.edit-btn:hover { background-color: #409eff; color: white; }
.delete-btn { background-color: #ffebec; color: #f56c6c; }
.delete-btn:hover { background-color: #f56c6c; color: white; }


/* Dialog Styles */
.custom-dialog { /* Applied to el-dialog */
    border-radius: 8px;
}
.dialog-form { /* Applied to el-form within dialog */
    padding: 0 20px;
    max-height: 60vh; /* Or other suitable max height */
    overflow-y: auto;
}
.dialog-form::-webkit-scrollbar { width: 6px; }
.dialog-form::-webkit-scrollbar-thumb { background-color: #c0c4cc; border-radius: 3px; }
.dialog-form::-webkit-scrollbar-track { background-color: #f5f7fa; }

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px; /* Spacing above footer */
    padding: 0 20px 10px 20px; /* Consistent with form padding */
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0; /* Reset potential inherited margin */
}
:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0px; /* Top/bottom padding, no horizontal for form to manage */
}
:deep(.custom-dialog .el-dialog__footer) {
    display: none; /* Using custom footer div */
}

/* Input styling within dialog for a cleaner look */
:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select .el-input__wrapper),
:deep(.custom-dialog .el-input-number) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}
:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select .el-input__wrapper:hover),
:deep(.custom-dialog .el-input-number:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}
:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-input-number.is-focus) {
     box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}
</style> 