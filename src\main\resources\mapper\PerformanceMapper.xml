<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.PerformanceMapper">

    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.Performance">
        <id column="id" property="id"/>
        <result column="employee_id" property="employeeId"/>
        <result column="date" property="date"/>
        <result column="estimated_performance" property="estimatedPerformance"/>
        <result column="actual_performance" property="actualPerformance"/>
        <result column="name" property="employeeName"/>
        <result column="department_name" property="department"/>
        <result column="position_name" property="position"/>
        <result column="total_salary" property="totalSalary"/>
        <result column="total_petty_cash" property="totalPettyCash"/>
        <result column="employee_department_id" property="employeeDepartmentId"/>
        <result column="monthly_capability_value" property="monthlyCapabilityValue"/>
    </resultMap>

    <!-- 根据主键查询业绩记录 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT p.id,
               p.employee_id,
               p.date,
               p.estimated_performance,
               p.actual_performance,
               e.name,
               d.department_name,
               pos.position_name,
               s.total_salary
        FROM performance p
                 LEFT JOIN employee e ON p.employee_id = e.employee_id
                 LEFT JOIN department d ON e.department_id = d.department_id
                 LEFT JOIN position pos ON e.position_id = pos.position_id
                 LEFT JOIN salary s ON p.employee_id = s.employee_id AND p.date = s.date
        WHERE p.id = #{id}
    </select>

    <!-- 查询所有业绩记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT p.id,
               p.employee_id,
               p.date,
               p.estimated_performance,
               p.actual_performance,
               e.name,
               d.department_name,
               pos.position_name,
               s.total_salary
        FROM performance p
                 LEFT JOIN employee e ON p.employee_id = e.employee_id
                 LEFT JOIN department d ON e.department_id = d.department_id
                 LEFT JOIN position pos ON e.position_id = pos.position_id
                 LEFT JOIN salary s ON p.employee_id = s.employee_id AND p.date = s.date
        ORDER BY p.date DESC
    </select>

    <!-- 根据条件查询业绩记录 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        p.id,
        p.employee_id,
        p.date,
        p.estimated_performance,
        p.actual_performance,
        e.name,
        d.department_name,
        e.department_id AS employee_department_id,
        pos.position_name,
        s.total_salary,
        (
            SELECT COALESCE(SUM(pc.amount), 0)
            FROM petty_cash pc
            WHERE pc.employee_id = e.employee_id
            AND pc.date = p.date
            AND pc.status != '已拒绝'
        ) AS total_petty_cash
        FROM performance p
        LEFT JOIN employee e ON p.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN position pos ON e.position_id = pos.position_id
        LEFT JOIN salary s ON p.employee_id = s.employee_id AND p.date = s.date
        <where>
            <!-- 根据员工ID筛选 -->
            <if test="employeeId != null">
                AND p.employee_id = #{employeeId}
            </if>
            <!-- 根据员工姓名模糊筛选 -->
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <!-- 根据部门ID筛选 -->
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <!-- 根据年月筛选，精确匹配 -->
            <if test="yearMonth != null and yearMonth != ''">
                AND p.date = #{yearMonth}
            </if>
        </where>
        ORDER BY p.date DESC
    </select>

    <!-- 根据员工ID查询业绩记录 -->
    <select id="selectByEmployeeId" resultMap="BaseResultMap">
        SELECT p.id,
               p.employee_id,
               p.date,
               p.estimated_performance,
               p.actual_performance,
               e.name,
               d.department_name,
               pos.position_name,
               s.total_salary
        FROM performance p
                 LEFT JOIN employee e ON p.employee_id = e.employee_id
                 LEFT JOIN department d ON e.department_id = d.department_id
                 LEFT JOIN position pos ON e.position_id = pos.position_id
                 LEFT JOIN salary s ON p.employee_id = s.employee_id AND p.date = s.date
        WHERE p.employee_id = #{employeeId}
        ORDER BY p.date DESC
    </select>

    <!-- 根据员工ID和日期查询业绩记录 -->
    <select id="selectByEmployeeIdAndDate" resultMap="BaseResultMap">
        SELECT p.id,
               p.employee_id,
               p.date,
               p.estimated_performance,
               p.actual_performance,
               e.name,
               d.department_name,
               pos.position_name,
               s.total_salary
        FROM performance p
                 LEFT JOIN employee e ON p.employee_id = e.employee_id
                 LEFT JOIN department d ON e.department_id = d.department_id
                 LEFT JOIN position pos ON e.position_id = pos.position_id
                 LEFT JOIN salary s ON p.employee_id = s.employee_id AND p.date = s.date
        WHERE p.employee_id = #{employeeId}
          AND p.date = #{date}
    </select>

    <!-- 插入业绩记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Performance" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO performance (employee_id, date, estimated_performance, actual_performance)
        VALUES (#{employeeId}, #{date}, #{estimatedPerformance}, #{actualPerformance})
    </insert>

    <!-- 根据主键更新业绩记录 -->
    <update id="updateByPrimaryKey" parameterType="org.example.company_management.entity.Performance">
        UPDATE performance
        SET employee_id           = #{employeeId},
            date                  = #{date},
            estimated_performance = #{estimatedPerformance},
            actual_performance    = #{actualPerformance}
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除业绩记录 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        DELETE
        FROM performance
        WHERE id = #{id}
    </delete>

    <!-- 根据部门ID列表和日期查询业绩记录 -->
    <select id="selectByDepartmentIdsAndDate" resultMap="BaseResultMap">
        SELECT
        p.id,
        p.employee_id,
        p.date,
        p.estimated_performance,
        p.actual_performance,
        e.name, -- BaseResultMap 将其映射到 Performance.employeeName
        d.department_name, -- BaseResultMap 将其映射到 Performance.department
        pos.position_name, -- BaseResultMap 将其映射到 Performance.position
        s.total_salary, -- BaseResultMap 将其映射到 Performance.totalSalary
        (
        SELECT COALESCE(SUM(pc.amount), 0)
        FROM petty_cash pc
        WHERE pc.employee_id = e.employee_id
        AND pc.date = p.date
        AND pc.status != '已拒绝'
        ) AS total_petty_cash
        FROM
        performance p
        JOIN
        employee e ON p.employee_id = e.employee_id
        JOIN
        department d ON e.department_id = d.department_id
        LEFT JOIN
        position pos ON e.position_id = pos.position_id
        LEFT JOIN
        salary s ON p.employee_id = s.employee_id AND p.date = s.date
        WHERE
        d.department_id IN
        <foreach collection="departmentIds" item="departmentId" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        <if test="employeeName != null and employeeName != ''">
            AND e.name LIKE CONCAT('%', #{employeeName}, '%')
        </if>
        <if test="date != null and date != ''">
            AND
            <choose>
                <when test="date.toString().startsWith('range_')">
                    <bind name="dateStr" value="date.toString().substring(6)"/>
                    <bind name="dateRange" value="dateStr.split('_')"/>
                    p.date BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                </when>
                <otherwise>
                    p.date = #{date}
                </otherwise>
            </choose>
        </if>
        AND (p.estimated_performance IS NOT NULL OR p.actual_performance IS NOT NULL)
        ORDER BY
        p.date DESC, e.employee_id ASC
    </select>

    <!-- 查询部门业绩统计数据 -->
    <select id="selectDepartmentStats" resultType="org.example.company_management.dto.DepartmentPerformanceStatsDTO">
        SELECT
            departmentId,
            departmentName,
            yearMonth,
            COALESCE(SUM(estimatedPerformance), 0) as estimatedPerformance,
            COALESCE(SUM(actualPerformance), 0) as actualPerformance,
            COALESCE(SUM(totalSalary), 0) as totalSalary,
            COALESCE(SUM(totalPettyCash), 0) as totalPettyCash,
            COALESCE(SUM(totalDepartmentExpense), 0) as totalDepartmentExpense,
            COALESCE(SUM(totalEmployeeExpense), 0) as totalEmployeeExpense,
            (COALESCE(SUM(estimatedPerformance), 0) - COALESCE(SUM(totalSalary), 0) - COALESCE(SUM(totalPettyCash), 0) - COALESCE(SUM(totalDepartmentExpense), 0) - COALESCE(SUM(totalEmployeeExpense), 0)) as estimatedMonthlyProfitLoss,
            (COALESCE(SUM(actualPerformance), 0) - COALESCE(SUM(totalSalary), 0) - COALESCE(SUM(totalPettyCash), 0) - COALESCE(SUM(totalDepartmentExpense), 0) - COALESCE(SUM(totalEmployeeExpense), 0)) as monthlyProfitLoss,
            SUM(recordCount) as recordCount
        FROM (
            SELECT
                e.department_id as departmentId,
                d.department_name as departmentName,
                p.date as yearMonth,
                p.estimated_performance as estimatedPerformance,
                p.actual_performance as actualPerformance,
                0 as totalSalary,
                0 as totalPettyCash,
                0 as totalDepartmentExpense,
                0 as totalEmployeeExpense,
                1 as recordCount
            FROM performance p
            JOIN employee e ON p.employee_id = e.employee_id
            JOIN department d ON e.department_id = d.department_id
            WHERE d.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                <if test="date != null and date != ''">
                    <choose>
                        <when test="date.toString().startsWith('range_')">
                            <bind name="dateStr" value="date.toString().substring(6)"/>
                            <bind name="dateRange" value="dateStr.split('_')"/>
                            AND p.date BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                        </when>
                        <otherwise>
                            AND p.date = #{date}
                        </otherwise>
                    </choose>
                </if>

            UNION ALL

            SELECT
                e.department_id as departmentId,
                d.department_name as departmentName,
                s.date as yearMonth,
                0 as estimatedPerformance,
                0 as actualPerformance,
                s.total_salary as totalSalary,
                0 as totalPettyCash,
                0 as totalDepartmentExpense,
                0 as totalEmployeeExpense,
                0 as recordCount
            FROM salary s
            JOIN employee e ON s.employee_id = e.employee_id
            JOIN department d ON e.department_id = d.department_id
            WHERE d.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                <if test="date != null and date != ''">
                    <choose>
                        <when test="date.toString().startsWith('range_')">
                            <bind name="dateStr" value="date.toString().substring(6)"/>
                            <bind name="dateRange" value="dateStr.split('_')"/>
                            AND s.date BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                        </when>
                        <otherwise>
                            AND s.date = #{date}
                        </otherwise>
                    </choose>
                </if>

            UNION ALL

            SELECT
                e.department_id as departmentId,
                d.department_name as departmentName,
                pc.date as yearMonth,
                0 as estimatedPerformance,
                0 as actualPerformance,
                0 as totalSalary,
                pc.amount as totalPettyCash,
                0 as totalDepartmentExpense,
                0 as totalEmployeeExpense,
                0 as recordCount
            FROM petty_cash pc
            JOIN employee e ON pc.employee_id = e.employee_id
            JOIN department d ON e.department_id = d.department_id
            WHERE d.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                AND pc.status != '已拒绝'
                <if test="date != null and date != ''">
                    <choose>
                        <when test="date.toString().startsWith('range_')">
                            <bind name="dateStr" value="date.toString().substring(6)"/>
                            <bind name="dateRange" value="dateStr.split('_')"/>
                            AND pc.date BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                        </when>
                        <otherwise>
                            AND pc.date = #{date}
                        </otherwise>
                    </choose>
                </if>

            UNION ALL

            SELECT
                de.department_id as departmentId,
                d.department_name as departmentName,
                DATE_FORMAT(de.expense_date, '%Y-%m') as yearMonth,
                0 as estimatedPerformance,
                0 as actualPerformance,
                0 as totalSalary,
                0 as totalPettyCash,
                de.amount as totalDepartmentExpense,
                0 as totalEmployeeExpense,
                0 as recordCount
            FROM department_expense de
            JOIN department d ON de.department_id = d.department_id
            WHERE de.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                <if test="date != null and date != ''">
                    <choose>
                        <when test="date.toString().startsWith('range_')">
                            <bind name="dateStr" value="date.toString().substring(6)"/>
                            <bind name="dateRange" value="dateStr.split('_')"/>
                            AND DATE_FORMAT(de.expense_date, '%Y-%m') BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                        </when>
                        <otherwise>
                            AND DATE_FORMAT(de.expense_date, '%Y-%m') = #{date}
                        </otherwise>
                    </choose>
                </if>

            UNION ALL

            SELECT
                e.department_id as departmentId,
                d.department_name as departmentName,
                DATE_FORMAT(eoe.expense_date, '%Y-%m') as yearMonth,
                0 as estimatedPerformance,
                0 as actualPerformance,
                0 as totalSalary,
                0 as totalPettyCash,
                0 as totalDepartmentExpense,
                eoe.amount as totalEmployeeExpense,
                0 as recordCount
            FROM employee_other_expense eoe
            JOIN employee e ON eoe.employee_id = e.employee_id
            JOIN department d ON e.department_id = d.department_id
            WHERE d.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                <if test="date != null and date != ''">
                    <choose>
                        <when test="date.toString().startsWith('range_')">
                            <bind name="dateStr" value="date.toString().substring(6)"/>
                            <bind name="dateRange" value="dateStr.split('_')"/>
                            AND DATE_FORMAT(eoe.expense_date, '%Y-%m') BETWEEN #{dateRange[0]} AND #{dateRange[1]}
                        </when>
                        <otherwise>
                            AND DATE_FORMAT(eoe.expense_date, '%Y-%m') = #{date}
                        </otherwise>
                    </choose>
                </if>
        ) AS AggregatedFinancialData
        GROUP BY departmentId, departmentName, yearMonth
        ORDER BY yearMonth DESC, departmentName ASC
    </select>

    <!-- 根据员工ID和日期查询特定业绩数据（预估和实际） -->
    <select id="selectPerformanceDataByEmployeeIdAndDate" resultType="org.example.company_management.entity.Performance">
        SELECT
            p.estimated_performance,
            p.actual_performance
        FROM performance p
        WHERE p.employee_id = #{employeeId} AND p.date = #{date}
    </select>

    <!-- 根据部门ID和年月查询部门总费用 -->
    <select id="sumTotalDepartmentExpensesByDepartmentIdAndMonth" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(de.amount), 0)
        FROM department_expense de
        WHERE de.department_id = #{departmentId}
          AND DATE_FORMAT(de.expense_date, '%Y-%m') = #{yearMonth}
    </select>

    <!-- 根据员工ID和年月查询员工其他费用 -->
    <select id="sumTotalEmployeeOtherExpensesByEmployeeIdAndMonth" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(eoe.amount), 0)
        FROM employee_other_expense eoe
        WHERE eoe.employee_id = #{employeeId}
          AND DATE_FORMAT(eoe.expense_date, '%Y-%m') = #{yearMonth}
    </select>

    <resultMap id="PerformanceResultMap" type="org.example.company_management.entity.Performance">
        <id property="id" column="id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="date" column="date"/>
        <result property="estimatedPerformance" column="estimated_performance"/>
        <result property="actualPerformance" column="actual_performance"/>
    </resultMap>

    <select id="findByEmployeeIdAndDate" resultMap="PerformanceResultMap">
        SELECT id, employee_id, date, estimated_performance, actual_performance
        FROM performance
        WHERE employee_id = #{employeeId} AND date = #{date}
        LIMIT 1
    </select>
</mapper>
