package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

import java.time.LocalDateTime;

/**
 * 会议参与者数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingParticipantDTO {
    
    /**
     * 参与者记录ID
     */
    private Long id;
    
    /**
     * 会议ID
     */
    private Long meetingId;
    
    /**
     * 参与者类型：DEPARTMENT-部门、EMPLOYEE-员工
     */
    @NotBlank(message = "参与者类型不能为空", groups = {ValidationGroups.Add.class})
    private String participantType;
    
    /**
     * 参与者ID（部门ID或员工ID）
     */
    @NotNull(message = "参与者ID不能为空", groups = {ValidationGroups.Add.class})
    private Integer participantId;
    
    /**
     * 参与者名称（非数据库字段）
     * 如果是部门，则为部门名称；如果是员工，则为员工姓名
     */
    private String participantName;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
