package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * {{CHENGQI: 推广附件实体类}}
 * {{CHENGQI: 任务ID: P4-LD-002}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 16:45:00 +08:00}}
 * {{CHENGQI: 描述: 推广附件实体类，对应数据库promotion_attachments表}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionAttachment {

    /**
     * 附件ID
     */
    private Long id;

    /**
     * 推广ID
     */
    @NotNull(message = "推广ID不能为空")
    private Long promotionId;

    /**
     * 原始文件名
     */
    @NotBlank(message = "文件名不能为空")
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    private String fileName;

    /**
     * 文件存储路径
     */
    @NotBlank(message = "文件路径不能为空")
    @Size(max = 500, message = "文件路径长度不能超过500个字符")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    @Min(value = 1, message = "文件大小必须大于0")
    @Max(value = 52428800, message = "文件大小不能超过50MB")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    @NotBlank(message = "文件类型不能为空")
    @Size(max = 100, message = "文件类型长度不能超过100个字符")
    private String fileType;

    /**
     * 文件扩展名
     */
    @NotBlank(message = "文件扩展名不能为空")
    @Size(max = 20, message = "文件扩展名长度不能超过20个字符")
    @Pattern(regexp = "^\\.(pdf|doc|docx|xls|xlsx|txt|jpg|jpeg|png|gif|zip|rar|7z|tar|gz)$",
             message = "不支持的文件类型")
    private String fileExtension;

    /**
     * 上传时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime uploadTime;

    /**
     * 上传者员工ID
     */
    @NotNull(message = "上传者ID不能为空")
    private Integer uploaderId;

    /**
     * 下载次数
     */
    @Min(value = 0, message = "下载次数不能为负数")
    private Integer downloadCount = 0;

    /**
     * 是否已删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // ==================== 非数据库字段（用于查询结果展示） ====================

    /**
     * 上传者姓名（非数据库字段）
     */
    private String uploaderName;

    /**
     * 推广标题（非数据库字段）
     */
    private String promotionTitle;

    /**
     * 文件大小格式化显示（非数据库字段）
     */
    private String fileSizeFormatted;

    // ==================== 业务方法 ====================

    /**
     * 获取格式化的文件大小
     * @return 格式化后的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", (double) size, units[unitIndex]);
    }

    /**
     * 检查文件是否为图片类型
     * @return 是否为图片
     */
    public boolean isImage() {
        if (fileExtension == null) {
            return false;
        }
        String ext = fileExtension.toLowerCase();
        return ext.equals(".jpg") || ext.equals(".jpeg") || ext.equals(".png") || ext.equals(".gif");
    }

    /**
     * 检查文件是否为文档类型
     * @return 是否为文档
     */
    public boolean isDocument() {
        if (fileExtension == null) {
            return false;
        }
        String ext = fileExtension.toLowerCase();
        return ext.equals(".pdf") || ext.equals(".doc") || ext.equals(".docx") || 
               ext.equals(".xls") || ext.equals(".xlsx") || ext.equals(".txt");
    }

    /**
     * 检查文件是否为压缩包
     * @return 是否为压缩包
     */
    public boolean isArchive() {
        if (fileExtension == null) {
            return false;
        }
        String ext = fileExtension.toLowerCase();
        return ext.equals(".zip") || ext.equals(".rar") || ext.equals(".7z") ||
               ext.equals(".tar") || ext.equals(".gz");
    }
}

// {{CHENGQI: 推广附件实体类创建完成}}
