<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.StockPriceMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.StockPrice">
        <id column="id" property="id"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="time" property="time"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, unit_price, time, remark, create_time, update_time
    </sql>

    <!-- 插入股票价格记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.StockPrice" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_price (unit_price, time, remark, create_time, update_time)
        VALUES (#{unitPrice}, #{time}, #{remark}, NOW(), NOW())
    </insert>

    <!-- 根据ID删除股票价格记录 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM stock_price WHERE id = #{id}
    </delete>

    <!-- 更新股票价格记录 -->
    <update id="updateById" parameterType="org.example.company_management.entity.StockPrice">
        UPDATE stock_price
        SET unit_price = #{unitPrice},
            time = #{time},
            remark = #{remark},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询股票价格记录 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        WHERE id = #{id}
    </select>

    <!-- 删除：selectByTime - 功能重复，可用日期范围查询替代 -->

    <!-- 分页查询股票价格记录 -->
    <select id="selectByPage" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        <where>
            <if test="startDate != null">
                AND time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND time &lt;= #{endDate}
            </if>
            <if test="remark != null and remark != ''">
                AND remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                <choose>
                    <when test="orderBy == 'time'">
                        ORDER BY time
                    </when>
                    <when test="orderBy == 'unit_price'">
                        ORDER BY unit_price
                    </when>
                    <when test="orderBy == 'create_time'">
                        ORDER BY create_time
                    </when>
                    <otherwise>
                        ORDER BY time
                    </otherwise>
                </choose>
                <if test="orderDirection != null and orderDirection != ''">
                    <choose>
                        <when test="orderDirection == 'ASC'">ASC</when>
                        <otherwise>DESC</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                ORDER BY time DESC
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 分页查询股票价格记录（使用PageHelper） -->
    <select id="selectByPageWithPageHelper" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        <where>
            <if test="startDate != null">
                AND time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND time &lt;= #{endDate}
            </if>
            <if test="remark != null and remark != ''">
                AND remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                <choose>
                    <when test="orderBy == 'time'">
                        ORDER BY time
                    </when>
                    <when test="orderBy == 'unit_price'">
                        ORDER BY unit_price
                    </when>
                    <when test="orderBy == 'create_time'">
                        ORDER BY create_time
                    </when>
                    <otherwise>
                        ORDER BY time
                    </otherwise>
                </choose>
                <if test="orderDirection != null and orderDirection != ''">
                    <choose>
                        <when test="orderDirection == 'ASC'">ASC</when>
                        <otherwise>DESC</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                ORDER BY time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 统计股票价格记录总数 -->
    <select id="countTotal" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM stock_price
        <where>
            <if test="startDate != null">
                AND time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND time &lt;= #{endDate}
            </if>
            <if test="remark != null and remark != ''">
                AND remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
    </select>

    <!-- 查询所有股票价格记录（不分页） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        ORDER BY time DESC
    </select>

    <!-- 查询指定日期范围内的股票价格记录 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        WHERE time BETWEEN #{startDate} AND #{endDate}
        ORDER BY time ASC
    </select>

    <!-- 查询最新的股票价格记录 -->
    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM stock_price
        ORDER BY time DESC
        LIMIT 1
    </select>

    <!-- 删除：selectByRemarkLike - 通过分页查询的remark参数实现 -->

    <!-- 检查指定时间是否已存在股票价格记录 -->
    <select id="countByTime" resultType="int">
        SELECT COUNT(*)
        FROM stock_price
        WHERE time = #{time}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
