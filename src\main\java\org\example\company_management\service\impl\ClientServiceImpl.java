package org.example.company_management.service.impl;

import org.example.company_management.entity.Client;
import org.example.company_management.mapper.ClientMapper;
import org.example.company_management.service.ClientService;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户服务实现类
 */
@Service
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientMapper clientMapper;

    @Override
    public Client getClientById(Integer clientId) {
        return clientMapper.selectById(clientId);
    }

    @Override
    public List<Client> getAllClients() {
        return clientMapper.selectAll();
    }

    @Override
    public List<Client> getClientsByPage(Map<String, Object> params) {
        if (params.containsKey("employeeName") && params.get("employeeName") != null && !params.get("employeeName").toString().trim().isEmpty()) {
            params.put("employeeName", "%" + params.get("employeeName").toString().trim() + "%");
        }
        if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().trim().isEmpty()) {
            params.put("name", "%" + params.get("name").toString().trim() + "%");
        }
        return clientMapper.selectByPage(params);
    }

    @Override
    public int getClientCount(Map<String, Object> params) {
        if (params.containsKey("employeeName") && params.get("employeeName") != null && !params.get("employeeName").toString().trim().isEmpty()) {
            params.put("employeeName", "%" + params.get("employeeName").toString().trim() + "%");
        }
        if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().trim().isEmpty()) {
            params.put("name", "%" + params.get("name").toString().trim() + "%");
        }
        return clientMapper.countTotal(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Integer> saveClient(Client client) {
        // 首先验证客户名称是否已存在
        List<Client> existingClients = clientMapper.selectByName(client.getName());
        if (existingClients != null && !existingClients.isEmpty()) {
            // 使用validateFailed返回，表示这是一个输入验证失败的情况
            return Result.validateFailed("客户名称 '" + client.getName() + "' 已存在，请联系管理员确认");
        }

        // 判断是否有传员工id
        if (client.getEmployeeId() == null) {
            // 从ThreadLocal获取当前登录用户信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
                // 同样使用400状态码表示客户端错误
                return Result.error(400, "用户未登录或登录信息已失效，请重新登录");
            }
            client.setEmployeeId((Integer) employeeInfo.get("employeeId"));
        }

        // 设置默认状态
        if (client.getStatus() == null || client.getStatus().isEmpty()) {
            client.setStatus("未审核");
        }
        if (client.getClientStatus() == null || client.getClientStatus().isEmpty()) {
            client.setClientStatus("报价中");
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        client.setCreateTime(now);
        client.setUpdateTime(now);
        int result = clientMapper.insert(client);
        if (result > 0) {
            return Result.success("客户添加成功", client.getClientId());
        } else {
            return Result.error("客户添加失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateClient(Client client) {
        // 设置更新时间
        client.setUpdateTime(LocalDateTime.now());
        return clientMapper.update(client);
    }

    //  软删除客户，将客户状态设定为已删除
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteClient(Integer clientId) {
        return clientMapper.deleteById(clientId);
    }

    @Override
    public List<Client> getClientsByEmployee(Integer employeeId) {
        return clientMapper.selectByEmployee(employeeId);
    }

    @Override
    public List<Client> getClientsByName(String name) {
        return clientMapper.selectByName(name);
    }

    @Override
    public Client getClientByEmail(String email) {
        return clientMapper.selectByEmail(email);
    }

    @Override
    public Client getClientByPhone(String phone) {
        return clientMapper.selectByPhone(phone);
    }

    @Override
    public List<Client> getMyClientsByPage(Map<String, Object> params) {
        Integer employeeId = null;

        // 检查参数中是否已经包含employeeId（用于管理员查看其他员工的客户）
        if (params.containsKey("employeeId") && params.get("employeeId") != null) {
            employeeId = (Integer) params.get("employeeId");
        } else {
            // 从ThreadLocal获取当前登录用户信息
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
                throw new RuntimeException("用户未登录或登录信息已失效");
            }
            employeeId = (Integer) employeeInfo.get("employeeId");
            params.put("employeeId", employeeId);
        }

        // 处理名称模糊查询
        if (params.containsKey("name") && params.get("name") != null
                && !params.get("name").toString().isEmpty()) {
            params.put("name", "%" + params.get("name") + "%");
        }
        List<Client> clients = clientMapper.selectMyClientsByPage(params);
        // 安全修复：删除搜索其他员工客户的逻辑，只能搜索自己负责的客户
        // 原逻辑存在安全漏洞：员工可以通过搜索功能查看其他员工的客户信息
        // if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().isEmpty() && clients.isEmpty()) {
        //     clients = clientMapper.selectOtherClientsByName(params.get("name").toString());
        // }
        return clients;
    }

    @Override
    public int getMyClientCount(Map<String, Object> params) {
        // 从ThreadLocal获取当前登录用户信息
        Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
        if (employeeInfo == null || employeeInfo.get("employeeId") == null) {
            throw new RuntimeException("用户未登录或登录信息已失效");
        }

        // 添加employeeId到查询参数
        Integer employeeId = (Integer) employeeInfo.get("employeeId");
        params.put("employeeId", employeeId);

        // 处理名称模糊查询
        if (params.containsKey("name") && params.get("name") != null
                && !params.get("name").toString().isEmpty()) {
            params.put("name", "%" + params.get("name") + "%");
        }

        return clientMapper.countMyClientsTotal(params);
    }

    @Override
    public List<Client> getPendingClients() {
        return clientMapper.selectPendingClients();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateClientStatus(Integer clientId, String status, String rejectRemark) {
        Map<String, Object> params = new HashMap<>();
        params.put("clientId", clientId);
        params.put("status", status);

        // 根据新的状态逻辑处理
        if(status.equals("审核通过")){
            params.put("operationTime", LocalDateTime.now());
        }
        // 如果状态是"已拒绝"，则设置拒绝备注；否则，确保拒绝备注为null或空，以便在mapper中正确处理
        if (status.equals("已拒绝")) {
            params.put("rejectRemark", rejectRemark != null ? rejectRemark : ""); // 或者根据mapper设为null
        } else {
            params.put("rejectRemark", null); // 确保非拒绝状态时，rejectRemark不被错误地保留或设置
        }
        return clientMapper.updateClientStatus(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateClientStatus(List<Integer> clientIds, String status, String rejectRemark) {
        if (clientIds == null || clientIds.isEmpty()) {
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("clientIds", clientIds);
        params.put("status", status);
        // 如果状态是"已拒绝"，则设置拒绝备注；否则，确保拒绝备注为null或空
        if (status.equals("已拒绝")) {
            params.put("rejectRemark", rejectRemark != null ? rejectRemark : "");
        } else {
            params.put("rejectRemark", null);
        }
        return clientMapper.batchUpdateClientStatus(params);
    }

    @Override
    public int getPendingClientCount() {
        return clientMapper.countPendingClients();
    }

    @Override
    public List<Client> getClientsByDepartments(Map<String, Object> params) {
        return clientMapper.selectClientsByDepartments(params);
    }

    @Override
    public int countClientsByDepartments(Map<String, Object> params) {
        return clientMapper.countClientsByDepartments(params);
    }
} 