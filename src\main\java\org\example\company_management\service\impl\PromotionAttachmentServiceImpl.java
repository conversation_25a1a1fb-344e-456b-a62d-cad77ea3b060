package org.example.company_management.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.entity.PromotionAttachment;
import org.example.company_management.mapper.PromotionAttachmentMapper;
import org.example.company_management.mapper.PromotionMapper;
import org.example.company_management.service.PromotionAttachmentService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {{CHENGQI: 推广附件服务实现类}}
 * {{CHENGQI: 任务ID: P4-LD-011}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 18:15:00 +08:00}}
 * {{CHENGQI: 描述: 推广附件业务逻辑实现，提供附件的上传、下载、管理等功能}}
 */
@Slf4j
@Service
public class PromotionAttachmentServiceImpl implements PromotionAttachmentService {

    // 允许的文件类型
    private static final Set<String> ALLOWED_FILE_TYPES = Set.of(
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/plain",
            "image/jpeg",
            "image/png",
            "image/gif",
            // ZIP 格式的多种 MIME 类型变体
            "application/zip",
            "application/x-zip",
            "application/x-zip-compressed",
            "application/octet-stream",       // 某些情况下文件可能被识别为此类型
            // RAR 格式
            "application/x-rar-compressed",
            "application/vnd.rar",
            "application/x-rar",
            // 其他压缩格式
            "application/x-7z-compressed",    // 7z 压缩包
            "application/x-tar",              // tar 压缩包
            "application/gzip"                // gz 压缩包
    );
    // 文件扩展名映射
    private static final Map<String, String> EXTENSION_MAPPING = new HashMap<String, String>() {{
        put("application/pdf", ".pdf");
        put("application/msword", ".doc");
        put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".docx");
        put("application/vnd.ms-excel", ".xls");
        put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".xlsx");
        put("text/plain", ".txt");
        put("image/jpeg", ".jpg");
        put("image/png", ".png");
        put("image/gif", ".gif");
        put("application/zip", ".zip");
        put("application/x-rar-compressed", ".rar");
        put("application/x-7z-compressed", ".7z");
        put("application/x-tar", ".tar");
        put("application/gzip", ".gz");
    }};
    // 最大文件大小（50MB）
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024;
    @Autowired
    private PromotionAttachmentMapper attachmentMapper;
    @Autowired
    private PromotionMapper promotionMapper;
    @Autowired
    private ObjectMapper objectMapper;
    // 附件存储根路径（物理路径）
    @Value("${app.upload.path}")
    private String uploadRootPath;
    // 附件访问URL前缀
    @Value("${app.upload.url-prefix}")
    private String urlPrefix;

    @Override
    @Transactional
    public Result<PromotionAttachment> uploadAttachment(Long promotionId, MultipartFile file) {
        try {
            // 验证推广是否存在
            if (promotionMapper.selectById(promotionId) == null) {
                return Result.error("推广不存在");
            }

            // 验证文件
            String validationError = validateFileInline(file);
            if (validationError != null) {
                return Result.error(validationError);
            }

            // 获取当前用户ID
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录");
            }

            // 1. 生成相对路径和唯一文件名
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
            String subDirectory = "promotions/attachments/" + datePath;
            String uniqueFileName = resolveFileNameConflict(subDirectory, file.getOriginalFilename());

            // 2. 组合成完整的物理路径
            Path destinationFile = Paths.get(this.uploadRootPath, subDirectory, uniqueFileName);

            // 3. 创建父目录并保存文件
            Files.createDirectories(destinationFile.getParent());
            file.transferTo(destinationFile.toFile());

            // 4. 构建URL访问路径
            String accessibleUrl = this.urlPrefix + "/" + subDirectory + "/" + uniqueFileName;

            // 5. 创建附件记录并保存到数据库
            PromotionAttachment attachment = new PromotionAttachment();
            attachment.setPromotionId(promotionId);
            attachment.setFileName(file.getOriginalFilename()); // 保存原始文件名
            attachment.setFilePath(accessibleUrl); // 存入数据库的是URL路径
            attachment.setFileSize(file.getSize());
            attachment.setFileType(file.getContentType());
            attachment.setFileExtension(getFileExtension(file));
            attachment.setUploadTime(LocalDateTime.now());
            attachment.setUploaderId(currentEmployeeId);
            attachment.setDownloadCount(0);
            attachment.setIsDeleted(false);
            attachment.setCreateTime(LocalDateTime.now());
            attachment.setUpdateTime(LocalDateTime.now());

            // 保存到数据库
            int result = attachmentMapper.insert(attachment);
            if (result > 0) {
                log.info("附件上传成功，推广ID: {}, 文件名: {}", promotionId, file.getOriginalFilename());
                return Result.success(attachment);
            } else {
                // 如果数据库保存失败，删除已上传的文件
                Files.deleteIfExists(destinationFile);
                return Result.error("附件保存失败");
            }
        } catch (IOException e) {
            log.error("附件上传失败，推广ID: {}, 文件名: {}", promotionId, file.getOriginalFilename(), e);
            return Result.error("文件保存失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("附件上传异常，推广ID: {}, 文件名: {}", promotionId, file.getOriginalFilename(), e);
            return Result.error("附件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<PromotionAttachment> uploadTempAttachment(MultipartFile file) {
        try {
            // 验证文件
            String validationError = validateFileInline(file);
            if (validationError != null) {
                return Result.error(validationError);
            }

            // 获取当前用户ID
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录");
            }

            // 1. 生成相对路径和唯一文件名
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
            String subDirectory = "promotions/temp/" + datePath;

            String originalFilename = file.getOriginalFilename();
            // 使用与编辑推广相同的文件名处理策略：原始文件名 + 冲突处理
            String uniqueFileName = resolveTempFileNameConflict(subDirectory, originalFilename);

            // 2. 组合成完整的物理路径
            Path destinationFile = Paths.get(this.uploadRootPath, subDirectory, uniqueFileName);

            // 3. 创建父目录
            Files.createDirectories(destinationFile.getParent());

            // 4. 保存文件
            file.transferTo(destinationFile.toFile());

            // 5. 构建URL访问路径
            String accessibleUrl = this.urlPrefix + "/" + subDirectory + "/" + uniqueFileName;

            // 6. 创建临时附件对象返回给前端
            PromotionAttachment tempAttachment = new PromotionAttachment();
            tempAttachment.setId(null); // 临时附件没有ID
            tempAttachment.setPromotionId(null); // 临时附件没有推广ID
            tempAttachment.setFileName(originalFilename);
            tempAttachment.setFilePath(accessibleUrl); // 返回URL路径！
            tempAttachment.setFileSize(file.getSize());
            tempAttachment.setFileType(file.getContentType());
            tempAttachment.setFileExtension(getFileExtension(file));
            tempAttachment.setUploadTime(LocalDateTime.now());
            tempAttachment.setUploaderId(currentEmployeeId);
            tempAttachment.setDownloadCount(0);
            tempAttachment.setIsDeleted(false);
            tempAttachment.setCreateTime(LocalDateTime.now());
            tempAttachment.setUpdateTime(LocalDateTime.now());

            log.info("临时附件上传成功，文件名: {}", originalFilename);
            return Result.success(tempAttachment);
        } catch (IOException e) {
            log.error("临时附件上传失败，文件名: {}", file.getOriginalFilename(), e);
            return Result.error("文件保存失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("上传临时附件异常，文件名: {}", file.getOriginalFilename(), e);
            return Result.error("上传临时附件失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 批量附件上传方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前使用单文件上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用单文件上传接口

    @Override
    @Transactional
    public Result<Void> deleteAttachment(Long attachmentId) {
        try {
            // 查询附件信息
            PromotionAttachment attachment = attachmentMapper.selectById(attachmentId);
            if (attachment == null) {
                return Result.error("附件不存在");
            }

            // 检查权限（只有上传者或管理员可以删除）
            Integer currentEmployeeId = getCurrentEmployeeId();
            if (currentEmployeeId == null) {
                return Result.error("用户未登录");
            }

            if (!attachment.getUploaderId().equals(currentEmployeeId) && !isAdmin()) {
                return Result.error("权限不足，无法删除该附件");
            }

            // 逻辑删除附件记录
            int result = attachmentMapper.deleteById(attachmentId);
            if (result > 0) {
                // 删除物理文件
                deletePhysicalFile(attachment.getFilePath());
                log.info("附件删除成功，附件ID: {}", attachmentId);
                return Result.success();
            } else {
                return Result.error("附件删除失败");
            }
        } catch (Exception e) {
            log.error("删除附件异常，附件ID: {}", attachmentId, e);
            return Result.error("删除附件失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 单个附件查询方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件信息通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件信息通过推广详情接口获取

    // {{FUTURE_EXTENSION: 推广附件列表查询方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件列表通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件列表通过推广详情接口获取



    /**
     * 获取文件扩展名
     */
    private String getFileExtension(MultipartFile file) {
        String contentType = file.getContentType();
        if (EXTENSION_MAPPING.containsKey(contentType)) {
            return EXTENSION_MAPPING.get(contentType);
        }

        // 从文件名获取扩展名
        String originalName = file.getOriginalFilename();
        if (StringUtils.hasText(originalName) && originalName.contains(".")) {
            return originalName.substring(originalName.lastIndexOf("."));
        }

        return ".unknown";
    }

    // {{FUTURE_EXTENSION: 分页附件查询方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要复杂的附件分页查询功能}}
    // 已删除 - 附件查询通过推广详情接口实现

    @Override
    public Result<byte[]> downloadAttachment(Long attachmentId) {
        try {
            // 查询附件信息
            PromotionAttachment attachment = attachmentMapper.selectById(attachmentId);
            if (attachment == null) {
                return Result.error("附件不存在");
            }

            // 关键修复：从URL反推物理路径
            // 数据库存的是: /uploads/promotions/attachments/2025/06/file.pdf
            // urlPrefix 是: /uploads
            String relativePath = attachment.getFilePath().substring(this.urlPrefix.length());
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1); // 移除开头的斜杠
            }
            Path physicalPath = Paths.get(this.uploadRootPath, relativePath);

            if (Files.exists(physicalPath)) {
                // 读取文件内容
                byte[] fileContent = Files.readAllBytes(physicalPath);

                // 更新下载次数
                attachmentMapper.incrementDownloadCount(attachmentId);

                log.info("附件下载成功，附件ID: {}", attachmentId);
                return Result.success(fileContent);
            } else {
                log.error("物理文件未找到: {}, URL路径: {}", physicalPath, attachment.getFilePath());
                return Result.error("文件资源不存在或已丢失");
            }
        } catch (Exception e) {
            log.error("下载附件异常，附件ID: {}", attachmentId, e);
            return Result.error("下载失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 获取下载URL方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前直接使用下载接口，不需要单独获取URL}}
    // 已删除 - 直接使用下载接口

    @Override
    public Result<Void> incrementDownloadCount(Long attachmentId) {
        try {
            int result = attachmentMapper.incrementDownloadCount(attachmentId);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("更新下载次数失败");
            }
        } catch (Exception e) {
            log.error("更新下载次数异常，附件ID: {}", attachmentId, e);
            return Result.error("更新下载次数失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 用户附件列表查询方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要按用户查询附件功能}}
    // 已删除 - 附件管理通过推广维度进行

    @Override
    @Transactional
    public Result<Void> deleteAttachmentsByPromotionId(Long promotionId) {
        try {
            // 先查询所有附件，用于删除物理文件
            List<PromotionAttachment> attachments = attachmentMapper.selectByPromotionId(promotionId);

            // 逻辑删除数据库记录
            int result = attachmentMapper.deleteByPromotionId(promotionId);

            // 删除物理文件
            for (PromotionAttachment attachment : attachments) {
                deletePhysicalFile(attachment.getFilePath());
            }

            log.info("推广附件批量删除成功，推广ID: {}", promotionId);
            return Result.success();
        } catch (Exception e) {
            log.error("批量删除推广附件异常，推广ID: {}", promotionId, e);
            return Result.error("批量删除附件失败: " + e.getMessage());
        }
    }

    // {{FUTURE_EXTENSION: 附件统计方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要附件统计功能}}
    // 已删除 - 统计功能可能在管理后台实现

    // {{FUTURE_EXTENSION: 文件验证方法 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前文件验证在上传时进行，不需要独立验证接口}}
    // 已删除 - 文件验证集成在上传接口中

    /**
     * 删除物理文件
     */
    private void deletePhysicalFile(String urlPath) {
        if (!StringUtils.hasText(urlPath)) return;
        try {
            // 同样，从URL反推物理路径
            String relativePath = urlPath.substring(this.urlPrefix.length());
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1); // 移除开头的斜杠
            }
            Path physicalPath = Paths.get(this.uploadRootPath, relativePath);

            Files.deleteIfExists(physicalPath);
        } catch (Exception e) {
            log.warn("删除物理文件失败: {}", urlPath, e);
        }
    }

    /**
     * 获取当前用户ID
     */
    private Integer getCurrentEmployeeId() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("employeeId")) {
            Object idObject = employeeInfo.get("employeeId");
            if (idObject instanceof Integer) {
                return (Integer) idObject;
            } else if (idObject instanceof Number) {
                return ((Number) idObject).intValue();
            }
        }
        return null;
    }

    /**
     * 检查是否为管理员
     */
    private boolean isAdmin() {
        Map<String, Object> employeeInfo = ThreadLocalUtil.<Map<String, Object>>get("employee");
        if (employeeInfo != null && employeeInfo.containsKey("role")) {
            String role = (String) employeeInfo.get("role");
            return "ADMIN".equalsIgnoreCase(role);
        }
        return false;
    }

    /**
     * 内联文件验证方法
     */
    private String validateFileInline(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "文件不能为空";
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return "文件大小不能超过50MB";
        }

        // 检查文件类型
        String contentType = file.getContentType();
        boolean isValidType = ALLOWED_FILE_TYPES.contains(contentType);

        // 如果 MIME 类型检查失败，使用文件扩展名作为备用验证
        if (!isValidType && StringUtils.hasText(file.getOriginalFilename())) {
            String fileName = file.getOriginalFilename().toLowerCase();
            String[] allowedExtensions = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt",
                                         ".jpg", ".jpeg", ".png", ".gif",
                                         ".zip", ".rar", ".7z", ".tar", ".gz"};

            for (String ext : allowedExtensions) {
                if (fileName.endsWith(ext)) {
                    isValidType = true;
                    break;
                }
            }
        }

        if (!isValidType) {
            return "不支持的文件类型。文件: " + file.getOriginalFilename() + "，检测到的类型: " + contentType;
        }

        // 检查文件名
        String originalName = file.getOriginalFilename();
        if (!StringUtils.hasText(originalName)) {
            return "文件名不能为空";
        }

        // 检查文件名长度
        if (originalName.length() > 255) {
            return "文件名长度不能超过255个字符";
        }

        return null; // 验证通过
    }

    /**
     * 获取安全的文件名
     */
    private String getSafeFileName(String originalFileName) {
        if (!StringUtils.hasText(originalFileName)) {
            return "unnamed_" + System.currentTimeMillis() + ".unknown";
        }

        // 移除危险字符，保留中文、英文、数字、点、下划线、连字符、括号
        String safeName = originalFileName.replaceAll("[\\\\/:*?\"<>|]", "_");

        // 确保文件有扩展名
        if (!safeName.contains(".")) {
            safeName += ".unknown";
        }

        // 限制长度（保留扩展名）
        if (safeName.length() > 200) {
            int lastDotIndex = safeName.lastIndexOf('.');
            String nameWithoutExt = safeName.substring(0, lastDotIndex);
            String extension = safeName.substring(lastDotIndex);

            int maxNameLength = 200 - extension.length();
            if (maxNameLength > 0) {
                safeName = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength)) + extension;
            } else {
                // 扩展名太长，截断扩展名
                safeName = nameWithoutExt.substring(0, 190) + extension.substring(0, 10);
            }
        }

        return safeName;
    }

    /**
     * 解决文件名冲突
     */
    private String resolveFileNameConflict(String subDirectory, String originalFileName) {
        String basePath = Paths.get(this.uploadRootPath, subDirectory).toString();
        String fileName = getSafeFileName(originalFileName);

        // 创建目录（如果不存在）
        try {
            Files.createDirectories(Paths.get(basePath));
        } catch (IOException e) {
            log.warn("创建目录失败: {}", basePath, e);
        }

        String originalSafeFileName = fileName;
        int lastDotIndex = fileName.lastIndexOf('.');
        String nameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        String extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";

        int counter = 1;
        while (Files.exists(Paths.get(basePath, fileName))) {
            fileName = nameWithoutExt + "(" + counter + ")" + extension;
            counter++;

            // 防止无限循环，超过9999个重复文件时回退到timestamp策略
            if (counter > 9999) {
                String timestamp = String.valueOf(System.currentTimeMillis());
                String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                fileName = timestamp + "_" + uuid + "_" + originalSafeFileName;
                log.warn("文件名冲突过多，回退到timestamp策略: {}", fileName);
                break;
            }
        }

        // 文件名冲突已解决

        return fileName;
    }

    /**
     * 解决临时文件名冲突（与正式文件使用相同的策略）
     */
    private String resolveTempFileNameConflict(String subDirectory, String originalFileName) {
        String basePath = Paths.get(this.uploadRootPath, subDirectory).toString();
        String fileName = getSafeFileName(originalFileName);

        // 创建目录（如果不存在）
        try {
            Files.createDirectories(Paths.get(basePath));
        } catch (IOException e) {
            log.warn("创建临时目录失败: {}", basePath, e);
        }

        String originalSafeFileName = fileName;
        int lastDotIndex = fileName.lastIndexOf('.');
        String nameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        String extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";

        int counter = 1;
        while (Files.exists(Paths.get(basePath, fileName))) {
            fileName = nameWithoutExt + "(" + counter + ")" + extension;
            counter++;

            // 防止无限循环，超过9999个重复文件时回退到timestamp策略
            if (counter > 9999) {
                String timestamp = String.valueOf(System.currentTimeMillis());
                String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                fileName = "temp_" + timestamp + "_" + uuid + "_" + originalSafeFileName;
                log.warn("临时文件名冲突过多，回退到timestamp策略: {}", fileName);
                break;
            }
        }

        // 临时文件名冲突已解决

        return fileName;
    }
}

// {{CHENGQI: 推广附件服务实现类创建完成}}
