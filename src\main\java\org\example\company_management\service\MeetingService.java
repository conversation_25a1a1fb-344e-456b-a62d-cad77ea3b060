package org.example.company_management.service;

import org.example.company_management.dto.MeetingDTO;
import org.example.company_management.dto.MeetingSummaryDTO;
import org.example.company_management.entity.Meeting;
import org.example.company_management.entity.MeetingSummary;
import org.example.company_management.utils.PageResult;

/**
 * 会议服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface MeetingService {
    
    /**
     * 创建会议（管理端）
     *
     * @param meetingDTO 会议信息
     */
    void createMeeting(MeetingDTO meetingDTO);

    /**
     * 创建会议（员工端）
     *
     * @param meetingDTO 会议信息
     */
    void createEmployeeMeeting(MeetingDTO meetingDTO);
    
    /**
     * 更新会议信息（管理端）
     *
     * @param meetingDTO 会议信息
     */
    void updateMeeting(MeetingDTO meetingDTO);

    /**
     * 更新会议信息（员工端）
     *
     * @param meetingDTO 会议信息
     * @param currentEmployeeId 当前员工ID
     */
    void updateEmployeeMeeting(MeetingDTO meetingDTO, Integer currentEmployeeId);
    
    /**
     * 删除会议
     * 
     * @param id 会议ID
     */
    void deleteMeeting(Long id);
    
    /**
     * 根据ID获取会议详情
     * 
     * @param id 会议ID
     * @return 会议信息
     */
    Meeting getMeetingById(Long id);
    
    /**
     * 分页查询会议列表（管理端）
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param title 会议主题（可选）
     * @param status 会议状态（可选）
     * @param creatorId 发起人ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    PageResult<Meeting> getMeetingPage(int pageNum, int pageSize, String title, String status, 
                                       Integer creatorId, String startTime, String endTime);
    
    /**
     * 分页查询员工相关的会议列表（用户端）
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param employeeId 员工ID
     * @param status 会议状态（可选）
     * @param title 会议主题（可选）
     * @return 分页结果
     */
    PageResult<Meeting> getEmployeeMeetingPage(int pageNum, int pageSize, Integer employeeId, 
                                               String status, String title);
    
    /**
     * 添加会议总结
     * 
     * @param summaryDTO 总结信息
     */
    void addMeetingSummary(MeetingSummaryDTO summaryDTO);
    
    /**
     * 更新会议总结
     * 
     * @param summaryDTO 总结信息
     */
    void updateMeetingSummary(MeetingSummaryDTO summaryDTO);
    
    /**
     * 删除会议总结
     * 
     * @param id 总结ID
     */
    void deleteMeetingSummary(Long id);
    
    /**
     * 根据会议ID和员工ID获取总结
     * 
     * @param meetingId 会议ID
     * @param employeeId 员工ID
     * @return 会议总结
     */
    MeetingSummary getMeetingSummaryByMeetingIdAndEmployeeId(Long meetingId, Integer employeeId);
    
    // 状态更新方法已移除，现在由系统定时任务根据时间自动管理
}
