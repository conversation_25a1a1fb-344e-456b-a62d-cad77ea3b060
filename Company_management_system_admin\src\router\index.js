import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/token'
import { getUserInfo } from '@/api/auth'
import { ElMessage } from 'element-plus'

// {{CHENGQI: 管理端路由懒加载优化 - 减少初始包体积}}
// {{CHENGQI: 优化时间: 2025-07-09 11:20:28 +08:00}}
// {{CHENGQI: 说明: 移除顶级导入，改为在路由配置中使用动态导入和webpackChunkName}}

const routes = [
	{
		path: '/',
		redirect: '/dashboard',
	},
	{
		path: '/login',
		name: 'Login',
		component: () => import(/* webpackChunkName: "auth" */ '../views/Login.vue'),
		meta: { requiresAuth: false },
	},
	{
		path: '/dashboard',
		name: 'Dashboard',
		component: () => import(/* webpackChunkName: "layout" */ '../views/Dashboard.vue'),
		meta: { requiresAuth: true },
		children: [
			{
				path: '',
				name: 'Welcome',
				component: () => import(/* webpackChunkName: "dashboard" */ '../views/Welcome.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/department',
				name: 'Department',
				component: () => import(/* webpackChunkName: "organization" */ '../views/Department.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/position',
				name: 'Position',
				component: () => import(/* webpackChunkName: "organization" */ '../views/Position.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/employee',
				name: 'Employee',
				component: () => import(/* webpackChunkName: "personnel" */ '../views/Employee.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/client',
				name: 'Client',
				component: () => import(/* webpackChunkName: "personnel" */ '../views/Client.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/performance-analysis',
				name: 'PerformanceAnalysis',
				component: () => import(/* webpackChunkName: "performance" */ '../views/PerformanceAnalysis.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/salary',
				name: 'Salary',
				component: () => import(/* webpackChunkName: "financial" */ '../views/Salary.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/petty-cash',
				name: 'PettyCash',
				component: () => import(/* webpackChunkName: "financial" */ '../views/PettyCash.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/stock-management',
				name: 'StockManagement',
				component: () => import(/* webpackChunkName: "financial" */ '../views/StockManagement.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/department-expense',
				name: 'DepartmentExpense',
				component: () => import(/* webpackChunkName: "financial" */ '../views/DepartmentExpense.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/employee-expense',
				name: 'EmployeeExpense',
				component: () => import(/* webpackChunkName: "financial" */ '../views/EmployeeExpense.vue'),
				meta: { requiresAuth: true },
			},
			// {{CHENGQI: 销售日报管理路由}}
			// {{CHENGQI: 任务ID: P3-LD-011}}
			// {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
			{
				path: '/sales-report-management',
				name: 'SalesReportManagement',
				component: () => import(/* webpackChunkName: "reports" */ '../views/SalesReportManagementBasic.vue'),
				meta: { requiresAuth: true },
			},
			{
				path: '/meeting-management',
				name: 'MeetingManagement',
				component: () => import(/* webpackChunkName: "meeting" */ '../views/MeetingManagement.vue'),
				meta: { requiresAuth: true },
			},

		],
	},
	// 捕获所有未匹配的路由，并重定向到仪表盘
	{
		path: '/:pathMatch(.*)*',
		redirect: '/dashboard',
	},
]

const router = createRouter({
	history: createWebHistory(),
	routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
	const authStore = useAuthStore()

	// 如果有token但没有用户信息，尝试获取用户信息
	if (authStore.isAuthenticated && !authStore.user) {
		try {
			const res = await getUserInfo()
			if (res.code === 200) {
				authStore.setUser(res.data)
			} else {
				// 获取用户信息失败，可能是token无效
				authStore.logout()
				ElMessage.error('登录已过期，请重新登录')
				next({ name: 'Login' })
				return
			}
		} catch (error) {
			authStore.logout()
			ElMessage.error('获取用户信息失败，请重新登录')
			next({ name: 'Login' })
			return
		}
	}

	// 需要登录的页面
	if (to.meta.requiresAuth && !authStore.isAuthenticated) {
		next({ name: 'Login' })
	}
	// 已登录用户访问登录页面，重定向到仪表盘
	else if (to.name === 'Login' && authStore.isAuthenticated) {
		next({ name: 'Dashboard' })
	} else {
		next()
	}
})

export default router
