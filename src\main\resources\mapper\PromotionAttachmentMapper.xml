<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.PromotionAttachmentMapper">

    <!-- {{CHENGQI: 推广附件MyBatis映射文件}} -->
    <!-- {{CHENGQI: 任务ID: P4-LD-006}} -->
    <!-- {{CHENGQI: 负责人: LD}} -->
    <!-- {{CHENGQI: 创建时间: 2025-01-27 17:00:00 +08:00}} -->
    <!-- {{CHENGQI: 描述: 推广附件数据访问层SQL映射}} -->

    <!-- 结果映射 -->
    <resultMap id="PromotionAttachmentResultMap" type="org.example.company_management.entity.PromotionAttachment">
        <id property="id" column="id"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="fileExtension" column="file_extension"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="uploaderId" column="uploader_id"/>
        <result property="downloadCount" column="download_count"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 关联查询字段 -->
        <result property="uploaderName" column="uploader_name"/>
        <result property="promotionTitle" column="promotion_title"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        pa.id, pa.promotion_id, pa.file_name, pa.file_path, pa.file_size, pa.file_type, 
        pa.file_extension, pa.upload_time, pa.uploader_id, pa.download_count, pa.is_deleted, 
        pa.create_time, pa.update_time
    </sql>

    <!-- 关联查询字段 -->
    <sql id="Join_Column_List">
        <include refid="Base_Column_List"/>,
        e.name as uploader_name,
        p.title as promotion_title
    </sql>

    <!-- 基础关联查询 -->
    <sql id="Base_Join">
        FROM promotion_attachments pa
        LEFT JOIN employee e ON pa.uploader_id = e.employee_id
        LEFT JOIN promotion p ON pa.promotion_id = p.id
    </sql>

    <!-- 动态查询条件 -->
    <sql id="Dynamic_Where">
        <where>
            pa.is_deleted = FALSE
            <if test="promotionId != null">
                AND pa.promotion_id = #{promotionId}
            </if>
            <if test="uploaderId != null">
                AND pa.uploader_id = #{uploaderId}
            </if>
            <if test="fileType != null and fileType != ''">
                AND pa.file_type = #{fileType}
            </if>
            <if test="fileExtension != null and fileExtension != ''">
                AND pa.file_extension = #{fileExtension}
            </if>
            <if test="uploadTimeStart != null">
                AND pa.upload_time >= #{uploadTimeStart}
            </if>
            <if test="uploadTimeEnd != null">
                AND pa.upload_time &lt;= #{uploadTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 插入附件记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.PromotionAttachment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO promotion_attachments (
            promotion_id, file_name, file_path, file_size, file_type, file_extension,
            upload_time, uploader_id, download_count, is_deleted, create_time, update_time
        ) VALUES (
            #{promotionId}, #{fileName}, #{filePath}, #{fileSize}, #{fileType}, #{fileExtension},
            #{uploadTime}, #{uploaderId}, #{downloadCount}, #{isDeleted}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID更新附件记录 -->
    <update id="updateById" parameterType="org.example.company_management.entity.PromotionAttachment">
        UPDATE promotion_attachments
        <set>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileExtension != null and fileExtension != ''">file_extension = #{fileExtension},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除附件 -->
    <update id="deleteById">
        UPDATE promotion_attachments 
        SET is_deleted = TRUE, update_time = NOW() 
        WHERE id = #{id}
    </update>

    <!-- 物理删除附件（谨慎使用） -->
    <delete id="physicalDeleteById">
        DELETE FROM promotion_attachments WHERE id = #{id}
    </delete>

    <!-- 根据ID查询附件记录（包含关联信息） -->
    <select id="selectById" resultMap="PromotionAttachmentResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        WHERE pa.id = #{id} AND pa.is_deleted = FALSE
    </select>

    <!-- 根据推广ID查询附件列表 -->
    <select id="selectByPromotionId" resultMap="PromotionAttachmentResultMap">
        SELECT <include refid="Join_Column_List"/>
        <include refid="Base_Join"/>
        WHERE pa.promotion_id = #{promotionId} AND pa.is_deleted = FALSE
        ORDER BY pa.upload_time DESC
    </select>

    <!-- {{FUTURE_EXTENSION: 分页查询附件列表SQL - 删除}} -->
    <!-- {{CREATED: 2025-01-27 18:30:00 +08:00}} -->
    <!-- {{REASON: 当前不需要复杂的附件分页查询功能}} -->
    <!-- 已删除 - 附件查询通过推广详情接口实现 -->

    <!-- 更新下载次数 -->
    <update id="incrementDownloadCount">
        UPDATE promotion_attachments 
        SET download_count = download_count + 1, update_time = NOW() 
        WHERE id = #{id}
    </update>

    <!-- {{FUTURE_EXTENSION: 附件数量统计SQL - 删除}} -->
    <!-- {{CREATED: 2025-01-27 18:30:00 +08:00}} -->
    <!-- {{REASON: 当前不需要附件统计功能}} -->
    <!-- 已删除 - 统计功能可能在管理后台实现 -->

    <!-- {{FUTURE_EXTENSION: 附件大小统计SQL - 删除}} -->
    <!-- {{CREATED: 2025-01-27 18:30:00 +08:00}} -->
    <!-- {{REASON: 当前不需要附件统计功能}} -->
    <!-- 已删除 - 统计功能可能在管理后台实现 -->

    <!-- {{FUTURE_EXTENSION: 用户附件列表查询SQL - 删除}} -->
    <!-- {{CREATED: 2025-01-27 18:30:00 +08:00}} -->
    <!-- {{REASON: 当前不需要按用户查询附件功能}} -->
    <!-- 已删除 - 附件管理通过推广维度进行 -->

    <!-- 批量删除推广的所有附件（推广删除时使用） -->
    <update id="deleteByPromotionId">
        UPDATE promotion_attachments 
        SET is_deleted = TRUE, update_time = NOW() 
        WHERE promotion_id = #{promotionId}
    </update>

</mapper>

<!-- {{CHENGQI: 推广附件MyBatis映射文件创建完成}} -->
