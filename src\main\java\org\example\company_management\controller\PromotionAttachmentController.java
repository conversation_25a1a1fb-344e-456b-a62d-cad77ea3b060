package org.example.company_management.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.entity.PromotionAttachment;
import org.example.company_management.mapper.PromotionAttachmentMapper;
import org.example.company_management.service.PromotionAttachmentService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 推广附件管理控制器}}
 * {{CHENGQI: 任务ID: P4-LD-012}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 18:30:00 +08:00}}
 * {{CHENGQI: 描述: 推广附件管理HTTP接口，提供附件的上传、下载、管理等功能}}
 */
@Slf4j
@RestController
@RequestMapping("/promotion/attachments")
public class PromotionAttachmentController {

    @Autowired
    private PromotionAttachmentService attachmentService;

    @Autowired
    private PromotionAttachmentMapper attachmentMapper;

    /**
     * 上传单个附件
     *
     * @param promotionId 推广ID
     * @param file        上传的文件
     * @return 上传结果
     */
    @PostMapping("/upload/{promotionId}")
    public Result<PromotionAttachment> uploadAttachment(
            @PathVariable Long promotionId,
            @RequestParam("file") MultipartFile file) {

        log.info("开始上传附件，推广ID: {}, 文件名: {}", promotionId, file.getOriginalFilename());

        return attachmentService.uploadAttachment(promotionId, file);
    }

    /**
     * 上传临时附件（用于新建推广）
     *
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping("/upload/temp")
    public Result<PromotionAttachment> uploadTempAttachment(@RequestParam("file") MultipartFile file) {
        log.info("开始上传临时附件，文件名: {}", file.getOriginalFilename());

        return attachmentService.uploadTempAttachment(file);
    }

    // {{FUTURE_EXTENSION: 批量附件上传接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前使用单文件上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用单文件上传接口

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     * @return 删除结果
     */
    @DeleteMapping("/{attachmentId}")
    public Result<Void> deleteAttachment(@PathVariable Long attachmentId) {
        log.info("删除附件，附件ID: {}", attachmentId);
        return attachmentService.deleteAttachment(attachmentId);
    }

    // {{FUTURE_EXTENSION: 单个附件查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件信息通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件信息通过推广详情接口获取

    // {{FUTURE_EXTENSION: 推广附件列表查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件列表通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件列表通过推广详情接口获取

    // {{FUTURE_EXTENSION: 分页附件查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要复杂的附件分页查询功能}}
    // 已删除 - 附件查询通过推广详情接口实现

    /**
     * 下载附件
     *
     * @param attachmentId 附件ID
     * @return 文件下载响应
     */
    @GetMapping("/download/{attachmentId}")
    public ResponseEntity<byte[]> downloadAttachment(@PathVariable Long attachmentId) {
        try {
            // 下载文件内容（downloadAttachment方法内部会获取附件信息）
            Result<byte[]> downloadResult = attachmentService.downloadAttachment(attachmentId);
            if (!downloadResult.isSuccess()) {
                if (downloadResult.getMessage().contains("不存在")) {
                    return ResponseEntity.notFound().build();
                }
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            byte[] fileContent = downloadResult.getData();

            // 需要获取附件信息来设置文件名，直接从Mapper获取
            PromotionAttachment attachment = attachmentMapper.selectById(attachmentId);
            if (attachment == null) {
                return ResponseEntity.notFound().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 处理文件名编码
            String encodedFileName;
            try {
                encodedFileName = URLEncoder.encode(attachment.getFileName(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                encodedFileName = attachment.getFileName();
            }

            headers.setContentDispositionFormData("attachment", encodedFileName);
            headers.setContentLength(fileContent.length);

            log.info("附件下载成功，附件ID: {}", attachmentId);

            return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载附件异常，附件ID: {}", attachmentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // {{FUTURE_EXTENSION: 获取下载URL接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前直接使用下载接口，不需要单独获取URL}}
    // 已删除 - 直接使用下载接口

    // {{FUTURE_EXTENSION: 用户附件列表查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要按用户查询附件功能}}
    // 已删除 - 附件管理通过推广维度进行

    // {{FUTURE_EXTENSION: 附件统计接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要附件统计功能}}
    // 已删除 - 统计功能可能在管理后台实现

    // {{FUTURE_EXTENSION: 文件验证接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前文件验证在上传时进行，不需要独立验证接口}}
    // 已删除 - 文件验证集成在上传接口中

    // {{FUTURE_EXTENSION: 支持文件类型查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前文件类型限制在前端硬编码，不需要动态查询}}
    // 已删除 - 文件类型限制在前端配置

    // {{FUTURE_EXTENSION: 上传限制信息查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前上传限制在前端硬编码，不需要动态查询}}
    // 已删除 - 上传限制在前端配置

    // {{FUTURE_EXTENSION: 上传限制信息内部类 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 配合上传限制接口一起删除}}
    // 已删除 - 不再需要此内部类
}

// {{CHENGQI: 推广附件管理控制器创建完成}}
