package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 股票价格DTO，用于前端和后端之间的数据传输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockPriceDTO {
    /**
     * 股票价格ID
     * 添加时不需要，修改时必须
     */
    @NotNull(message = "股票价格ID不能为空", groups = {ValidationGroups.Update.class})
    private Long id;

    /**
     * 股票单价
     * 添加和修改时都必须
     */
    @NotNull(message = "股票单价不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @DecimalMin(value = "0.01", message = "股票单价必须大于0", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Digits(integer = 10, fraction = 2, message = "股票单价格式不正确，最多10位整数，2位小数", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private BigDecimal unitPrice;

    /**
     * 股票价格时间（年月日）
     * 添加和修改时都必须
     */
    @NotNull(message = "股票价格时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate time;

    /**
     * 备注
     * 可选字段
     */
    @Size(max = 255, message = "备注长度不能超过255个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String remark;

    /**
     * 创建时间（查询时返回）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间（查询时返回）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
