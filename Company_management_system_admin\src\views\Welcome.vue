<script setup>
import { ref, onMounted, computed, markRaw } from 'vue';
import { useAuthStore } from '../stores/token';
import { ElRow, ElCol, ElCard, ElStatistic, ElMessage, ElEmpty } from 'element-plus';
import {
    User,
    Briefcase,
    DataAnalysis,
    OfficeBuilding,
    Service,
    HomeFilled,
    Money,
    CollectionTag,
    List,
    DocumentCopy,
    TrendCharts
} from '@element-plus/icons-vue';
import { getDashboardStats } from '@/api/dashboard';

const authStore = useAuthStore();
const user = ref(null);
const loading = ref(false);
const currentDate = new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
});

// 仪表盘数据
const dashboardData = ref({
    employees: {
        total: 0,
        active: 0,
        inactive: 0,
    },
    departments: 0,
    positions: 0,
    performance: {
        total: 0,
        average: 0,
    },
});

// 定义所有可能的快速访问项
const masterQuickAccessItems = ref([
    { id: 'department', title: '部门管理', path: '/department', icon: markRaw(OfficeBuilding) },
    { id: 'employee', title: '员工管理', path: '/employee', icon: markRaw(User) },
    { id: 'client', title: '客户管理', path: '/client', icon: markRaw(Service) },
    { id: 'performance-analysis', title: '业绩分析', path: '/performance-analysis', icon: markRaw(DataAnalysis) },
    { id: 'salary', title: '工资管理', path: '/salary', icon: markRaw(Money) },
    { id: 'petty-cash', title: '备用金管理', path: '/petty-cash', icon: markRaw(CollectionTag) },
    { id: 'stock-management', title: '股票管理', path: '/stock-management', icon: markRaw(TrendCharts) },
    // 部门开销
    { id: 'department-expense', title: '部门开销', path: '/department-expense', icon: markRaw(List) },
    // 员工费用
    { id: 'employee-expense', title: '员工费用', path: '/employee-expense', icon: markRaw(DocumentCopy) },

]);

// 根据用户权限过滤快速访问项
const filteredQuickAccessItems = computed(() => {
    const accessibleIds = new Set(authStore.accessibleMenuItems || []);
    const items = masterQuickAccessItems.value.filter(item => accessibleIds.has(item.id));
    return items;
});

// 加载仪表盘数据
const loadDashboardData = async () => {
    loading.value = true;
    try {
        const res = await getDashboardStats();
        if (res.code === 200) {
            dashboardData.value = res.data;
        } else {
            ElMessage.error(res.msg || '获取仪表盘数据失败');
        }
    } catch (error) {
        console.error('加载仪表盘数据失败详情:', error);
        if (error && error.message && 
            (error.message.includes('登录已过期，请重新登录') || 
             error.message.includes('无法连接到服务器') ||
             error.message.includes('token无效或已过期'))) {
        } else {
            ElMessage.error('加载仪表盘数据失败: ' + (error.message || '未知错误'));
        }
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    user.value = authStore.user;
    loadDashboardData();
});
</script>

<template>
    <div class="welcome-container">
        <div class="welcome-header">
            <h2>欢迎来到中航物流管理系统</h2>
            <p class="date">{{ currentDate }}</p>
        </div>

        <div class="user-greeting">
            <h3>您好，{{ user?.name || '管理员' }}！</h3>
            <p>欢迎回到管理系统，祝您工作顺利。</p>
        </div>

        <div class="dashboard-summary">
            <el-row :gutter="20">
                <el-col
                    :xs="24"
                    :sm="12"
                    :md="6"
                >
                    <el-card
                        shadow="hover"
                        class="dashboard-card"
                        v-loading="loading"
                    >
                        <el-statistic
                            :value="dashboardData.employees.total"
                            title="员工总数"
                        >
                            <template #prefix>
                                <el-icon class="dashboard-icon">
                                    <User />
                                </el-icon>
                            </template>
                        </el-statistic>
                        <div class="dashboard-detail">
                            在职: {{ dashboardData.employees.active }} | 离职: {{ dashboardData.employees.inactive }}
                        </div>
                    </el-card>
                </el-col>

                <el-col
                    :xs="24"
                    :sm="12"
                    :md="6"
                >
                    <el-card
                        shadow="hover"
                        class="dashboard-card"
                        v-loading="loading"
                    >
                        <el-statistic
                            :value="dashboardData.departments"
                            title="部门数量"
                        >
                            <template #prefix>
                                <el-icon class="dashboard-icon">
                                    <OfficeBuilding />
                                </el-icon>
                            </template>
                        </el-statistic>
                    </el-card>
                </el-col>

                <el-col
                    :xs="24"
                    :sm="12"
                    :md="6"
                >
                    <el-card
                        shadow="hover"
                        class="dashboard-card"
                        v-loading="loading"
                    >
                        <el-statistic
                            :value="dashboardData.positions"
                            title="职位数量"
                        >
                            <template #prefix>
                                <el-icon class="dashboard-icon">
                                    <Briefcase />
                                </el-icon>
                            </template>
                        </el-statistic>
                    </el-card>
                </el-col>

                <el-col
                    :xs="24"
                    :sm="12"
                    :md="6"
                >
                    <el-card
                        shadow="hover"
                        class="dashboard-card"
                        v-loading="loading"
                    >
                        <el-statistic
                            :value="dashboardData.performance.total"
                            :precision="2"
                            title="总业绩(元)"
                        >
                            <template #prefix>
                                <el-icon class="dashboard-icon">
                                    <DataAnalysis />
                                </el-icon>
                            </template>
                        </el-statistic>
                        <div class="dashboard-detail">
                            平均: {{ dashboardData.performance.average.toFixed(2) }}元/人
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <div class="quick-access">
            <h3>快速访问</h3>
            <el-row :gutter="20" v-if="filteredQuickAccessItems.length > 0">
                <el-col
                    v-for="item in filteredQuickAccessItems"
                    :key="item.id"
                    :xs="24"
                    :sm="12" 
                    :md="8" 
                    :lg="6"
                >
                    <el-card
                        shadow="hover"
                        class="quick-card"
                        @click="$router.push(item.path)"
                    >
                        <div class="quick-icon" :style="{ color: item.color }"> 
                            <component :is="item.icon" />
                        </div>
                        <div class="quick-title">{{ item.title }}</div>
                    </el-card>
                </el-col>
            </el-row>
            <el-empty v-else description="暂无可用的快速访问项" />
        </div>
    </div>
</template>

<style scoped>
.welcome-container {
    padding: 20px;
}

.welcome-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.welcome-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 5px;
}

.date {
    color: #888;
    font-size: 14px;
}

.user-greeting {
    margin-bottom: 30px;
}

.user-greeting h3 {
    font-size: 20px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.user-greeting p {
    color: #666;
}

.dashboard-summary {
    margin-bottom: 40px;
}

.dashboard-card {
    height: 140px;
    border-radius: 8px;
    transition: all 0.3s;
    margin-bottom: 20px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-icon {
    font-size: 24px;
    margin-right: 8px;
    color: #1e3a8a;
}

.dashboard-detail {
    margin-top: 10px;
    font-size: 12px;
    color: #888;
}

.quick-access h3 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
}

.quick-card {
    height: 120px;
    text-align: center;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    transition: all 0.3s;
    margin-bottom: 20px;
}

.quick-card:hover {
    background-color: #1e3a8a;
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.quick-icon {
    font-size: 28px;
    margin-bottom: 10px;
}

.quick-title {
    font-size: 16px;
    font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .welcome-header h2 {
        font-size: 20px;
    }

    .user-greeting h3 {
        font-size: 18px;
    }

    .dashboard-card {
        height: 120px;
    }
}
</style> 