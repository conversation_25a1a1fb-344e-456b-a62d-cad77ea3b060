package org.example.company_management.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 会议时间验证注解
 * 验证开始时间和结束时间的逻辑关系
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MeetingTimeValidator.class)
@Documented
public @interface ValidMeetingTime {
    
    String message() default "会议时间设置不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
