-- =====================================================
-- 部门多负责人功能数据库迁移脚本
-- 创建时间: 2025-07-14 09:53:52 +08:00
-- 版本: v1.0
-- 描述: 将部门单负责人架构升级为多负责人架构，支持一个部门配置多个负责人
-- =====================================================

-- 使用公司管理系统数据库
USE `company_management_system`;

-- =====================================================
-- 执行前检查和准备工作
-- =====================================================

-- 1. 检查当前数据库版本和表结构
SELECT 
    TABLE_NAME, 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'company_management_system' 
  AND TABLE_NAME = 'department' 
  AND COLUMN_NAME = 'leader_id';

-- 2. 统计当前部门负责人数据
SELECT 
    COUNT(*) as total_departments,
    COUNT(leader_id) as departments_with_leaders,
    COUNT(*) - COUNT(leader_id) as departments_without_leaders
FROM department;

-- 3. 检查是否存在无效的负责人ID（防止外键约束问题）
SELECT d.department_id, d.department_name, d.leader_id
FROM department d
LEFT JOIN employee e ON d.leader_id = e.employee_id
WHERE d.leader_id IS NOT NULL AND e.employee_id IS NULL;

-- =====================================================
-- 第一步：创建部门负责人关联表
-- =====================================================

-- 创建部门负责人关联表
CREATE TABLE IF NOT EXISTS `department_leader`
(
    `id`            INT      NOT NULL AUTO_INCREMENT COMMENT '关联记录ID',
    `department_id` INT      NOT NULL COMMENT '部门ID',
    `employee_id`   INT      NOT NULL COMMENT '员工ID（负责人）',
    `leader_role`   VARCHAR(50)       DEFAULT 'PRIMARY' COMMENT '负责人角色：PRIMARY-主要负责人，DEPUTY-副负责人，ASSISTANT-协助负责人',
    `start_date`    DATE     NOT NULL DEFAULT (CURDATE()) COMMENT '任职开始日期',
    `end_date`      DATE              DEFAULT NULL COMMENT '任职结束日期（NULL表示当前在职）',
    `is_active`     BOOLEAN  NOT NULL DEFAULT TRUE COMMENT '是否激活状态',
    `remark`        VARCHAR(255)      DEFAULT '' COMMENT '备注信息',
    `create_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,
    
    -- 索引优化
    INDEX `idx_department_id` (`department_id`),
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_leader_role` (`leader_role`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_date_range` (`start_date`, `end_date`),
    INDEX `idx_dept_employee` (`department_id`, `employee_id`),
    INDEX `idx_dept_active` (`department_id`, `is_active`),
    INDEX `idx_employee_active` (`employee_id`, `is_active`),
    
    -- 防止重复添加同一员工为同一部门的活跃负责人
    UNIQUE KEY `uk_dept_employee_active` (`department_id`, `employee_id`, `is_active`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '部门负责人关联表';

-- =====================================================
-- 第二步：数据迁移
-- =====================================================

-- 迁移现有部门负责人数据到新的关联表
-- 只迁移有效的负责人数据（leader_id不为NULL且对应的员工存在）
INSERT INTO `department_leader` 
    (`department_id`, `employee_id`, `leader_role`, `start_date`, `is_active`, `remark`, `create_time`)
SELECT 
    d.department_id,
    d.leader_id,
    'PRIMARY' as leader_role,
    COALESCE(e.entry_date, CURDATE()) as start_date,  -- 使用员工入职日期作为任职开始日期
    TRUE as is_active,
    '从原department.leader_id字段迁移' as remark,
    NOW() as create_time
FROM department d
INNER JOIN employee e ON d.leader_id = e.employee_id  -- 确保员工存在
WHERE d.leader_id IS NOT NULL;

-- 验证数据迁移结果
SELECT 
    '数据迁移验证' as check_type,
    COUNT(*) as migrated_records
FROM department_leader 
WHERE remark LIKE '%从原department.leader_id字段迁移%';

-- =====================================================
-- 第三步：创建视图以保持向后兼容性（可选）
-- =====================================================

-- 创建视图以模拟原有的单负责人查询，便于现有代码的平滑过渡
CREATE OR REPLACE VIEW `department_with_primary_leader` AS
SELECT 
    d.department_id,
    d.department_name,
    d.department_description,
    d.parent_department_id,
    d.status,
    d.create_time,
    d.update_time,
    dl.employee_id as primary_leader_id,
    e.name as primary_leader_name
FROM department d
LEFT JOIN department_leader dl ON d.department_id = dl.department_id 
    AND dl.leader_role = 'PRIMARY' 
    AND dl.is_active = TRUE
LEFT JOIN employee e ON dl.employee_id = e.employee_id;

-- =====================================================
-- 第四步：数据完整性验证
-- =====================================================

-- 验证迁移后的数据完整性
SELECT 
    'Original departments with leaders' as description,
    COUNT(*) as count
FROM department 
WHERE leader_id IS NOT NULL

UNION ALL

SELECT 
    'Migrated department leaders' as description,
    COUNT(*) as count
FROM department_leader 
WHERE remark LIKE '%从原department.leader_id字段迁移%'

UNION ALL

SELECT 
    'Active department leaders' as description,
    COUNT(*) as count
FROM department_leader 
WHERE is_active = TRUE;

-- 检查是否有部门负责人数据丢失
SELECT 
    d.department_id,
    d.department_name,
    d.leader_id,
    CASE 
        WHEN dl.id IS NULL THEN '数据迁移失败'
        ELSE '数据迁移成功'
    END as migration_status
FROM department d
LEFT JOIN department_leader dl ON d.department_id = dl.department_id 
    AND dl.employee_id = d.leader_id 
    AND dl.is_active = TRUE
WHERE d.leader_id IS NOT NULL;

-- =====================================================
-- 第五步：删除原有字段（谨慎操作）
-- 注意：只有在确认数据迁移成功且前端代码已适配后才执行此步骤
-- =====================================================

-- 备份原有约束信息（用于回滚）
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'company_management_system'
  AND TABLE_NAME = 'department'
  AND COLUMN_NAME = 'leader_id'
  AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 删除外键约束
-- 注意：约束名称可能因MySQL版本而异，请根据实际情况调整
SET @sql = (
    SELECT CONCAT('ALTER TABLE `department` DROP FOREIGN KEY `', CONSTRAINT_NAME, '`;')
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = 'company_management_system'
      AND TABLE_NAME = 'department'
      AND COLUMN_NAME = 'leader_id'
      AND REFERENCED_TABLE_NAME IS NOT NULL
    LIMIT 1
);

-- 执行删除外键约束（如果存在）
SET @sql = IFNULL(@sql, 'SELECT "No foreign key constraint found for leader_id" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除leader_id字段
-- 注意：这是不可逆操作，请确保已完成数据迁移验证
-- ALTER TABLE `department` DROP COLUMN `leader_id`;

-- =====================================================
-- 第六步：创建常用查询的存储过程（可选）
-- =====================================================

-- 创建获取部门所有负责人的存储过程（MySQL兼容版本）
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `GetDepartmentLeaders`(
    IN dept_id INT,
    IN include_inactive TINYINT
)
BEGIN
    -- 如果include_inactive为NULL，默认设为0（FALSE）
    IF include_inactive IS NULL THEN
        SET include_inactive = 0;
    END IF;

    SELECT
        dl.id,
        dl.department_id,
        d.department_name,
        dl.employee_id,
        e.name as leader_name,
        e.email as leader_email,
        dl.leader_role,
        dl.start_date,
        dl.end_date,
        dl.is_active,
        dl.remark,
        dl.create_time,
        dl.update_time
    FROM department_leader dl
    INNER JOIN department d ON dl.department_id = d.department_id
    INNER JOIN employee e ON dl.employee_id = e.employee_id
    WHERE dl.department_id = dept_id
      AND (include_inactive = 1 OR dl.is_active = TRUE)
    ORDER BY
        CASE dl.leader_role
            WHEN 'PRIMARY' THEN 1
            WHEN 'DEPUTY' THEN 2
            WHEN 'ASSISTANT' THEN 3
            ELSE 4
        END,
        dl.start_date DESC;
END //

-- 创建获取员工负责部门的存储过程
CREATE PROCEDURE IF NOT EXISTS `GetEmployeeDepartments`(
    IN emp_id INT,
    IN include_inactive TINYINT
)
BEGIN
    -- 如果include_inactive为NULL，默认设为0（FALSE）
    IF include_inactive IS NULL THEN
        SET include_inactive = 0;
    END IF;

    SELECT
        dl.id,
        dl.department_id,
        d.department_name,
        d.department_description,
        dl.leader_role,
        dl.start_date,
        dl.end_date,
        dl.is_active,
        dl.remark
    FROM department_leader dl
    INNER JOIN department d ON dl.department_id = d.department_id
    WHERE dl.employee_id = emp_id
      AND (include_inactive = 1 OR dl.is_active = TRUE)
    ORDER BY dl.start_date DESC;
END //
DELIMITER ;

-- =====================================================
-- 第七步：回滚脚本（紧急情况使用）
-- =====================================================

-- 创建回滚脚本（注释状态，紧急情况下可执行）
/*
-- 回滚步骤1：重新添加leader_id字段
ALTER TABLE `department` ADD COLUMN `leader_id` INT AFTER `department_name`;

-- 回滚步骤2：从department_leader表恢复数据到leader_id字段
UPDATE department d
INNER JOIN department_leader dl ON d.department_id = dl.department_id
SET d.leader_id = dl.employee_id
WHERE dl.leader_role = 'PRIMARY' AND dl.is_active = TRUE;

-- 回滚步骤3：重新添加外键约束
ALTER TABLE `department`
    ADD CONSTRAINT `fk_department_leader`
        FOREIGN KEY (`leader_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE SET NULL ON UPDATE CASCADE;

-- 回滚步骤4：删除新创建的表和视图
DROP VIEW IF EXISTS `department_with_primary_leader`;
DROP PROCEDURE IF EXISTS `GetDepartmentLeaders`;
DROP PROCEDURE IF EXISTS `GetEmployeeDepartments`;
DROP TABLE IF EXISTS `department_leader`;
*/

-- =====================================================
-- 执行说明和注意事项
-- =====================================================

/*
执行步骤说明：
1. 备份数据库：执行任何迁移前必须完整备份数据库
2. 测试环境验证：先在测试环境执行此脚本，验证无误后再在生产环境执行
3. 分步执行：可以分步执行各个阶段，每个阶段完成后验证结果
4. 代码适配：在删除原leader_id字段前，确保前后端代码已适配新的表结构
5. 性能测试：新的关联查询可能影响性能，建议进行性能测试

注意事项：
- 第五步的删除原字段操作是不可逆的，请谨慎执行
- 建议保留兼容性视图一段时间，便于代码逐步迁移
- 存储过程可根据实际需要选择是否创建
- 如遇问题，可使用第七步的回滚脚本恢复原状态
*/

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT
    '部门多负责人功能迁移脚本准备完成' as status,
    NOW() as script_time,
    '请按照执行说明分步骤谨慎执行' as next_step,
    '执行前请务必备份数据库' as warning;
