package org.example.company_management.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.entity.DepartmentLeader;
import org.example.company_management.mapper.DepartmentLeaderMapper;
import org.example.company_management.service.DepartmentLeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 部门负责人服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class DepartmentLeaderServiceImpl implements DepartmentLeaderService {
    
    @Autowired
    private DepartmentLeaderMapper departmentLeaderMapper;
    
    // ========== 基础CRUD操作 ==========
    
    @Override
    @Transactional
    public void add(DepartmentLeader departmentLeader) {
        if (departmentLeader == null) {
            throw new IllegalArgumentException("部门负责人信息不能为空");
        }
        
        if (departmentLeader.getDepartmentId() == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }
        
        if (departmentLeader.getEmployeeId() == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }
        
        // 设置默认值
        if (!StringUtils.hasText(departmentLeader.getLeaderRole())) {
            departmentLeader.setLeaderRole("PRIMARY");
        }
        
        if (departmentLeader.getStartDate() == null) {
            departmentLeader.setStartDate(new Date());
        }
        
        if (departmentLeader.getIsActive() == null) {
            departmentLeader.setIsActive(true);
        }
        
        if (departmentLeader.getRemark() == null) {
            departmentLeader.setRemark("");
        }
        
        // 设置创建和更新时间
        Date now = new Date();
        departmentLeader.setCreateTime(now);
        departmentLeader.setUpdateTime(now);
        
        // 检查是否已存在相同的关联关系
        boolean exists = departmentLeaderMapper.checkIsLeader(
            departmentLeader.getDepartmentId(), 
            departmentLeader.getEmployeeId()
        );
        
        if (exists) {
            throw new IllegalArgumentException("该员工已经是该部门的负责人");
        }
        
        // 如果是主要负责人，需要检查是否已有主要负责人
        if ("PRIMARY".equals(departmentLeader.getLeaderRole())) {
            DepartmentLeader existingPrimary = departmentLeaderMapper.selectPrimaryLeaderByDepartmentId(
                departmentLeader.getDepartmentId()
            );
            if (existingPrimary != null) {
                throw new IllegalArgumentException("该部门已有主要负责人，请先移除现有主要负责人或设置为其他角色");
            }
        }
        
        int result = departmentLeaderMapper.insert(departmentLeader);
        if (result <= 0) {
            throw new RuntimeException("添加部门负责人失败");
        }
        
        log.info("成功添加部门负责人：部门ID={}, 员工ID={}, 角色={}", 
                departmentLeader.getDepartmentId(), 
                departmentLeader.getEmployeeId(), 
                departmentLeader.getLeaderRole());
    }
    
    @Override
    @Transactional
    public void deleteById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("关联记录ID不能为空");
        }
        
        DepartmentLeader departmentLeader = departmentLeaderMapper.selectById(id);
        if (departmentLeader == null) {
            throw new IllegalArgumentException("部门负责人关联记录不存在");
        }
        
        int result = departmentLeaderMapper.deleteById(id);
        if (result <= 0) {
            throw new RuntimeException("删除部门负责人关联失败");
        }
        
        log.info("成功删除部门负责人关联：ID={}, 部门ID={}, 员工ID={}", 
                id, departmentLeader.getDepartmentId(), departmentLeader.getEmployeeId());
    }
    
    @Override
    @Transactional
    public void removeDepartmentLeader(Integer departmentId, Integer employeeId) {
        if (departmentId == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }
        
        if (employeeId == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }
        
        int result = departmentLeaderMapper.deleteByDepartmentAndEmployee(departmentId, employeeId);
        if (result <= 0) {
            throw new RuntimeException("删除部门负责人关联失败，可能该关联不存在");
        }
        
        log.info("成功删除部门负责人关联：部门ID={}, 员工ID={}", departmentId, employeeId);
    }
    
    @Override
    @Transactional
    public void update(DepartmentLeader departmentLeader) {
        if (departmentLeader == null || departmentLeader.getId() == null) {
            throw new IllegalArgumentException("部门负责人信息或ID不能为空");
        }
        
        // 检查记录是否存在
        DepartmentLeader existing = departmentLeaderMapper.selectById(departmentLeader.getId());
        if (existing == null) {
            throw new IllegalArgumentException("部门负责人关联记录不存在");
        }
        
        // 设置更新时间
        departmentLeader.setUpdateTime(new Date());
        
        // 如果角色变更为主要负责人，需要检查是否已有其他主要负责人
        if ("PRIMARY".equals(departmentLeader.getLeaderRole()) && 
            !departmentLeader.getId().equals(existing.getId())) {
            
            DepartmentLeader existingPrimary = departmentLeaderMapper.selectPrimaryLeaderByDepartmentId(
                departmentLeader.getDepartmentId()
            );
            
            if (existingPrimary != null && !existingPrimary.getId().equals(departmentLeader.getId())) {
                throw new IllegalArgumentException("该部门已有其他主要负责人，请先调整现有主要负责人的角色");
            }
        }
        
        int result = departmentLeaderMapper.updateById(departmentLeader);
        if (result <= 0) {
            throw new RuntimeException("更新部门负责人信息失败");
        }
        
        log.info("成功更新部门负责人信息：ID={}, 部门ID={}, 员工ID={}", 
                departmentLeader.getId(), 
                departmentLeader.getDepartmentId(), 
                departmentLeader.getEmployeeId());
    }
    
    @Override
    public DepartmentLeader getById(Integer id) {
        if (id == null) {
            return null;
        }
        return departmentLeaderMapper.selectById(id);
    }
    
    // ========== 业务查询方法 ==========
    
    @Override
    public List<DepartmentLeader> getDepartmentLeaders(Integer departmentId, Boolean includeInactive) {
        if (departmentId == null) {
            return new ArrayList<>();
        }
        return departmentLeaderMapper.selectByDepartmentId(departmentId, includeInactive);
    }
    
    @Override
    public List<DepartmentLeader> getActiveDepartmentLeaders(Integer departmentId) {
        return getDepartmentLeaders(departmentId, false);
    }
    
    @Override
    public List<DepartmentLeader> getEmployeeDepartments(Integer employeeId, Boolean includeInactive) {
        if (employeeId == null) {
            return new ArrayList<>();
        }
        return departmentLeaderMapper.selectByEmployeeId(employeeId, includeInactive);
    }
    
    @Override
    public List<DepartmentLeader> getActiveEmployeeDepartments(Integer employeeId) {
        return getEmployeeDepartments(employeeId, false);
    }
    
    @Override
    public DepartmentLeader getPrimaryLeader(Integer departmentId) {
        if (departmentId == null) {
            return null;
        }
        return departmentLeaderMapper.selectPrimaryLeaderByDepartmentId(departmentId);
    }
    
    @Override
    public List<Integer> getActiveLeaderIds(Integer departmentId) {
        if (departmentId == null) {
            return new ArrayList<>();
        }
        return departmentLeaderMapper.selectActiveLeaderIdsByDepartmentId(departmentId);
    }
    
    @Override
    public List<Integer> getActiveDepartmentIds(Integer employeeId) {
        if (employeeId == null) {
            return new ArrayList<>();
        }
        return departmentLeaderMapper.selectActiveDepartmentIdsByEmployeeId(employeeId);
    }

    // ========== 权限检查方法 ==========

    @Override
    public boolean isLeader(Integer departmentId, Integer employeeId) {
        if (departmentId == null || employeeId == null) {
            return false;
        }
        return departmentLeaderMapper.checkIsLeader(departmentId, employeeId);
    }

    @Override
    public boolean isPrimaryLeader(Integer departmentId, Integer employeeId) {
        if (departmentId == null || employeeId == null) {
            return false;
        }
        return departmentLeaderMapper.checkIsPrimaryLeader(departmentId, employeeId);
    }

    @Override
    public boolean isAnyDepartmentLeader(Integer employeeId) {
        if (employeeId == null) {
            return false;
        }
        List<Integer> departmentIds = getActiveDepartmentIds(employeeId);
        return !departmentIds.isEmpty();
    }

    // ========== 批量操作方法 ==========

    @Override
    @Transactional
    public void batchAdd(List<DepartmentLeader> departmentLeaders) {
        if (departmentLeaders == null || departmentLeaders.isEmpty()) {
            return;
        }

        // 验证每个记录
        Date now = new Date();
        for (DepartmentLeader departmentLeader : departmentLeaders) {
            if (departmentLeader.getDepartmentId() == null) {
                throw new IllegalArgumentException("部门ID不能为空");
            }
            if (departmentLeader.getEmployeeId() == null) {
                throw new IllegalArgumentException("员工ID不能为空");
            }

            // 设置默认值
            if (!StringUtils.hasText(departmentLeader.getLeaderRole())) {
                departmentLeader.setLeaderRole("PRIMARY");
            }
            if (departmentLeader.getStartDate() == null) {
                departmentLeader.setStartDate(now);
            }
            if (departmentLeader.getIsActive() == null) {
                departmentLeader.setIsActive(true);
            }
            if (departmentLeader.getRemark() == null) {
                departmentLeader.setRemark("");
            }
            departmentLeader.setCreateTime(now);
            departmentLeader.setUpdateTime(now);
        }

        int result = departmentLeaderMapper.batchInsert(departmentLeaders);
        if (result <= 0) {
            throw new RuntimeException("批量添加部门负责人失败");
        }

        log.info("成功批量添加部门负责人：数量={}", departmentLeaders.size());
    }

    @Override
    @Transactional
    public void setDepartmentLeaders(Integer departmentId, List<Integer> leaderIds, Integer primaryLeaderId) {
        if (departmentId == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        // 删除现有的所有负责人
        departmentLeaderMapper.deleteByDepartmentId(departmentId);

        if (leaderIds != null && !leaderIds.isEmpty()) {
            List<DepartmentLeader> departmentLeaders = new ArrayList<>();
            Date now = new Date();

            for (Integer employeeId : leaderIds) {
                DepartmentLeader departmentLeader = new DepartmentLeader();
                departmentLeader.setDepartmentId(departmentId);
                departmentLeader.setEmployeeId(employeeId);

                // 设置角色
                if (employeeId.equals(primaryLeaderId)) {
                    departmentLeader.setLeaderRole("PRIMARY");
                } else {
                    departmentLeader.setLeaderRole("DEPUTY");
                }

                departmentLeader.setStartDate(now);
                departmentLeader.setIsActive(true);
                departmentLeader.setRemark("批量设置");
                departmentLeader.setCreateTime(now);
                departmentLeader.setUpdateTime(now);

                departmentLeaders.add(departmentLeader);
            }

            batchAdd(departmentLeaders);
        }

        log.info("成功设置部门负责人：部门ID={}, 负责人数量={}", departmentId,
                leaderIds != null ? leaderIds.size() : 0);
    }

    @Override
    @Transactional
    public void updateActiveStatus(Integer departmentId, Integer employeeId, Boolean isActive) {
        if (departmentId == null || employeeId == null || isActive == null) {
            throw new IllegalArgumentException("部门ID、员工ID和激活状态不能为空");
        }

        int result = departmentLeaderMapper.updateActiveStatus(departmentId, employeeId, isActive);
        if (result <= 0) {
            throw new RuntimeException("更新部门负责人激活状态失败");
        }

        log.info("成功更新部门负责人激活状态：部门ID={}, 员工ID={}, 激活状态={}",
                departmentId, employeeId, isActive);
    }

    // ========== 兼容性方法（用于向后兼容） ==========

    @Override
    public Integer getPrimaryLeaderId(Integer departmentId) {
        DepartmentLeader primaryLeader = getPrimaryLeader(departmentId);
        return primaryLeader != null ? primaryLeader.getEmployeeId() : null;
    }

    @Override
    @Transactional
    public void setPrimaryLeader(Integer departmentId, Integer employeeId) {
        if (departmentId == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        if (employeeId == null) {
            // 如果员工ID为空，删除所有主要负责人
            List<DepartmentLeader> primaryLeaders = departmentLeaderMapper.selectByDepartmentId(departmentId, false);
            for (DepartmentLeader leader : primaryLeaders) {
                if ("PRIMARY".equals(leader.getLeaderRole())) {
                    departmentLeaderMapper.deleteById(leader.getId());
                }
            }
            return;
        }

        // 检查是否已经是负责人
        boolean isExistingLeader = isLeader(departmentId, employeeId);

        if (isExistingLeader) {
            // 如果已经是负责人，更新为主要负责人
            List<DepartmentLeader> leaders = departmentLeaderMapper.selectByDepartmentId(departmentId, false);
            for (DepartmentLeader leader : leaders) {
                if (leader.getEmployeeId().equals(employeeId)) {
                    leader.setLeaderRole("PRIMARY");
                    leader.setUpdateTime(new Date());
                    departmentLeaderMapper.updateById(leader);
                } else if ("PRIMARY".equals(leader.getLeaderRole())) {
                    // 将其他主要负责人降级为副负责人
                    leader.setLeaderRole("DEPUTY");
                    leader.setUpdateTime(new Date());
                    departmentLeaderMapper.updateById(leader);
                }
            }
        } else {
            // 如果不是负责人，添加为主要负责人
            DepartmentLeader newLeader = new DepartmentLeader();
            newLeader.setDepartmentId(departmentId);
            newLeader.setEmployeeId(employeeId);
            newLeader.setLeaderRole("PRIMARY");
            newLeader.setStartDate(new Date());
            newLeader.setIsActive(true);
            newLeader.setRemark("兼容性设置");

            add(newLeader);
        }

        log.info("成功设置部门主要负责人：部门ID={}, 员工ID={}", departmentId, employeeId);
    }
}
