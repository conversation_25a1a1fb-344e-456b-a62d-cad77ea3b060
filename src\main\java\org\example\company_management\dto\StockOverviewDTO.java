package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 持股总览DTO，用于展示员工持股情况的汇总信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOverviewDTO {
    
    /**
     * 员工ID
     */
    private Long employeeId;
    
    /**
     * 员工姓名
     */
    private String employeeName;
    
    /**
     * 部门ID
     */
    private Long departmentId;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 已解禁股票数量
     */
    private Integer unlockedQuantity;
    
    /**
     * 未解禁股票数量
     */
    private Integer lockedQuantity;
    
    /**
     * 剩余可提现数量
     */
    private Integer availableQuantity;
    
    /**
     * 已提现股票数量
     */
    private Integer withdrawnQuantity;
    
    /**
     * 已提现总金额（按历史价格计算）
     */
    private BigDecimal withdrawnTotalValue;
    
    /**
     * 获取总持股数量
     */
    public Integer getTotalQuantity() {
        return (unlockedQuantity != null ? unlockedQuantity : 0) + 
               (lockedQuantity != null ? lockedQuantity : 0);
    }
    
    /**
     * 计算总持股价值（按当前股价）
     */
    public BigDecimal getTotalValue(BigDecimal currentPrice) {
        if (currentPrice == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(getTotalQuantity()).multiply(currentPrice);
    }
    
    /**
     * 计算已解禁股票价值（按当前股价）
     */
    public BigDecimal getUnlockedValue(BigDecimal currentPrice) {
        if (currentPrice == null || unlockedQuantity == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(unlockedQuantity).multiply(currentPrice);
    }
    
    /**
     * 计算未解禁股票价值（按当前股价）
     */
    public BigDecimal getLockedValue(BigDecimal currentPrice) {
        if (currentPrice == null || lockedQuantity == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(lockedQuantity).multiply(currentPrice);
    }
    
    /**
     * 计算可提现股票价值（按当前股价）
     */
    public BigDecimal getAvailableValue(BigDecimal currentPrice) {
        if (currentPrice == null || availableQuantity == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(availableQuantity).multiply(currentPrice);
    }
}
