package org.example.company_management.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.mapper.EmployeeStockSummaryMapper;
import org.example.company_management.service.EmployeeStockService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 股票汇总表维护定时任务
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StockSummaryMaintenanceTask {
    
    private final EmployeeStockService employeeStockService;
    private final EmployeeStockSummaryMapper employeeStockSummaryMapper;
    
    /**
     * 每天凌晨2点执行数据一致性检查和修复
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyConsistencyCheck() {
        log.info("开始执行股票汇总表数据一致性检查");
        
        try {
            // 检查数据一致性
            List<Map<String, Object>> inconsistentData = employeeStockService.checkStockSummaryConsistency();
            
            if (inconsistentData.isEmpty()) {
                log.info("股票汇总表数据一致性检查完成：数据一致");
            } else {
                log.warn("发现{}条不一致的股票汇总数据，开始修复", inconsistentData.size());
                
                // 重新计算所有数据
                int fixedCount = employeeStockService.recalculateAllStockSummary();
                log.info("股票汇总表数据修复完成：修复了{}条记录", fixedCount);
                
                // 再次检查
                List<Map<String, Object>> remainingInconsistent = employeeStockService.checkStockSummaryConsistency();
                if (remainingInconsistent.isEmpty()) {
                    log.info("股票汇总表数据修复成功：所有数据已一致");
                } else {
                    log.error("股票汇总表数据修复后仍有{}条不一致记录，需要人工检查", remainingInconsistent.size());
                }
            }
            
        } catch (Exception e) {
            log.error("股票汇总表数据一致性检查失败", e);
        }
    }
    
    /**
     * 每小时检查是否有需要重新计算的数据（超过6小时未更新的记录）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyRecalculation() {
        log.info("开始执行股票汇总表定时重新计算");
        
        try {
            // 查找超过6小时未更新的记录
            List<Integer> needRecalculateEmployees = employeeStockSummaryMapper.selectNeedRecalculate(6);
            
            if (needRecalculateEmployees.isEmpty()) {
                log.info("股票汇总表定时重新计算完成：无需重新计算的记录");
            } else {
                log.info("发现{}个员工的股票汇总数据需要重新计算", needRecalculateEmployees.size());
                
                int successCount = 0;
                for (Integer employeeId : needRecalculateEmployees) {
                    try {
                        boolean success = employeeStockService.recalculateStockSummary(employeeId);
                        if (success) {
                            successCount++;
                        }
                    } catch (Exception e) {
                        log.error("重新计算员工股票汇总数据失败: employeeId={}", employeeId, e);
                    }
                }
                
                log.info("股票汇总表定时重新计算完成：成功重新计算{}个员工的数据", successCount);
            }
            
        } catch (Exception e) {
            log.error("股票汇总表定时重新计算失败", e);
        }
    }
    
    /**
     * 每周日凌晨3点执行全量重新计算（保险措施）
     */
    @Scheduled(cron = "0 0 3 * * SUN")
    public void weeklyFullRecalculation() {
        log.info("开始执行股票汇总表全量重新计算");
        
        try {
            int recalculatedCount = employeeStockService.recalculateAllStockSummary();
            log.info("股票汇总表全量重新计算完成：重新计算了{}条记录", recalculatedCount);
            
            // 获取统计信息
            Map<String, Object> statistics = employeeStockSummaryMapper.getStatistics();
            log.info("股票汇总表统计信息：{}", statistics);
            
        } catch (Exception e) {
            log.error("股票汇总表全量重新计算失败", e);
        }
    }
    
    /**
     * 手动触发数据一致性检查（用于测试和紧急修复）
     */
    public void manualConsistencyCheck() {
        log.info("手动触发股票汇总表数据一致性检查");
        dailyConsistencyCheck();
    }
    
    /**
     * 手动触发全量重新计算（用于测试和紧急修复）
     */
    public void manualFullRecalculation() {
        log.info("手动触发股票汇总表全量重新计算");
        weeklyFullRecalculation();
    }
}
