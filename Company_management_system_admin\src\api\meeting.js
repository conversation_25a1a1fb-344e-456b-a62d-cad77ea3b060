import request from '@/utils/request'

/**
 * 会议管理API
 */

// 分页查询会议列表
export function getMeetingPage(params) {
  return request({
    url: '/admin/meeting',
    method: 'get',
    params
  })
}

// 根据ID获取会议详情
export function getMeetingById(id) {
  return request({
    url: `/admin/meeting/${id}`,
    method: 'get'
  })
}

// 创建会议
export function createMeeting(data) {
  return request({
    url: '/admin/meeting',
    method: 'post',
    data
  })
}

// 更新会议
export function updateMeeting(id, data) {
  return request({
    url: `/admin/meeting/${id}`,
    method: 'put',
    data
  })
}

// 删除会议
export function deleteMeeting(id) {
  return request({
    url: `/admin/meeting/${id}`,
    method: 'delete'
  })
}

// 状态更新API已移除，现在由系统根据时间自动管理
