<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑会议' : '新增会议'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 使用 v-if 确保表单在对话框关闭时完全销毁，避免验证警告 -->
    <el-form
      v-if="dialogVisible"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议主题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入会议主题"
              maxlength="200"
              show-word-limit
              class="text-overflow-input"
              :title="formData.title"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              placeholder="请先选择会议地点"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :disabled="!formData.locationId"
              :disabled-date="disabledDate"
              :disabledHours="getDisabledStartHours"
              :disabledMinutes="getDisabledStartMinutes"
              :disabledSeconds="getDisabledStartSeconds"
              @change="handleStartTimeChange"
            />

            <!-- 时间选择提示信息 -->
            <div v-if="!formData.locationId" class="time-selection-hint">
              <el-text size="small" type="info">
                <el-icon><InfoFilled /></el-icon>
                请先选择会议地点后再选择时间
              </el-text>
            </div>

            <!-- 统一的时间信息展示容器 -->
            <div v-else-if="locationAvailability && !locationAvailability.errorMode" class="info-display-container">
              <!-- 可用时间提示 -->
              <el-text size="small" type="success">
                <el-icon><SuccessFilled /></el-icon>
                可选时间: {{ getAvailableTimeText() }}
              </el-text>

              <!-- 日期范围限制提示 -->
              <div v-if="getDateRangeRestrictionText()" class="date-range-restriction">
                <el-text size="small" type="warning">
                  <el-icon><InfoFilled /></el-icon>
                  {{ getDateRangeRestrictionText() }}
                </el-text>
              </div>

              <!-- 已占用时间段详细显示 -->
              <div v-if="locationAvailability.bookedTimeSlots && locationAvailability.bookedTimeSlots.length > 0">
                <el-text size="small" type="info">
                  <el-icon><InfoFilled /></el-icon>
                  已预定时间段:
                </el-text>
                <div class="booked-time-list">
                  <div v-for="slot in getDetailedBookedSlots()"
                       :key="slot.meetingId"
                       class="booked-time-item">
                    <span class="time-tag text-overflow-span"
                          :title="formatDetailedTimeSlotWithBooker(slot.startTime, slot.endTime, slot.bookedByName)">
                      {{ formatDetailedTimeSlotWithBooker(slot.startTime, slot.endTime, slot.bookedByName) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div v-else-if="locationAvailability && locationAvailability.errorMode" class="time-selection-warning">
              <el-text size="small" type="warning">
                <el-icon><Warning /></el-icon>
                时间冲突检测不可用，建议选择工作时间 (08:00-18:00)
              </el-text>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="datetime"
              placeholder="选择会议结束时间（可选）"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :disabled="!formData.locationId"
              :disabled-date="disabledDate"
              :disabledHours="getDisabledEndHours"
              :disabledMinutes="getDisabledEndMinutes"
              :disabledSeconds="getDisabledEndSeconds"
            />

            <!-- 结束时间提示 -->
            <div v-if="!formData.locationId" class="time-selection-hint">
              <el-text size="small" type="info">
                <el-icon><InfoFilled /></el-icon>
                请先选择会议地点
              </el-text>
            </div>
            <div v-else-if="!formData.startTime" class="time-selection-hint">
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会议地点" prop="locationId">
            <el-select
              v-model="formData.locationId"
              placeholder="请选择会议地点"
              filterable
              clearable
              style="width: 100%"
              class="text-overflow-select"
              @change="handleLocationChange"
            >
              <el-option
                v-for="location in locationOptions"
                :key="location.id"
                :label="location.status === 'INACTIVE' ? `${location.fullDisplay} (已禁用)` : location.fullDisplay"
                :value="location.id"
                :disabled="location.status === 'INACTIVE'"
                :class="{ 'disabled-location': location.status === 'INACTIVE' }"
                :title="location.status === 'INACTIVE' ? `${location.fullDisplay} (已禁用)` : location.fullDisplay"
              />
            </el-select>

            <!-- 地点可用性提示 -->
            <div v-if="formData.locationId && locationAvailability" class="location-availability-info">
              <!-- 错误模式提示 -->
              <el-alert v-if="locationAvailability.errorMode"
                        title="时间冲突检测功能暂时不可用"
                        type="warning"
                        size="small"
                        :closable="false"
                        show-icon>
                <template #default>
                  当前显示的是默认开放时间，请手动确认时间安排，避免冲突
                  <el-button type="primary" size="small" text @click="retryFetchAvailability"
                             :loading="availabilityLoading" style="margin-left: 10px;">
                    重试获取
                  </el-button>
                </template>
              </el-alert>




            </div>

            <div v-if="availabilityLoading" class="location-availability-loading">
              <el-text size="small" type="info">
                <el-icon class="is-loading"><Loading /></el-icon>
                正在检查地点可用性...
              </el-text>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会议负责人" prop="responsibleId">
            <el-select
              v-model="formData.responsibleId"
              placeholder="请选择会议负责人"
              @change="handleResponsibleChange"
              remote
              filterable
              :remote-method="handleResponsibleSearch"
              :loading="responsibleLoading"
              clearable
              style="width: 100%"
              class="text-overflow-select"
              popper-class="employee-select-dropdown"
              @visible-change="handleResponsibleSelectVisibleChange"
            >
              <template #empty>
                <div class="empty-text">
                  <p v-if="!responsibleSearchQuery && responsibleOptions.length === 0">
                    点击下拉加载负责人列表
                  </p>
                  <p v-else-if="responsibleSearchQuery && responsibleOptions.length === 0">
                    未找到匹配的负责人
                  </p>
                  <p v-else>暂无数据</p>
                </div>
              </template>

              <!-- 负责人选项列表 -->
              <div
                v-if="responsibleOptions.length > 0"
                class="employee-options-container"
                @scroll="handleResponsibleScroll"
              >
                <el-option
                  v-for="employee in responsibleOptions"
                  :key="employee.employeeId"
                  :label="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : '')) + (employee.isResigned ? '（已离职）' : '')"
                  :value="employee.employeeId"
                  :class="{ 'resigned-employee': employee.isResigned }"
                  :title="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : '')) + (employee.isResigned ? '（已离职）' : '')"
                />

                <!-- 加载更多提示 -->
                <div
                  v-if="responsibleHasMore"
                  class="load-more-option"
                  @click="loadMoreResponsibleEmployees"
                >
                  <el-icon v-if="responsibleLoading" class="is-loading">
                    <Loading />
                  </el-icon>
                  <span>{{ responsibleLoading ? '加载中...' : '滚动或点击加载更多' }}</span>
                </div>

                <!-- 没有更多数据提示 -->
                <div v-else class="no-more-data">
                  <span>已显示全部 {{ responsibleOptions.length }} 个员工</span>
                </div>
              </div>
            </el-select>
            <div class="el-form-item__help">
              <span v-if="responsibleOptions.length > 0" class="help-text">
                已显示 {{ responsibleOptions.length }} 个员工{{ responsibleHasMore ? '，滚动或点击加载更多' : '' }}
              </span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 状态字段已移除，现在由系统根据时间自动管理 -->

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议内容" prop="content">
            <el-input
              v-model="formData.content"
              type="textarea"
              :rows="4"
              placeholder="请输入会议内容描述"
              maxlength="5000"
              show-word-limit
              class="text-overflow-textarea"
              :title="formData.content"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="参与者" prop="participants">
            <div class="participants-section">
              <!-- 参与者选择区域 -->
              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="participant-group">
                    <label class="group-label">选择部门：</label>
                <el-select
                  v-model="selectedDepartments"
                  placeholder="搜索并选择部门"
                  filterable
                  remote
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  :remote-method="handleDepartmentSearch"
                  :loading="departmentLoading"
                  clearable
                  class="participant-select"
                  popper-class="department-select-dropdown"
                  @visible-change="handleDepartmentSelectVisibleChange"
                  @change="handleDepartmentChange"
                >
                  <template #empty>
                    <div class="empty-text">
                      <p v-if="!departmentSearchQuery && departmentOptions.length === 0">
                        点击下拉加载部门列表
                      </p>
                      <p v-else-if="departmentSearchQuery && departmentOptions.length === 0">
                        未找到匹配的部门
                      </p>
                      <p v-else>暂无数据</p>
                    </div>
                  </template>

                  <!-- 部门选项列表 -->
                  <div
                    v-if="departmentOptions.length > 0"
                    class="participant-options-container"
                    @scroll="handleDepartmentScroll"
                  >
                    <template v-for="dept in departmentOptions" :key="dept?.departmentId || dept?.id || Math.random()">
                      <el-option
                        v-if="dept && (dept?.departmentId || dept?.id)"
                        :label="dept?.departmentName || ''"
                        :value="dept?.departmentId || dept?.id"
                      />
                    </template>

                    <!-- 加载更多提示 -->
                    <div
                      v-if="departmentHasMore"
                      class="load-more-option"
                      @click="loadMoreDepartments"
                    >
                      <el-icon v-if="departmentLoading" class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>{{ departmentLoading ? '加载中...' : '滚动或点击加载更多' }}</span>
                    </div>

                    <!-- 没有更多数据提示 -->
                    <div v-else class="no-more-data">
                      <span>已显示全部 {{ departmentOptions.length }} 个部门</span>
                    </div>
                  </div>
                </el-select>
                <div class="el-form-item__help">
                  <span v-if="departmentOptions.length > 0" class="help-text">
                    已显示 {{ departmentOptions.length }} 个部门{{ departmentHasMore ? '，滚动或点击加载更多' : '' }}
                  </span>
                </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="participant-group">
                    <label class="group-label">选择员工：</label>
                <el-select
                  v-model="selectedEmployees"
                  placeholder="搜索并选择员工"
                  filterable
                  remote
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  :remote-method="handleEmployeeSearch"
                  :loading="employeeLoading"
                  clearable
                  class="participant-select"
                  popper-class="participant-employee-select-dropdown"
                  @visible-change="handleEmployeeSelectVisibleChange"
                  @change="handleEmployeeChange"
                >
                  <template #empty>
                    <div class="empty-text">
                      <p v-if="!employeeSearchQuery && employeeOptions.length === 0">
                        点击下拉加载员工列表
                      </p>
                      <p v-else-if="employeeSearchQuery && employeeOptions.length === 0">
                        未找到匹配的员工
                      </p>
                      <p v-else>暂无数据</p>
                    </div>
                  </template>

                  <!-- 员工选项列表 -->
                  <div
                    v-if="employeeOptions.length > 0"
                    class="participant-options-container"
                    @scroll="handleEmployeeScroll"
                  >
                    <template v-for="emp in employeeOptions" :key="emp?.employeeId || emp?.id || Math.random()">
                      <el-option
                        v-if="emp && (emp?.employeeId || emp?.id)"
                        :label="(emp?.name || '') + (emp?.departmentName ? ` (${emp?.departmentName}${emp?.positionName ? '-' + emp?.positionName : ''})` : (emp?.positionName ? ` (${emp?.positionName})` : '')) + (emp?.isResigned ? '（已离职）' : '')"
                        :value="emp?.employeeId || emp?.id"
                        :class="{ 'resigned-employee': emp?.isResigned }"
                      />
                    </template>

                    <!-- 加载更多提示 -->
                    <div
                      v-if="employeeHasMore"
                      class="load-more-option"
                      @click="loadMoreEmployees"
                    >
                      <el-icon v-if="employeeLoading" class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>{{ employeeLoading ? '加载中...' : '滚动或点击加载更多' }}</span>
                    </div>

                    <!-- 没有更多数据提示 -->
                    <div v-else class="no-more-data">
                      <span>已显示全部 {{ employeeOptions.length }} 个员工</span>
                    </div>
                  </div>
                </el-select>
                <div class="el-form-item__help">
                  <span v-if="employeeOptions.length > 0" class="help-text">
                    已显示 {{ employeeOptions.length }} 个员工{{ employeeHasMore ? '，滚动或点击加载更多' : '' }}
                  </span>
                </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 已选择的参与者列表 -->
              <div class="participant-list" v-if="formData.participants.length > 0">
                <div class="list-header">已选择的参与者：</div>
                <el-tag
                  v-for="(participant, index) in formData.participants"
                  :key="`${participant.participantType}-${participant.participantId}`"
                  :type="participant.participantType === 'DEPARTMENT' ? 'success' : 'primary'"
                  closable
                  @close="removeParticipant(index)"
                  style="margin: 5px 5px 0 0"
                >
                  {{ participant.participantType === 'DEPARTMENT' ? '部门' : '员工' }}: {{ participant.participantName }}
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Loading, Clock, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'
import { createMeeting, updateMeeting } from '@/api/meeting'
import { getDepartmentList } from '@/api/department'
import { getEmployeePage } from '@/api/employee'
import { getActiveLocations, getAllLocations, getLocationAvailability } from '@/api/meetingLocation'



// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  meetingData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

// 部门选择相关
const selectedDepartments = ref([])
const departmentOptions = ref([])
const departmentLoading = ref(false)
const departmentSearchQuery = ref('')
const departmentCurrentPage = ref(1)
const departmentPageSize = ref(20)
const departmentHasMore = ref(true)

// 员工选择相关
const selectedEmployees = ref([])
const employeeOptions = ref([])
const employeeLoading = ref(false)
const employeeSearchQuery = ref('')
const employeeCurrentPage = ref(1)
const employeePageSize = ref(20)
const employeeHasMore = ref(true)

// 地点选择相关
const locationOptions = ref([])
const locationLoading = ref(false)

// 地点可用性相关
const locationAvailability = ref(null)
const availabilityLoading = ref(false)
const availabilityCache = ref(new Map()) // 缓存可用性数据

// 负责人选择相关
const responsibleOptions = ref([])
const responsibleLoading = ref(false)
const responsibleSearchQuery = ref('')
const responsibleCurrentPage = ref(1)
const responsiblePageSize = ref(20)
const responsibleHasMore = ref(true)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.meetingData?.id)

// 表单数据
const formData = reactive({
  id: null,
  title: '',
  content: '',
  startTime: '',
  endTime: '',
  location: '',
  locationId: null,
  responsibleId: null,
  status: 'NOT_STARTED',
  participants: []
})

// 时间验证函数
const validateEndTime = (rule, value, callback) => {
  if (!value) {
    // 结束时间是可选的
    callback()
    return
  }

  if (!formData.startTime) {
    callback(new Error('请先选择开始时间'))
    return
  }

  const startTime = new Date(formData.startTime)
  const endTime = new Date(value)

  if (endTime <= startTime) {
    callback(new Error('结束时间必须晚于开始时间'))
    return
  }

  callback()
}

// 禁用过去的日期和不可用的日期
const disabledDate = (time) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 禁用过去的日期
  if (time.getTime() < today.getTime()) {
    return true
  }

  // 如果没有选择地点或没有可用性信息，只禁用过去日期
  if (!formData.locationId || !locationAvailability.value) {
    return false
  }

  const availability = locationAvailability.value

  // 检查地点日期范围限制
  if (availability.availableStartDate || availability.availableEndDate) {
    const selectedDate = new Date(time)
    selectedDate.setHours(0, 0, 0, 0)

    // 检查开始日期限制
    if (availability.availableStartDate) {
      const startDate = new Date(availability.availableStartDate)
      startDate.setHours(0, 0, 0, 0)
      if (selectedDate.getTime() < startDate.getTime()) {
        return true
      }
    }

    // 检查结束日期限制
    if (availability.availableEndDate) {
      const endDate = new Date(availability.availableEndDate)
      endDate.setHours(23, 59, 59, 999)
      if (selectedDate.getTime() > endDate.getTime()) {
        return true
      }
    }
  }

  // 检查是否在地点的可用星期内
  const dayOfWeek = time.getDay() === 0 ? 7 : time.getDay() // 转换为1-7格式（周一到周日）
  const availableDays = availability.availableDaysList || []

  // 在错误模式下，只禁用周末
  if (availability.errorMode) {
    return dayOfWeek === 6 || dayOfWeek === 7 // 禁用周六和周日
  }

  return !availableDays.includes(dayOfWeek)
}

// 禁用结束时间（必须晚于开始时间，且遵循地点开放时间）
const disabledEndTime = (date) => {
  if (!formData.startTime) {
    return {}
  }

  if (!formData.locationId || !locationAvailability.value) {
    // 如果没有地点信息，只进行基本的时间验证
    const startTime = new Date(formData.startTime)
    const selectedDate = new Date(date)

    if (selectedDate.toDateString() === startTime.toDateString()) {
      // 同一天，禁用开始时间之前的所有时间（不包括开始时间所在的小时）
      const disabledHours = []
      for (let i = 0; i < startTime.getHours(); i++) {
        disabledHours.push(i)
      }

      const disabledMinutes = (hour) => {
        const minutes = []
        if (hour === startTime.getHours()) {
          // 禁用开始时间之前的分钟（不包括开始时间的分钟）
          for (let i = 0; i < startTime.getMinutes(); i++) {
            minutes.push(i)
          }
        }
        return minutes
      }

      return {
        disabledHours: () => disabledHours,
        disabledMinutes: disabledMinutes,
        disabledSeconds: () => []
      }
    }

    return {}
  }

  const availability = locationAvailability.value
  const selectedDate = new Date(date)
  const startTime = new Date(formData.startTime)

  // 在错误模式下，提供基本的时间限制
  if (availability.errorMode) {
    const disabledHours = []
    // 禁用非工作时间（8:00-18:00之外）
    for (let i = 0; i < 8; i++) {
      disabledHours.push(i)
    }
    for (let i = 18; i < 24; i++) {
      disabledHours.push(i)
    }

    // 如果是开始时间的同一天，还要禁用开始时间之前的时间（不包括开始时间所在的小时）
    if (selectedDate.toDateString() === startTime.toDateString()) {
      for (let i = 0; i < startTime.getHours(); i++) {
        if (!disabledHours.includes(i)) {
          disabledHours.push(i)
        }
      }
    }

    const disabledMinutes = (hour) => {
      const minutes = []
      // 如果是开始时间的同一天和同一小时，禁用开始时间之前的分钟（不包括开始时间的分钟）
      if (selectedDate.toDateString() === startTime.toDateString() && hour === startTime.getHours()) {
        for (let i = 0; i < startTime.getMinutes(); i++) {
          minutes.push(i)
        }
      }
      return minutes
    }

    return {
      disabledHours: () => disabledHours,
      disabledMinutes: disabledMinutes,
      disabledSeconds: () => []
    }
  }

  // 获取地点开放时间，处理 HH:MM:SS 格式
  const openTime = availability.openTime ? availability.openTime.split(':') : ['08', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['18', '00']

  const openHour = parseInt(openTime[0])
  const openMinute = parseInt(openTime[1])
  const closeHour = parseInt(closeTime[0])
  const closeMinute = parseInt(closeTime[1])

  // 禁用的小时数组
  const disabledHours = []
  for (let i = 0; i < 24; i++) {
    if (i < openHour || i >= closeHour) {
      disabledHours.push(i)
    }
  }

  // 如果是开始时间的同一天，还要禁用开始时间之前的时间（不包括开始时间所在的小时）
  if (selectedDate.toDateString() === startTime.toDateString()) {
    for (let i = 0; i < startTime.getHours(); i++) {
      if (!disabledHours.includes(i)) {
        disabledHours.push(i)
      }
    }
  }

  // 禁用的分钟数组（针对开放和关闭的边界小时，以及开始时间限制）
  const disabledMinutes = (hour) => {
    const minutes = []

    // 地点开放时间限制
    if (hour === openHour) {
      for (let i = 0; i < openMinute; i++) {
        minutes.push(i)
      }
    }
    if (hour === closeHour) {
      for (let i = closeMinute; i < 60; i++) {
        minutes.push(i)
      }
    }

    // 开始时间限制（不包括开始时间的分钟）
    if (selectedDate.toDateString() === startTime.toDateString() && hour === startTime.getHours()) {
      for (let i = 0; i < startTime.getMinutes(); i++) {
        if (!minutes.includes(i)) {
          minutes.push(i)
        }
      }
    }

    // 检查与已预订时间段的冲突
    const bookedSlots = availability.bookedTimeSlots || []
    for (const slot of bookedSlots) {
      if (slot.isCurrentMeeting) continue // 跳过当前编辑的会议

      const slotStart = new Date(slot.startTime)
      const slotEnd = new Date(slot.endTime)

      // 处理冲突检测（包括跨天会议）
      const selectedDateStr = selectedDate.toDateString()
      const slotStartDateStr = slotStart.toDateString()
      const slotEndDateStr = slotEnd.toDateString()

      let shouldCheckConflict = false
      let effectiveStartHour = 0
      let effectiveStartMinute = 0
      let effectiveEndHour = 23
      let effectiveEndMinute = 59

      if (selectedDateStr === slotStartDateStr && selectedDateStr === slotEndDateStr) {
        // 会议在同一天开始和结束
        shouldCheckConflict = true
        effectiveStartHour = slotStart.getHours()
        effectiveStartMinute = slotStart.getMinutes()
        effectiveEndHour = slotEnd.getHours()
        effectiveEndMinute = slotEnd.getMinutes()
      } else if (selectedDateStr === slotStartDateStr) {
        // 选择的日期是会议开始日期（跨天会议的第一天）
        shouldCheckConflict = true
        effectiveStartHour = slotStart.getHours()
        effectiveStartMinute = slotStart.getMinutes()
        effectiveEndHour = 23
        effectiveEndMinute = 59
      } else if (selectedDateStr === slotEndDateStr) {
        // 选择的日期是会议结束日期（跨天会议的最后一天）
        shouldCheckConflict = true
        effectiveStartHour = 0
        effectiveStartMinute = 0
        effectiveEndHour = slotEnd.getHours()
        effectiveEndMinute = slotEnd.getMinutes()
      } else if (slotStart < selectedDate && selectedDate < slotEnd) {
        // 选择的日期在跨天会议的中间（整天被占用）
        shouldCheckConflict = true
        effectiveStartHour = 0
        effectiveStartMinute = 0
        effectiveEndHour = 23
        effectiveEndMinute = 59
      }

      if (shouldCheckConflict) {
        // 如果当前小时在冲突时间段内
        if (hour >= effectiveStartHour && hour <= effectiveEndHour) {
          if (hour === effectiveStartHour && hour === effectiveEndHour) {
            // 开始和结束在同一小时
            for (let i = effectiveStartMinute; i < effectiveEndMinute; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else if (hour === effectiveStartHour) {
            // 开始小时
            for (let i = effectiveStartMinute; i < 60; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else if (hour === effectiveEndHour) {
            // 结束小时
            for (let i = 0; i < effectiveEndMinute; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else {
            // 中间小时，全部禁用
            for (let i = 0; i < 60; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          }
        }
      }
    }

    return minutes
  }

  return {
    disabledHours: () => disabledHours,
    disabledMinutes: disabledMinutes,
    disabledSeconds: () => []
  }
}

// 禁用开始时间（基于地点开放时间和冲突检测）
const disabledStartTime = (date) => {
  if (!formData.locationId || !locationAvailability.value) {
    return {}
  }

  const availability = locationAvailability.value
  const selectedDate = new Date(date)
  const today = new Date()

  // 在错误模式下，只提供基本的时间限制
  if (availability.errorMode) {
    const disabledHours = []
    // 禁用非工作时间（8:00-18:00之外）
    for (let i = 0; i < 8; i++) {
      disabledHours.push(i)
    }
    for (let i = 18; i < 24; i++) {
      disabledHours.push(i)
    }

    const disabledMinutes = (hour) => {
      const minutes = []
      // 如果是今天，禁用过去的时间
      if (selectedDate.toDateString() === today.toDateString()) {
        const currentHour = today.getHours()
        const currentMinute = today.getMinutes()

        if (hour === currentHour) {
          for (let i = 0; i <= currentMinute; i++) {
            minutes.push(i)
          }
        }
      }
      return minutes
    }

    return {
      disabledHours: () => disabledHours,
      disabledMinutes: disabledMinutes,
      disabledSeconds: () => []
    }
  }

  // 获取地点开放时间，处理 HH:MM:SS 格式
  const openTime = availability.openTime ? availability.openTime.split(':') : ['08', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['18', '00']

  const openHour = parseInt(openTime[0])
  const openMinute = parseInt(openTime[1])
  const closeHour = parseInt(closeTime[0])
  const closeMinute = parseInt(closeTime[1])

  // 禁用的小时数组
  const disabledHours = []
  for (let i = 0; i < 24; i++) {
    if (i < openHour || i >= closeHour) {
      disabledHours.push(i)
    }
  }

  // 禁用的分钟数组（针对开放和关闭的边界小时）
  const disabledMinutes = (hour) => {
    const minutes = []
    if (hour === openHour) {
      for (let i = 0; i < openMinute; i++) {
        minutes.push(i)
      }
    }
    if (hour === closeHour) {
      for (let i = closeMinute; i < 60; i++) {
        minutes.push(i)
      }
    }

    // 如果是今天，还需要禁用过去的时间
    if (selectedDate.toDateString() === today.toDateString()) {
      const currentHour = today.getHours()
      const currentMinute = today.getMinutes()

      if (hour === currentHour) {
        for (let i = 0; i <= currentMinute; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      }
    }

    // 检查与已预订时间段的冲突
    const bookedSlots = availability.bookedTimeSlots || []
    for (const slot of bookedSlots) {
      if (slot.isCurrentMeeting) continue // 跳过当前编辑的会议

      const slotStart = new Date(slot.startTime)
      const slotEnd = new Date(slot.endTime)

      // 处理冲突检测（包括跨天会议）
      const selectedDateStr = selectedDate.toDateString()
      const slotStartDateStr = slotStart.toDateString()
      const slotEndDateStr = slotEnd.toDateString()

      let shouldCheckConflict = false
      let effectiveStartHour = 0
      let effectiveStartMinute = 0
      let effectiveEndHour = 23
      let effectiveEndMinute = 59

      if (selectedDateStr === slotStartDateStr && selectedDateStr === slotEndDateStr) {
        // 会议在同一天开始和结束
        shouldCheckConflict = true
        effectiveStartHour = slotStart.getHours()
        effectiveStartMinute = slotStart.getMinutes()
        effectiveEndHour = slotEnd.getHours()
        effectiveEndMinute = slotEnd.getMinutes()
      } else if (selectedDateStr === slotStartDateStr) {
        // 选择的日期是会议开始日期（跨天会议的第一天）
        shouldCheckConflict = true
        effectiveStartHour = slotStart.getHours()
        effectiveStartMinute = slotStart.getMinutes()
        effectiveEndHour = 23
        effectiveEndMinute = 59
      } else if (selectedDateStr === slotEndDateStr) {
        // 选择的日期是会议结束日期（跨天会议的最后一天）
        shouldCheckConflict = true
        effectiveStartHour = 0
        effectiveStartMinute = 0
        effectiveEndHour = slotEnd.getHours()
        effectiveEndMinute = slotEnd.getMinutes()
      } else if (slotStart < selectedDate && selectedDate < slotEnd) {
        // 选择的日期在跨天会议的中间（整天被占用）
        shouldCheckConflict = true
        effectiveStartHour = 0
        effectiveStartMinute = 0
        effectiveEndHour = 23
        effectiveEndMinute = 59
      }

      if (shouldCheckConflict) {
        // 如果当前小时在冲突时间段内
        if (hour >= effectiveStartHour && hour <= effectiveEndHour) {
          if (hour === effectiveStartHour && hour === effectiveEndHour) {
            // 开始和结束在同一小时
            for (let i = effectiveStartMinute; i < effectiveEndMinute; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else if (hour === effectiveStartHour) {
            // 开始小时
            for (let i = effectiveStartMinute; i < 60; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else if (hour === effectiveEndHour) {
            // 结束小时
            for (let i = 0; i < effectiveEndMinute; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          } else {
            // 中间小时，全部禁用
            for (let i = 0; i < 60; i++) {
              if (!minutes.includes(i)) {
                minutes.push(i)
              }
            }
          }
        }
      }
    }

    return minutes
  }

  return {
    disabledHours: () => disabledHours,
    disabledMinutes: disabledMinutes,
    disabledSeconds: () => []
  }
}

// 新的时间禁用函数（使用 Element Plus 的分离式属性）
const getDisabledStartHours = () => {
  if (!formData.locationId || !locationAvailability.value) {
    return []
  }

  const availability = locationAvailability.value

  // 在错误模式下，只提供基本的时间限制
  if (availability.errorMode) {
    const disabledHours = []
    // 禁用非工作时间（8:00-18:00之外）
    for (let i = 0; i < 8; i++) {
      disabledHours.push(i)
    }
    for (let i = 18; i < 24; i++) {
      disabledHours.push(i)
    }
    return disabledHours
  }

  // 获取地点开放时间，处理 HH:MM:SS 格式
  const openTime = availability.openTime ? availability.openTime.split(':') : ['09', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['16', '26']

  const openHour = parseInt(openTime[0])
  const closeHour = parseInt(closeTime[0])

  // 禁用的小时数组
  const disabledHours = []
  for (let i = 0; i < 24; i++) {
    if (i < openHour || i > closeHour) {
      disabledHours.push(i)
    }
  }

  // 检查已预订时间段，如果某个小时完全被占用，则禁用该小时
  const selectedDate = formData.startTime ? new Date(formData.startTime) : new Date()
  for (let hour = 0; hour < 24; hour++) {
    if (disabledHours.includes(hour)) continue // 已经被地点开放时间禁用

    // 检查这个小时是否有任何可用分钟
    const availableMinutes = getAvailableMinutesForHour(hour, selectedDate, 'start')
    if (availableMinutes.length === 0) {
      disabledHours.push(hour)
    }
  }

  return disabledHours
}

const getDisabledStartMinutes = (hour) => {
  if (!formData.locationId || !locationAvailability.value) {
    return []
  }

  const availability = locationAvailability.value
  const today = new Date()
  const selectedDate = formData.startTime ? new Date(formData.startTime) : new Date()

  // 在错误模式下，只处理当天过去时间
  if (availability.errorMode) {
    const minutes = []
    const currentHour = today.getHours()
    const currentMinute = today.getMinutes()

    if (hour === currentHour && selectedDate.toDateString() === today.toDateString()) {
      for (let i = 0; i <= currentMinute; i++) {
        minutes.push(i)
      }
    }
    return minutes
  }

  // 获取地点开放时间
  const openTime = availability.openTime ? availability.openTime.split(':') : ['09', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['16', '26']

  const openHour = parseInt(openTime[0])
  const openMinute = parseInt(openTime[1])
  const closeHour = parseInt(closeTime[0])
  const closeMinute = parseInt(closeTime[1])

  const minutes = []

  // 开放时间边界处理
  if (hour === openHour) {
    for (let i = 0; i < openMinute; i++) {
      minutes.push(i)
    }
  }
  if (hour === closeHour) {
    for (let i = closeMinute; i < 60; i++) {
      minutes.push(i)
    }
  }

  // 如果是今天，还需要禁用过去的时间
  const currentHour = today.getHours()
  const currentMinute = today.getMinutes()

  if (hour === currentHour && selectedDate.toDateString() === today.toDateString()) {
    for (let i = 0; i <= currentMinute; i++) {
      if (!minutes.includes(i)) {
        minutes.push(i)
      }
    }
  }

  // 检查已预订时间段的冲突
  const bookedSlots = availability.bookedTimeSlots || []
  for (const slot of bookedSlots) {
    if (slot.isCurrentMeeting) continue // 跳过当前编辑的会议

    const slotStart = new Date(slot.startTime)
    const slotEnd = new Date(slot.endTime)

    // 只处理同一天的时间段
    if (slotStart.toDateString() === selectedDate.toDateString()) {
      const slotStartHour = slotStart.getHours()
      const slotStartMinute = slotStart.getMinutes()
      const slotEndHour = slotEnd.getHours()
      const slotEndMinute = slotEnd.getMinutes()

      // 如果当前小时在已预订时间段内，禁用相应的分钟
      if (hour === slotStartHour && hour === slotEndHour) {
        // 开始和结束在同一小时
        for (let i = slotStartMinute; i < slotEndMinute; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour === slotStartHour) {
        // 当前小时是开始小时
        for (let i = slotStartMinute; i < 60; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour === slotEndHour) {
        // 当前小时是结束小时
        for (let i = 0; i < slotEndMinute; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour > slotStartHour && hour < slotEndHour) {
        // 当前小时完全在已预订时间段内
        for (let i = 0; i < 60; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      }
    }
  }

  return minutes
}

const getDisabledStartSeconds = () => {
  return []
}

const getDisabledEndHours = () => {
  if (!formData.startTime) {
    return []
  }

  if (!formData.locationId || !locationAvailability.value) {
    // 如果没有地点信息，只进行基本的时间验证
    const startTime = new Date(formData.startTime)
    const disabledHours = []
    // 只禁用开始时间之前的小时，不禁用开始时间所在的小时
    for (let i = 0; i < startTime.getHours(); i++) {
      disabledHours.push(i)
    }
    return disabledHours
  }

  const availability = locationAvailability.value
  const startTime = new Date(formData.startTime)

  // 在错误模式下，提供基本的时间限制
  if (availability.errorMode) {
    const disabledHours = []
    // 禁用非工作时间（8:00-18:00之外）
    for (let i = 0; i < 8; i++) {
      disabledHours.push(i)
    }
    for (let i = 18; i < 24; i++) {
      disabledHours.push(i)
    }

    // 还要禁用开始时间之前的时间（不包括开始时间所在的小时）
    for (let i = 0; i < startTime.getHours(); i++) {
      if (!disabledHours.includes(i)) {
        disabledHours.push(i)
      }
    }

    return disabledHours
  }

  // 获取地点开放时间
  const openTime = availability.openTime ? availability.openTime.split(':') : ['09', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['16', '26']

  const openHour = parseInt(openTime[0])
  const closeHour = parseInt(closeTime[0])

  // 禁用的小时数组
  const disabledHours = []
  for (let i = 0; i < 24; i++) {
    if (i < openHour || i > closeHour) {
      disabledHours.push(i)
    }
  }

  // 还要禁用开始时间之前的时间（不包括开始时间所在的小时）
  for (let i = 0; i < startTime.getHours(); i++) {
    if (!disabledHours.includes(i)) {
      disabledHours.push(i)
    }
  }

  // 检查已预订时间段，如果某个小时完全被占用，则禁用该小时
  const selectedDate = formData.endTime ? new Date(formData.endTime) : new Date(formData.startTime)
  for (let hour = 0; hour < 24; hour++) {
    if (disabledHours.includes(hour)) continue // 已经被其他条件禁用

    // 检查这个小时是否有任何可用分钟
    const availableMinutes = getAvailableMinutesForHour(hour, selectedDate, 'end')
    if (availableMinutes.length === 0) {
      disabledHours.push(hour)
    }
  }

  return disabledHours
}

const getDisabledEndMinutes = (hour) => {
  if (!formData.startTime) {
    return []
  }

  if (!formData.locationId || !locationAvailability.value) {
    // 如果没有地点信息，只进行基本的时间验证
    const startTime = new Date(formData.startTime)
    const selectedDate = formData.endTime ? new Date(formData.endTime) : new Date(formData.startTime)
    const minutes = []

    // 如果是同一天且同一小时，禁用开始时间之前的分钟（不包括开始时间的分钟）
    if (selectedDate.toDateString() === startTime.toDateString() && hour === startTime.getHours()) {
      for (let i = 0; i < startTime.getMinutes(); i++) {
        minutes.push(i)
      }
    }
    return minutes
  }

  const availability = locationAvailability.value
  const startTime = new Date(formData.startTime)
  const selectedDate = formData.endTime ? new Date(formData.endTime) : new Date(formData.startTime)

  // 在错误模式下，提供基本的时间限制
  if (availability.errorMode) {
    const minutes = []
    // 如果是开始时间的同一天且同一小时，禁用开始时间之前的分钟（不包括开始时间的分钟）
    if (selectedDate.toDateString() === startTime.toDateString() && hour === startTime.getHours()) {
      for (let i = 0; i < startTime.getMinutes(); i++) {
        minutes.push(i)
      }
    }
    return minutes
  }

  // 获取地点开放时间
  const openTime = availability.openTime ? availability.openTime.split(':') : ['09', '00']
  const closeTime = availability.closeTime ? availability.closeTime.split(':') : ['16', '26']

  const openHour = parseInt(openTime[0])
  const openMinute = parseInt(openTime[1])
  const closeHour = parseInt(closeTime[0])
  const closeMinute = parseInt(closeTime[1])

  const minutes = []

  // 地点开放时间限制
  if (hour === openHour) {
    for (let i = 0; i < openMinute; i++) {
      minutes.push(i)
    }
  }
  if (hour === closeHour) {
    for (let i = closeMinute; i < 60; i++) {
      minutes.push(i)
    }
  }

  // 开始时间限制（不包括开始时间的分钟）
  if (selectedDate.toDateString() === startTime.toDateString() && hour === startTime.getHours()) {
    for (let i = 0; i < startTime.getMinutes(); i++) {
      if (!minutes.includes(i)) {
        minutes.push(i)
      }
    }
  }

  // 检查已预订时间段的冲突
  const bookedSlots = availability.bookedTimeSlots || []
  for (const slot of bookedSlots) {
    if (slot.isCurrentMeeting) continue // 跳过当前编辑的会议

    const slotStart = new Date(slot.startTime)
    const slotEnd = new Date(slot.endTime)

    // 只处理同一天的时间段
    if (slotStart.toDateString() === selectedDate.toDateString()) {
      const slotStartHour = slotStart.getHours()
      const slotStartMinute = slotStart.getMinutes()
      const slotEndHour = slotEnd.getHours()
      const slotEndMinute = slotEnd.getMinutes()

      // 如果当前小时在已预订时间段内，禁用相应的分钟
      if (hour === slotStartHour && hour === slotEndHour) {
        // 开始和结束在同一小时
        for (let i = slotStartMinute; i < slotEndMinute; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour === slotStartHour) {
        // 当前小时是开始小时
        for (let i = slotStartMinute; i < 60; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour === slotEndHour) {
        // 当前小时是结束小时
        for (let i = 0; i < slotEndMinute; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      } else if (hour > slotStartHour && hour < slotEndHour) {
        // 当前小时完全在已预订时间段内
        for (let i = 0; i < 60; i++) {
          if (!minutes.includes(i)) {
            minutes.push(i)
          }
        }
      }
    }
  }

  return minutes
}

const getDisabledEndSeconds = () => {
  return []
}

// 辅助函数：获取指定小时的可用分钟数组
const getAvailableMinutesForHour = (hour, selectedDate, timeType) => {
  const availableMinutes = []

  // 生成该小时的所有分钟（0-59）
  for (let minute = 0; minute < 60; minute++) {
    availableMinutes.push(minute)
  }

  // 获取该小时的禁用分钟
  let disabledMinutes = []
  if (timeType === 'start') {
    disabledMinutes = getDisabledStartMinutes(hour)
  } else if (timeType === 'end') {
    disabledMinutes = getDisabledEndMinutes(hour)
  }

  // 过滤掉禁用的分钟，返回可用分钟
  return availableMinutes.filter(minute => !disabledMinutes.includes(minute))
}

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入会议主题', trigger: 'blur' },
    { max: 200, message: '会议主题长度不能超过200个字符', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择会议开始时间', trigger: 'change' }
  ],
  endTime: [
    { validator: validateEndTime, trigger: 'change' }
  ],
  location: [
    { max: 200, message: '会议地点长度不能超过200个字符', trigger: 'blur' }
  ],
  locationId: [
    { required: true, message: '请选择会议地点', trigger: 'change' }
  ],
  responsibleId: [
    { required: true, message: '请选择会议负责人', trigger: 'change' }
  ],
  content: [
    { max: 5000, message: '会议内容长度不能超过5000个字符', trigger: 'blur' }
  ],
  participants: [
    {
      type: 'array',
      required: true,
      min: 1,
      message: '至少需要选择一个参与者',
      trigger: ['change', 'blur']
    },
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要选择一个参与者'))
        } else {
          callback()
        }
      },
      trigger: ['change', 'blur']
    }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    title: '',
    content: '',
    startTime: '',
    endTime: '',
    location: '',
    locationId: null,
    responsibleId: null,
    status: 'NOT_STARTED',
    participants: []
  })

  // 重置部门选择相关状态
  selectedDepartments.value = []
  departmentOptions.value = []
  departmentSearchQuery.value = ''
  departmentCurrentPage.value = 1
  departmentHasMore.value = true

  // 重置员工选择相关状态
  selectedEmployees.value = []
  employeeOptions.value = []
  employeeSearchQuery.value = ''
  employeeCurrentPage.value = 1
  employeeHasMore.value = true

  // 重置负责人选择相关状态
  responsibleOptions.value = []
  responsibleSearchQuery.value = ''
  responsibleCurrentPage.value = 1
  responsibleHasMore.value = true

  // 清理地点可用性相关状态
  locationAvailability.value = null
  availabilityCache.value.clear()

  // 重置表单验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 监听会议数据变化
watch(() => props.meetingData, async (newData) => {
  if (newData) {
    // 编辑模式，先确保地点列表已加载
    if (locationOptions.value.length === 0) {
      await loadLocations()
    }

    // 填充表单数据
    Object.assign(formData, {
      id: newData.id,
      title: newData.title || '',
      content: newData.content || '',
      startTime: newData.startTime || '',
      endTime: newData.endTime || '',
      location: newData.location || '',
      locationId: newData.locationId || null,
      responsibleId: newData.responsibleId || null,
      status: newData.status || 'NOT_STARTED',
      participants: newData.participants || []
    })

    // 重置参与者选择状态
    selectedDepartments.value = []
    selectedEmployees.value = []
    departmentOptions.value = []
    employeeOptions.value = []

    // 填充已选择的参与者到对应的选择框
    if (newData.participants && newData.participants.length > 0) {
      const departments = newData.participants.filter(p => p.participantType === 'DEPARTMENT')
      const employees = newData.participants.filter(p => p.participantType === 'EMPLOYEE')

      // 设置已选择的参与者ID
      selectedDepartments.value = departments.map(d => d.participantId)
      selectedEmployees.value = employees.map(e => e.participantId)

      // 将已选择的部门添加到选项中，确保下拉框能正确显示
      if (departments.length > 0) {
        departmentOptions.value = departments.map(d => ({
          departmentId: d.participantId,
          departmentName: d.participantName
        }))
      }

      // 将已选择的员工添加到选项中，确保下拉框能正确显示
      if (employees.length > 0) {
        employeeOptions.value = employees.map(e => ({
          employeeId: e.participantId,
          name: e.participantName,
          departmentName: '',
          positionName: '',
          isResigned: false
        }))
      }

      // 确保参与者数据同步到表单
      updateParticipants()
    }

    // 如果有负责人ID，加载负责人信息到选项中
    if (newData.responsibleId && newData.responsibleName) {
      responsibleOptions.value = [{
        employeeId: newData.responsibleId,
        name: newData.responsibleName,
        departmentName: newData.responsibleDepartmentName || '',
        positionName: newData.responsiblePositionName || '',
        isResigned: false
      }]
    }

    // 如果有地点ID和开始时间，直接加载具体的可用性信息；否则只加载地点基本信息
    if (newData.locationId && newData.startTime) {
      // 编辑模式下有完整信息，直接获取具体时间段的可用性
      await fetchLocationAvailability()
    } else if (newData.locationId) {
      // 只有地点信息，获取地点基本可用性信息
      await fetchLocationAvailabilityForLocation(newData.locationId)
    }
  } else {
    // 新增模式，重置表单数据
    resetForm()
  }
}, { immediate: true })

// 监听对话框显示状态，加载地点列表并处理表单重置
watch(() => props.visible, async (visible) => {
  if (visible) {
    // 对话框打开时加载地点列表
    if (locationOptions.value.length === 0) {
      await loadLocations()
    }

    // 如果是新增模式（没有meetingData或meetingData为null），确保表单被重置
    if (!props.meetingData) {
      resetForm()
    }
  }
  // 使用 v-if 后，表单关闭时会自动销毁，不需要手动清理
})

// 监听开始时间变化，更新可用性信息（使用防抖）
let startTimeChangeTimer = null
watch(() => formData.startTime, async (startTime) => {
  // 清除之前的定时器
  if (startTimeChangeTimer) {
    clearTimeout(startTimeChangeTimer)
  }

  // 如果没有地点或开始时间，直接返回
  if (!formData.locationId || !startTime) {
    return
  }

  // 防抖处理，500ms后执行
  startTimeChangeTimer = setTimeout(async () => {
    await fetchLocationAvailability()
  }, 500)
}, { immediate: false })

// 部门搜索相关方法
const loadDepartments = async (query = '', page = 1, append = false) => {
  departmentLoading.value = true
  try {
    const response = await getDepartmentList()
    if (response.code === 200) {
      let departments = response.data || []

      // 过滤无效数据
      departments = departments.filter(dept => dept && dept.departmentId && dept.departmentName)

      // 如果有查询条件，进行过滤
      if (query) {
        departments = departments.filter(dept =>
          dept.departmentName.toLowerCase().includes(query.toLowerCase())
        )
      }

      // 模拟分页
      const startIndex = (page - 1) * departmentPageSize.value
      const endIndex = startIndex + departmentPageSize.value
      const paginatedDepts = departments.slice(startIndex, endIndex)

      if (append) {
        departmentOptions.value = [...departmentOptions.value, ...paginatedDepts]
      } else {
        departmentOptions.value = paginatedDepts
      }

      // 更新分页状态
      departmentCurrentPage.value = page
      departmentHasMore.value = endIndex < departments.length
    } else {
      ElMessage.error(response.message || '加载部门失败')
    }
  } catch (error) {
    console.error('加载部门失败:', error)
    ElMessage.error('加载部门失败')
  } finally {
    departmentLoading.value = false
  }
}

const loadMoreDepartments = async () => {
  if (!departmentHasMore.value || departmentLoading.value) return

  const nextPage = departmentCurrentPage.value + 1
  await loadDepartments(departmentSearchQuery.value, nextPage, true)
}

const handleDepartmentSearch = async (query) => {
  departmentSearchQuery.value = query
  departmentCurrentPage.value = 1
  departmentHasMore.value = true

  if (!query) {
    departmentOptions.value = []
    return
  }

  await loadDepartments(query, 1, false)
}

const handleDepartmentSelectVisibleChange = (visible) => {
  if (visible && departmentOptions.value.length === 0) {
    loadDepartments('', 1, false)
  }

  // 当下拉框打开时，设置滚动监听器
  if (visible) {
    nextTick(() => {
      const dropdownEl = document.querySelector('.department-select-dropdown .el-scrollbar__wrap')
      if (dropdownEl) {
        dropdownEl.addEventListener('scroll', handleDepartmentScrollDirect)
      }
    })
  } else {
    // 当下拉框关闭时，移除滚动监听器
    const dropdownEl = document.querySelector('.department-select-dropdown .el-scrollbar__wrap')
    if (dropdownEl) {
      dropdownEl.removeEventListener('scroll', handleDepartmentScrollDirect)
    }
  }
}

// 处理部门下拉框滚动事件
const handleDepartmentScroll = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && departmentHasMore.value && !departmentLoading.value) {
    loadMoreDepartments()
  }
}

// 直接监听Element Plus滚动容器的滚动事件
const handleDepartmentScrollDirect = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && departmentHasMore.value && !departmentLoading.value) {
    loadMoreDepartments()
  }
}

// 员工搜索相关方法
const loadEmployees = async (query = '', page = 1, append = false) => {
  employeeLoading.value = true
  try {
    const response = await getEmployeePage({
      pageNum: page,
      pageSize: employeePageSize.value,
      name: query || undefined
    })

    if (response.code === 200) {
      let employees = []
      if (response.data && response.data.list) {
        employees = response.data.list
      } else if (Array.isArray(response.data)) {
        employees = response.data
      } else {
        employees = []
      }

      // 过滤无效数据并为每个员工添加离职状态
      employees = employees.filter(emp => emp && emp.employeeId && emp.name)
      employees.forEach(emp => {
        emp.isResigned = !!(emp.exitDate)
      })

      if (append) {
        employeeOptions.value = [...employeeOptions.value, ...employees]
      } else {
        employeeOptions.value = employees
      }

      // 更新分页状态
      employeeCurrentPage.value = page
      employeeHasMore.value = employees.length === employeePageSize.value
    } else {
      ElMessage.error(response.message || '加载员工失败')
    }
  } catch (error) {
    console.error('加载员工失败:', error)
    ElMessage.error('加载员工失败')
  } finally {
    employeeLoading.value = false
  }
}

const loadMoreEmployees = async () => {
  if (!employeeHasMore.value || employeeLoading.value) return

  const nextPage = employeeCurrentPage.value + 1
  await loadEmployees(employeeSearchQuery.value, nextPage, true)
}

const handleEmployeeSearch = async (query) => {
  employeeSearchQuery.value = query
  employeeCurrentPage.value = 1
  employeeHasMore.value = true

  if (!query) {
    employeeOptions.value = []
    return
  }

  await loadEmployees(query, 1, false)
}

const handleEmployeeSelectVisibleChange = (visible) => {
  if (visible && employeeOptions.value.length === 0) {
    loadEmployees('', 1, false)
  }

  // 当下拉框打开时，设置滚动监听器
  if (visible) {
    nextTick(() => {
      const dropdownEl = document.querySelector('.participant-employee-select-dropdown .el-scrollbar__wrap')
      if (dropdownEl) {
        dropdownEl.addEventListener('scroll', handleEmployeeScrollDirect)
      }
    })
  } else {
    // 当下拉框关闭时，移除滚动监听器
    const dropdownEl = document.querySelector('.participant-employee-select-dropdown .el-scrollbar__wrap')
    if (dropdownEl) {
      dropdownEl.removeEventListener('scroll', handleEmployeeScrollDirect)
    }
  }
}

// 处理员工下拉框滚动事件
const handleEmployeeScroll = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && employeeHasMore.value && !employeeLoading.value) {
    loadMoreEmployees()
  }
}

// 直接监听Element Plus滚动容器的滚动事件
const handleEmployeeScrollDirect = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && employeeHasMore.value && !employeeLoading.value) {
    loadMoreEmployees()
  }
}

// 处理部门选择变化
const handleDepartmentChange = (selectedDeptIds) => {
  selectedDepartments.value = selectedDeptIds
  updateParticipants()
}

// 处理员工选择变化
const handleEmployeeChange = (selectedEmpIds) => {
  selectedEmployees.value = selectedEmpIds
  updateParticipants()
}

// 更新参与者数据到表单
const updateParticipants = () => {
  // 清空现有参与者数据
  formData.participants = []

  // 添加部门参与者
  selectedDepartments.value.forEach(deptId => {
    const dept = departmentOptions.value.find(d => d.departmentId === deptId)
    if (dept) {
      formData.participants.push({
        participantType: 'DEPARTMENT',
        participantId: deptId,
        participantName: dept.departmentName
      })
    }
  })

  // 添加员工参与者
  selectedEmployees.value.forEach(empId => {
    const emp = employeeOptions.value.find(e => e.employeeId === empId)
    if (emp) {
      formData.participants.push({
        participantType: 'EMPLOYEE',
        participantId: empId,
        participantName: emp.name
      })
    }
  })

  // 触发表单验证
  nextTick(() => {
    formRef.value?.validateField('participants')
  })
}

// 移除参与者
const removeParticipant = (index) => {
  const participant = formData.participants[index]

  // 从对应的选择框中移除
  if (participant.participantType === 'DEPARTMENT') {
    selectedDepartments.value = selectedDepartments.value.filter(id => id !== participant.participantId)
  } else if (participant.participantType === 'EMPLOYEE') {
    selectedEmployees.value = selectedEmployees.value.filter(id => id !== participant.participantId)
  }

  // 从参与者列表中移除
  formData.participants.splice(index, 1)

  // 触发表单验证
  nextTick(() => {
    formRef.value?.validateField('participants')
  })
}

// 地点管理相关方法
const loadLocations = async () => {
  locationLoading.value = true
  try {
    const response = await getAllLocations()
    if (response.code === 200) {
      locationOptions.value = (response.data || []).map(location => ({
        ...location,
        fullDisplay: `${location.name}${location.description ? ` (${location.description})` : ''} - 容纳${location.capacity}人`
      }))
    } else {
      ElMessage.error(response.message || '获取地点列表失败')
    }
  } catch (error) {
    console.error('获取地点列表失败:', error)
    ElMessage.error('获取地点列表失败')
  } finally {
    locationLoading.value = false
  }
}

const handleLocationChange = async (locationId) => {
  if (locationId) {
    const selectedLocation = locationOptions.value.find(loc => loc.id === locationId)
    if (selectedLocation) {
      // 保持向后兼容，同时设置location字段
      formData.location = selectedLocation.name
    }

    // 获取地点可用性信息，如果有开始时间则获取具体信息，否则获取基本信息
    if (formData.startTime) {
      // 有开始时间，获取具体的可用性信息
      await fetchLocationAvailability()
    } else {
      // 没有开始时间，只获取地点基本可用性信息用于显示提示
      await fetchLocationAvailabilityForLocation(locationId)
    }
  } else {
    formData.location = ''
    locationAvailability.value = null
  }
}

// 获取可用时间文本描述
const getAvailableTimeText = () => {
  if (!locationAvailability.value) return ''

  const availability = locationAvailability.value
  const openTime = availability.openTime || '08:00'
  const closeTime = availability.closeTime || '18:00'

  // 获取可用星期
  const availableDays = availability.availableDaysList || []
  const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const dayText = availableDays.map(day => dayNames[day]).join('、')

  return `${dayText} ${openTime}-${closeTime}`
}

// 获取日期范围限制文本描述
const getDateRangeRestrictionText = () => {
  if (!locationAvailability.value) return ''

  const availability = locationAvailability.value
  const startDate = availability.availableStartDate
  const endDate = availability.availableEndDate

  if (!startDate && !endDate) return ''

  if (startDate && endDate) {
    return `开放日期限制: ${startDate} 至 ${endDate}`
  } else if (startDate) {
    return `开放日期限制: ${startDate} 起开放`
  } else if (endDate) {
    return `开放日期限制: 开放至 ${endDate}`
  }

  return ''
}

// 获取相关的已预订时间段（近期的）
const getRelevantBookedSlots = () => {
  if (!locationAvailability.value || !locationAvailability.value.bookedTimeSlots) {
    return []
  }

  const now = new Date()
  const fiveDaysLater = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000)

  return locationAvailability.value.bookedTimeSlots
    .filter(slot => {
      const slotStart = new Date(slot.startTime)
      return slotStart >= now && slotStart <= fiveDaysLater && !slot.isCurrentMeeting
    })
    .sort((a, b) => new Date(a.startTime) - new Date(b.startTime))
    .slice(0, 5) // 只显示最近的5个
}

// 获取详细的已预订时间段（用于详细显示）
const getDetailedBookedSlots = () => {
  if (!locationAvailability.value || !locationAvailability.value.bookedTimeSlots) {
    return []
  }

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()) // 今天00:00:00
  const thirtyDaysLater = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000) // 未来30天

  return locationAvailability.value.bookedTimeSlots
    .filter(slot => {
      const slotStart = new Date(slot.startTime)
      const slotEnd = new Date(slot.endTime)
      // 显示今天到未来30天的已预订时间段，排除当前正在编辑的会议
      // 显示所有未结束的会议（结束时间在当前时间之后）
      return slotEnd >= now && slotStart < thirtyDaysLater && !slot.isCurrentMeeting
    })
    .sort((a, b) => new Date(a.startTime) - new Date(b.startTime))
    .slice(0, 10) // 最多显示10个时间段
}

// 重试获取地点可用性
const retryFetchAvailability = async () => {
  if (!formData.locationId || !formData.startTime) {
    ElMessage.warning('请先选择地点和开始时间')
    return
  }

  await fetchLocationAvailability()
}

// 格式化时间段显示
const formatTimeSlot = (startTime, endTime) => {
  const start = new Date(startTime)
  const end = new Date(endTime)

  const formatTime = (date) => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  // 如果是同一天，只显示时间
  if (start.toDateString() === end.toDateString()) {
    return `${start.getHours().toString().padStart(2, '0')}:${start.getMinutes().toString().padStart(2, '0')} - ${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`
  } else {
    return `${formatTime(start)} - ${formatTime(end)}`
  }
}

// 格式化详细时间段显示（包含完整日期）
const formatDetailedTimeSlot = (startTime, endTime) => {
  const start = new Date(startTime)
  const end = new Date(endTime)

  const formatDateTime = (date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(/\//g, '-')
  }

  const formatTime = (date) => {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }

  // 如果是同一天，显示日期和时间段
  if (start.toDateString() === end.toDateString()) {
    const dateStr = start.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-')
    return `${dateStr} ${formatTime(start)}-${formatTime(end)} 已被预定`
  } else {
    // 跨天会议，显示完整的开始和结束时间
    return `${formatDateTime(start)} - ${formatDateTime(end)} 已被预定`
  }
}

// 格式化带负责人的详细时间段显示
const formatDetailedTimeSlotWithBooker = (startTime, endTime, bookerName) => {
  const timeSlot = formatDetailedTimeSlot(startTime, endTime)
  return `${timeSlot} （负责人：${bookerName || '未知'}）`
}

// 处理开始时间变化
const handleStartTimeChange = async (value) => {
  // 移除自动请求，只在用户明确需要时才请求
  // 这样可以避免频繁的API调用
  //console.log('开始时间已更改:', value)
}

// 获取地点可用性信息（用于地点选择后的提示）
const fetchLocationAvailabilityForLocation = async (locationId) => {
  if (!locationId) {
    locationAvailability.value = null
    return
  }

  // 检查缓存
  const cacheKey = `${locationId}_basic`
  if (availabilityCache.value.has(cacheKey)) {
    locationAvailability.value = availabilityCache.value.get(cacheKey)
    return
  }

  availabilityLoading.value = true
  try {
    // 查询未来10天的数据用于提示
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0)

    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 10)
    endDate.setHours(23, 59, 59, 999)

    const response = await getLocationAvailability(
      locationId,
      startDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      endDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      isEdit.value ? formData.id : null
    )

    if (response.code === 200) {
      locationAvailability.value = response.data
      // 缓存基本信息（不包含具体的预订时间段）
      const basicInfo = {
        ...response.data,
        bookedTimeSlots: [] // 基本信息不缓存具体预订时间段
      }
      availabilityCache.value.set(cacheKey, basicInfo)
    } else {
      ElMessage.error(response.message || '获取地点可用性失败')
      locationAvailability.value = null
    }
  } catch (error) {
    console.error('获取地点可用性失败:', error)

    // 降级处理：设置基本的地点信息
    const selectedLocation = locationOptions.value.find(loc => loc.id === locationId)
    if (selectedLocation) {
      const fallbackData = {
        locationId: locationId,
        locationName: selectedLocation.name,
        openTime: selectedLocation.openTime || '09:00:00',
        closeTime: selectedLocation.closeTime || '16:26:24',
        availableDaysList: selectedLocation.availableDays ?
          selectedLocation.availableDays.split(',').map(d => parseInt(d)) :
          [1, 2, 3, 4, 5],
        bookedTimeSlots: [],
        isAvailable: true,
        errorMode: true
      }
      locationAvailability.value = fallbackData
      // 缓存降级数据
      availabilityCache.value.set(cacheKey, fallbackData)
    } else {
      locationAvailability.value = null
    }
  } finally {
    availabilityLoading.value = false
  }
}

// 获取地点可用性信息
const fetchLocationAvailability = async () => {
  if (!formData.locationId || !formData.startTime) {
    locationAvailability.value = null
    return
  }

  // 计算查询时间范围（当天及后续5天）
  const startDate = new Date(formData.startTime)
  startDate.setHours(0, 0, 0, 0) // 设置为当天开始

  const endDate = new Date(startDate)
  endDate.setDate(endDate.getDate() + 5) // 查询未来5天的数据
  endDate.setHours(23, 59, 59, 999) // 设置为结束时间

  // 生成缓存键
  const cacheKey = `${formData.locationId}_${startDate.toDateString()}_${isEdit.value ? formData.id : 'new'}`

  // 检查缓存
  if (availabilityCache.value.has(cacheKey)) {
    locationAvailability.value = availabilityCache.value.get(cacheKey)
    return
  }

  availabilityLoading.value = true
  try {
    const response = await getLocationAvailability(
      formData.locationId,
      startDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      endDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      isEdit.value ? formData.id : null
    )

    if (response.code === 200) {
      locationAvailability.value = response.data
      // 缓存数据，5分钟过期
      availabilityCache.value.set(cacheKey, response.data)
      setTimeout(() => {
        availabilityCache.value.delete(cacheKey)
      }, 5 * 60 * 1000)
    } else {
      ElMessage.error(response.message || '获取地点可用性失败')
      locationAvailability.value = null
    }
  } catch (error) {
    console.error('获取地点可用性失败:', error)

    // 网络错误的降级处理
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      ElMessage.warning('网络连接异常，时间冲突检测功能暂时不可用')
    } else if (error.response?.status === 404) {
      ElMessage.warning('地点信息不存在，请重新选择地点')
    } else if (error.response?.status >= 500) {
      ElMessage.warning('服务器暂时不可用，时间冲突检测功能暂时不可用')
    } else {
      ElMessage.error('获取地点可用性失败，请稍后重试')
    }

    // 降级处理：设置基本的地点信息，允许用户继续操作
    const selectedLocation = locationOptions.value.find(loc => loc.id === formData.locationId)
    if (selectedLocation) {
      const fallbackData = {
        locationId: formData.locationId,
        locationName: selectedLocation.name,
        openTime: selectedLocation.openTime || '09:00:00',
        closeTime: selectedLocation.closeTime || '16:26:24',
        availableDaysList: selectedLocation.availableDays ?
          selectedLocation.availableDays.split(',').map(d => parseInt(d)) :
          [1, 2, 3, 4, 5], // 默认工作日
        bookedTimeSlots: [],
        isAvailable: true,
        errorMode: true // 标记为错误模式
      }
      locationAvailability.value = fallbackData
      // 缓存降级数据
      availabilityCache.value.set(cacheKey, fallbackData)
    } else {
      locationAvailability.value = null
    }
  } finally {
    availabilityLoading.value = false
  }
}

// 负责人搜索相关方法
// 加载负责人数据（支持分页和搜索）
const loadResponsibleEmployees = async (query = '', page = 1, append = false) => {
  responsibleLoading.value = true
  try {
    // 使用员工API来获取负责人数据
    const res = await getEmployeePage({
      pageNum: page,
      pageSize: responsiblePageSize.value,
      name: query || undefined
    })

    if (res.code === 200) {
      let employees = []
      if (res.data && res.data.list) {
        employees = res.data.list
      } else if (Array.isArray(res.data)) {
        employees = res.data
      } else {
        employees = []
      }

      // 为每个员工添加离职状态
      employees.forEach(emp => {
        emp.isResigned = !!(emp.exitDate)
      })

      if (append) {
        responsibleOptions.value = [...responsibleOptions.value, ...employees]
      } else {
        responsibleOptions.value = employees
      }

      // 更新分页状态
      responsibleCurrentPage.value = page
      responsibleHasMore.value = employees.length === responsiblePageSize.value
    } else {
      ElMessage.error(res.msg || '加载负责人失败')
    }
  } catch (error) {
    ElMessage.error('加载负责人失败: ' + (error.message || '未知错误'))
  } finally {
    responsibleLoading.value = false
  }
}

// 加载更多负责人
const loadMoreResponsibleEmployees = async () => {
  if (!responsibleHasMore.value || responsibleLoading.value) return

  const nextPage = responsibleCurrentPage.value + 1
  await loadResponsibleEmployees(responsibleSearchQuery.value, nextPage, true)
}

// 远程搜索负责人
const handleResponsibleSearch = async (query) => {
  // 重置搜索状态
  responsibleSearchQuery.value = query
  responsibleCurrentPage.value = 1
  responsibleHasMore.value = true

  if (!query) {
    // 如果没有查询内容，清空选项
    responsibleOptions.value = []
    return
  }

  // 加载第一页数据
  await loadResponsibleEmployees(query, 1, false)
}

// 处理负责人选择框可见性变化
const handleResponsibleSelectVisibleChange = (visible) => {
  if (visible && responsibleOptions.value.length === 0) {
    // 当下拉框打开且没有数据时，加载负责人列表
    loadResponsibleEmployees('', 1, false)
  }

  // 当下拉框打开时，设置滚动监听器
  if (visible) {
    nextTick(() => {
      const dropdownEl = document.querySelector('.employee-select-dropdown .el-scrollbar__wrap')
      if (dropdownEl) {
        dropdownEl.addEventListener('scroll', handleResponsibleScrollDirect)
      }
    })
  } else {
    // 当下拉框关闭时，移除滚动监听器
    const dropdownEl = document.querySelector('.employee-select-dropdown .el-scrollbar__wrap')
    if (dropdownEl) {
      dropdownEl.removeEventListener('scroll', handleResponsibleScrollDirect)
    }
  }
}

// 处理负责人下拉框滚动事件
const handleResponsibleScroll = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && responsibleHasMore.value && !responsibleLoading.value) {
    loadMoreResponsibleEmployees()
  }
}

// 直接监听Element Plus滚动容器的滚动事件
const handleResponsibleScrollDirect = (event) => {
  const { target } = event
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到底部附近时（距离底部10px以内），加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && responsibleHasMore.value && !responsibleLoading.value) {
    loadMoreResponsibleEmployees()
  }
}

const handleResponsibleChange = (responsibleId) => {
  // 可以在这里添加额外的逻辑，比如验证负责人权限等
  //console.log('选择的负责人ID:', responsibleId)
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const isValid = await formRef.value.validate().catch(() => false)

    if (!isValid) {
      // 验证失败，显示具体的验证错误信息
      await showValidationErrors()
      return
    }

    submitLoading.value = true

    const submitData = { ...formData }

    // 移除状态字段，因为状态现在由系统自动管理
    delete submitData.status

    let response
    if (isEdit.value) {
      response = await updateMeeting(submitData.id, submitData)
    } else {
      response = await createMeeting(submitData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '会议更新成功' : '会议创建成功')
      emit('success')
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败，请检查网络连接或联系管理员')
  } finally {
    submitLoading.value = false
  }
}

// 显示验证错误信息
const showValidationErrors = async () => {
  const formEl = formRef.value
  if (!formEl) return

  try {
    // 尝试获取Element Plus的验证错误
    await formEl.validate()
  } catch (validationError) {
    // 处理Element Plus返回的验证错误
    if (validationError && typeof validationError === 'object') {
      const errorFields = Object.keys(validationError)
      if (errorFields.length > 0) {
        const firstField = errorFields[0]
        const firstError = validationError[firstField]
        if (firstError && firstError.length > 0) {
          ElMessage.warning(firstError[0].message)

          // 如果有多个字段错误，显示总数
          if (errorFields.length > 1) {
            setTimeout(() => {
              ElMessage.info(`共有 ${errorFields.length} 个字段需要完善`)
            }, 1000)
          }
          return
        }
      }
    }
  }

  // 备用验证逻辑（如果Element Plus验证失败）
  const errors = []

  // 检查必填字段
  if (!formData.title?.trim()) {
    errors.push('请输入会议主题')
  }
  if (!formData.startTime) {
    errors.push('请选择会议开始时间')
  }
  if (!formData.locationId) {
    errors.push('请选择会议地点')
  }
  if (!formData.responsibleId) {
    errors.push('请选择会议负责人')
  }
  if (!formData.participants || formData.participants.length === 0) {
    errors.push('至少需要选择一个参与者')
  }

  // 检查结束时间验证
  if (formData.endTime && formData.startTime) {
    const startTime = new Date(formData.startTime)
    const endTime = new Date(formData.endTime)
    if (endTime <= startTime) {
      errors.push('结束时间必须晚于开始时间')
    }
  }

  // 显示第一个错误信息
  if (errors.length > 0) {
    ElMessage.warning(errors[0])

    // 如果有多个错误，显示总数
    if (errors.length > 1) {
      setTimeout(() => {
        ElMessage.info(`共有 ${errors.length} 个字段需要完善`)
      }, 1000)
    }
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 使用 v-if 后，表单会自动销毁，不需要手动重置
}

// 刷新地点列表方法
const refreshLocationList = async () => {
  await loadLocations()
}

// 暴露方法给父组件
defineExpose({
  refreshLocationList
})

</script>

<style scoped>
.participants-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  background-color: #fafafa;
}

.participant-group {
  margin-bottom: 15px;
}

.group-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

/* 参与者选择框样式优化 */
.participant-select {
  width: 100%;
}

/* 优化多选标签的显示 */
:deep(.participant-select .el-select__tags) {
  max-width: 100%;
  overflow: hidden;
}

:deep(.participant-select .el-tag) {
  max-width: 120px;
  margin: 2px 4px 2px 0;
}

:deep(.participant-select .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 折叠标签样式 */
:deep(.participant-select .el-tag.el-tag--info) {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  cursor: pointer;
  transition: all 0.3s ease;
}

:deep(.participant-select .el-tag.el-tag--info:hover) {
  background-color: #e9e9eb;
  border-color: #d3d4d6;
  color: #606266;
}

/* 折叠标签tooltip样式优化 */
:deep(.el-tooltip__popper) {
  max-width: 300px;
}

:deep(.el-tooltip__popper .el-tooltip__content) {
  line-height: 1.5;
  word-break: break-all;
}

.participant-list {
  min-height: 60px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-top: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.list-header {
  font-weight: 500;
  color: #606266;
  margin-bottom: 12px;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

/* 参与者标签样式优化 */
.participant-list :deep(.el-tag) {
  margin: 6px 8px 6px 0;
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 4px;
  max-width: 200px;
}

.participant-list :deep(.el-tag .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  text-align: right;
}

/* 参与者选择下拉框样式 */
:deep(.department-select-dropdown) {
  max-height: 300px;
}

:deep(.participant-options-container) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.participant-employee-select-dropdown) {
  max-height: 300px;
}

:deep(.employee-select-dropdown) {
  max-height: 300px;
}

:deep(.employee-options-container) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.load-more-option) {
  padding: 8px 20px;
  text-align: center;
  color: #409eff;
  cursor: pointer;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 12px;
}

:deep(.load-more-option:hover) {
  background-color: #ecf5ff;
}

:deep(.no-more-data) {
  padding: 8px 20px;
  text-align: center;
  color: #909399;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 12px;
}

:deep(.resigned-employee) {
  background-color: #f5f7fa !important;
  color: #909399 !important;
}

:deep(.resigned-employee:hover) {
  background-color: #ecf5ff !important;
}

.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.help-text {
  color: #909399;
  font-size: 12px;
}

:deep(.el-form-item__help) {
  margin-top: 5px;
}

/* 离职员工样式 */
:deep(.resigned-employee) {
  background-color: #f5f7fa !important;
  color: #909399 !important;
}

:deep(.resigned-employee:hover) {
  background-color: #ecf5ff !important;
}

/* 地点可用性提示样式 */
.location-availability-info {
  margin-top: 5px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
}

.location-availability-info .el-text {
  display: flex;
  align-items: center;
  gap: 3px;
}

.location-availability-loading {
  margin-top: 5px;
}

.location-availability-loading .el-text {
  display: flex;
  align-items: center;
  gap: 3px;
}

/* 已预订时间段详情样式 */
.booked-slots-detail {
  margin-top: 8px;
}

.booked-slot-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.slot-info {
  font-size: 12px;
  color: #606266;
}

.booker-name {
  color: #909399;
}

.current-meeting-tag {
  color: #67c23a;
  font-weight: 500;
}

/* 错误模式警告样式 */
.location-availability-info .el-alert {
  margin-bottom: 8px;
}

/* 统一的信息展示区域样式 */
.time-selection-hint,
.time-selection-guide,
.time-selection-warning {
  margin-top: 8px;
}

.time-selection-hint .el-text,
.time-selection-guide .el-text,
.time-selection-warning .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 统一的信息展示容器样式 - 合并为一个通用样式 */
.info-display-container {
  margin-top: 10px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  min-width: 500px;
  max-width: 800px;
  font-size: 13px;
  line-height: 1.6;
  color: #475569;
}

.info-display-container .el-text {
  display: block;
  word-wrap: break-word;
  white-space: normal;
  margin-bottom: 8px;
}

.info-display-container .el-text:last-child {
  margin-bottom: 0;
}

/* 日期范围限制提示样式 */
.date-range-restriction {
  margin-top: 6px;
  padding: 8px 12px;
  background-color: rgba(250, 204, 21, 0.1);
  border-radius: 6px;
  border-left: 3px solid #facc15;
}

.date-range-restriction .el-text {
  margin-bottom: 0;
  font-weight: 500;
}

.unavailable-times {
  margin-top: 8px;
}

.unavailable-time-list {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.unavailable-time-tag {
  margin-bottom: 3px;
}

.slot-reason {
  margin-left: 5px;
  font-size: 11px;
  opacity: 0.8;
}

.time-selection-warning {
  padding: 6px;
  background-color: #fdf6ec;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

/* 信息展示容器内的列表样式 */
.info-display-container .booked-time-list {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-display-container .booked-time-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  border-left: 3px solid #64748b;
  min-height: 32px;
  width: 100%;
  transition: all 0.2s ease;
}

.info-display-container .booked-time-item:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateX(2px);
}

.info-display-container .time-tag {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  font-weight: 500;
  color: #334155;
  width: 100%;
  text-align: left;
}



/* 禁用地点样式 */
.disabled-location {
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
}

.disabled-location:hover {
  background-color: #f5f7fa !important;
}

/* ========== 统一文本溢出处理样式 ========== */

/* 基础文本溢出样式 */
.text-overflow-base {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 输入框文本溢出处理 */
.text-overflow-input :deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文本域溢出处理（只在只读状态下应用） */
.text-overflow-textarea :deep(.el-textarea__inner[readonly]) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  resize: none;
}

/* 选择器文本溢出处理 */
.text-overflow-select :deep(.el-select__selected-item) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.text-overflow-select :deep(.el-select__input) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选择器选项文本溢出处理 */
:deep(.el-select-dropdown__item) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  padding-right: 20px; /* 为省略号留出空间 */
}

/* 行内文本溢出处理 */
.text-overflow-span {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  vertical-align: top;
}

/* 表单标签文本溢出处理 */
:deep(.el-form-item__label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px; /* 限制标签宽度 */
}

/* 响应式文本溢出处理 */
@media (max-width: 768px) {
  .text-overflow-input :deep(.el-input__inner),
  .text-overflow-select :deep(.el-select__selected-item),
  .text-overflow-span {
    max-width: 200px;
  }

  :deep(.el-form-item__label) {
    max-width: 80px;
  }
}

@media (max-width: 480px) {
  .text-overflow-input :deep(.el-input__inner),
  .text-overflow-select :deep(.el-select__selected-item),
  .text-overflow-span {
    max-width: 150px;
  }

  :deep(.el-form-item__label) {
    max-width: 60px;
  }
}

/* 特定组件的文本溢出优化 */

/* 时间标签的文本溢出处理 */
.info-display-container .time-tag.text-overflow-span {
  max-width: calc(100% - 20px);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 已预订时间项的文本溢出处理 */
.info-display-container .booked-time-item {
  min-height: 32px;
  align-items: flex-start;
  padding: 8px 12px;
}

.info-display-container .booked-time-item .time-tag {
  line-height: 1.4;
  word-break: break-word;
  white-space: normal;
  max-width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  line-clamp: 2; /* 标准属性 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

/* 参与者标签的文本溢出处理增强 */
:deep(.participant-select .el-tag) {
  max-width: 140px;
  overflow: hidden;
}

:deep(.participant-select .el-tag .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 参与者列表的文本溢出处理 */
.participant-list :deep(.el-tag) {
  max-width: 180px;
  overflow: hidden;
}

.participant-list :deep(.el-tag .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 工具提示样式优化 */
:deep(.el-tooltip__popper) {
  max-width: 400px;
  word-wrap: break-word;
  white-space: normal;
}

:deep(.el-tooltip__popper .el-tooltip__content) {
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
