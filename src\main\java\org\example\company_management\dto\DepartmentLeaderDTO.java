package org.example.company_management.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 部门负责人DTO
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentLeaderDTO {

    /**
     * 关联记录ID
     */
    private Integer id;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    private Integer departmentId;

    /**
     * 员工ID（负责人）
     */
    @NotNull(message = "员工ID不能为空")
    private Integer employeeId;

    /**
     * 负责人角色
     * PRIMARY - 主要负责人
     * DEPUTY - 副负责人
     * ASSISTANT - 协助负责人
     */
    private String leaderRole;

    /**
     * 任职开始日期
     */
    private Date startDate;

    /**
     * 任职结束日期（NULL表示当前在职）
     */
    private Date endDate;

    /**
     * 是否激活状态
     */
    private Boolean isActive;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // ========== 关联查询字段 ==========

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门描述
     */
    private String departmentDescription;

    /**
     * 负责人姓名
     */
    private String leaderName;

    /**
     * 负责人邮箱
     */
    private String leaderEmail;

    /**
     * 负责人手机号
     */
    private String leaderPhone;

    // ========== 业务字段 ==========

    /**
     * 角色显示名称
     */
    private String leaderRoleDisplayName;

    /**
     * 是否为主要负责人
     */
    private Boolean isPrimaryLeader;

    /**
     * 是否为当前在职状态
     */
    private Boolean isCurrentlyActive;

    // ========== 批量操作字段 ==========

    /**
     * 批量操作时的部门ID列表
     */
    private List<Integer> departmentIds;

    /**
     * 批量操作时的员工ID列表
     */
    private List<Integer> employeeIds;

    /**
     * 主要负责人ID（用于批量设置）
     */
    private Integer primaryLeaderId;

    // ========== 业务方法 ==========

    /**
     * 获取角色显示名称
     *
     * @return 角色的中文显示名称
     */
    public String getLeaderRoleDisplayName() {
        if (this.leaderRole == null) {
            return "未知";
        }
        switch (this.leaderRole) {
            case "PRIMARY":
                return "主要负责人";
            case "DEPUTY":
                return "副负责人";
            case "ASSISTANT":
                return "协助负责人";
            default:
                return this.leaderRole;
        }
    }

    /**
     * 判断是否为主要负责人
     *
     * @return true-主要负责人，false-其他角色
     */
    public Boolean getIsPrimaryLeader() {
        return "PRIMARY".equals(this.leaderRole);
    }

    /**
     * 判断是否为当前在职状态
     *
     * @return true-在职，false-离职
     */
    public Boolean getIsCurrentlyActive() {
        return Boolean.TRUE.equals(this.isActive) &&
                (this.endDate == null || this.endDate.after(new Date()));
    }
}
