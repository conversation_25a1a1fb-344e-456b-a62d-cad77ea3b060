package org.example.company_management.service;

import org.example.company_management.entity.Client;
import org.example.company_management.utils.Result;

import java.util.List;
import java.util.Map;

/**
 * 客户服务接口
 */
public interface ClientService {

    /**
     * 根据ID查询客户
     *
     * @param clientId 客户ID
     * @return 客户对象
     */
    Client getClientById(Integer clientId);

    /**
     * 获取所有客户
     *
     * @return 客户列表
     */
    List<Client> getAllClients();

    /**
     * 分页查询客户
     *
     * @param params 查询参数，包含分页信息和过滤条件
     * @return 客户列表
     */
    List<Client> getClientsByPage(Map<String, Object> params);

    /**
     * 获取客户总数
     *
     * @param params 查询参数
     * @return 客户总数
     */
    int getClientCount(Map<String, Object> params);

    /**
     * 新增客户
     *
     * @param client 客户对象
     * @return Result对象，成功时包含客户ID，失败时包含错误信息
     */
    Result<Integer> saveClient(Client client);

    /**
     * 更新客户信息
     *
     * @param client 客户对象
     * @return 影响的行数
     */
    int updateClient(Client client);

    /**
     * 删除客户
     *
     * @param clientId 客户ID
     * @return 影响的行数
     */
    int deleteClient(Integer clientId);

    /**
     * 根据员工ID查询客户
     *
     * @param employeeId 员工ID
     * @return 客户列表
     */
    List<Client> getClientsByEmployee(Integer employeeId);

    /**
     * 根据名称查询客户
     *
     * @param name 客户名称
     * @return 客户列表
     */
    List<Client> getClientsByName(String name);

    /**
     * 根据邮箱查询客户
     *
     * @param email 客户邮箱
     * @return 客户对象
     */
    Client getClientByEmail(String email);

    /**
     * 根据电话查询客户
     *
     * @param phone 客户电话
     * @return 客户对象
     */
    Client getClientByPhone(String phone);

    /**
     * 分页查询当前登录用户的客户
     *
     * @param params 查询参数
     * @return 客户列表
     */
    List<Client> getMyClientsByPage(Map<String, Object> params);

    /**
     * 获取当前登录用户的客户总数
     *
     * @param params 查询参数
     * @return 客户总数
     */
    int getMyClientCount(Map<String, Object> params);

    /**
     * 获取待审批的客户列表
     *
     * @return 待审批客户列表
     */
    List<Client> getPendingClients();

    /**
     * 更新客户状态
     *
     * @param clientId 客户ID
     * @param status   新状态
     * @param rejectRemark 拒绝备注 (仅当status为'已拒绝'时相关)
     * @return 影响的行数
     */
    int updateClientStatus(Integer clientId, String status, String rejectRemark);

    /**
     * 批量更新客户状态
     *
     * @param clientIds 客户ID列表
     * @param status    新状态
     * @param rejectRemark 拒绝备注 (仅当status为'已拒绝'时相关)
     * @return 影响的行数
     */
    int batchUpdateClientStatus(List<Integer> clientIds, String status, String rejectRemark);

    /**
     * 获取待审批的客户数量
     *
     * @return 待审批客户数量
     */
    int getPendingClientCount();

    /**
     * 根据部门ID列表分页查询客户
     * @param params 查询参数
     * @return 客户列表
     */
    List<Client> getClientsByDepartments(Map<String, Object> params);

    /**
     * 根据部门ID列表获取客户总数
     * @param params 查询参数
     * @return 客户总数
     */
    int countClientsByDepartments(Map<String, Object> params);
}