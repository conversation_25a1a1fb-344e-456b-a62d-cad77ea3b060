{"name": "company-management-system-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "echarts": "^5.6.0", "element-plus": "^2.5.5", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass-embedded": "^1.89.2", "terser": "^5.39.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.2.0"}}