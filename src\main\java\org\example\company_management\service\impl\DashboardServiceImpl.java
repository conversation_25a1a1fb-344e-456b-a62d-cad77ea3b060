package org.example.company_management.service.impl;

import org.example.company_management.dto.AchievementDTO;
import org.example.company_management.mapper.DashboardMapper;
import org.example.company_management.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仪表盘服务实现类
 */
@Service
public class DashboardServiceImpl implements DashboardService {

    private final DashboardMapper dashboardMapper;

    public DashboardServiceImpl(DashboardMapper dashboardMapper) {
        this.dashboardMapper = dashboardMapper;
    }
    
    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 员工统计
        int totalEmployees = dashboardMapper.countTotalEmployees();
        int activeEmployees = dashboardMapper.countActiveEmployees();
        int inactiveEmployees = totalEmployees - activeEmployees;
        
        // 部门和职位统计
        int totalDepartments = dashboardMapper.countTotalDepartments();
        int totalPositions = dashboardMapper.countTotalPositions();
        
        // 业绩统计
        double totalPerformance = dashboardMapper.sumTotalPerformance();
        double averagePerformance = (activeEmployees > 0) ? 
                totalPerformance / activeEmployees : 0;
        
        // 构建返回数据
        Map<String, Object> employeeStats = new HashMap<>();
        employeeStats.put("total", totalEmployees);
        employeeStats.put("active", activeEmployees);
        employeeStats.put("inactive", inactiveEmployees);
        
        Map<String, Object> performanceStats = new HashMap<>();
        performanceStats.put("total", totalPerformance);
        performanceStats.put("average", averagePerformance);
        
        stats.put("employees", employeeStats);
        stats.put("departments", totalDepartments);
        stats.put("positions", totalPositions);
        stats.put("performance", performanceStats);
        
        return stats;
    }

    @Override
    public List<AchievementDTO> getEmployeeAchievements(Integer employeeId) {
        List<AchievementDTO> achievements = new ArrayList<>();

        try {
            // 1. 第一次获得新客户
            LocalDateTime firstClientDate = dashboardMapper.getFirstClientAchievementDate(employeeId);
            if (firstClientDate != null) {
                achievements.add(new AchievementDTO(
                    "第一次获得新客户",
                    "成功获得第一个新客户",
                    "FIRST_CLIENT",
                    firstClientDate,
                    "恭喜获得第一个客户！"
                ));
            } else {
                achievements.add(new AchievementDTO(
                    "第一次获得新客户",
                    "成功获得第一个新客户",
                    "FIRST_CLIENT"
                ));
            }

            // 2. 第一次获得提成（业绩达到1万）
            String firstCommissionDate = dashboardMapper.getFirstCommissionAchievementDate(employeeId);
            if (firstCommissionDate != null) {
                AchievementDTO commissionAchievement = new AchievementDTO(
                    "第一次获得提成",
                    "首次获得销售提成奖励",
                    "FIRST_COMMISSION",
                    parseYearMonthToDateTime(firstCommissionDate),
                    "月业绩达到1万元，获得提成资格！"
                );
                // 设置年月格式的日期字符串
                commissionAchievement.setAchievedDateStr(firstCommissionDate);
                achievements.add(commissionAchievement);
            } else {
                achievements.add(new AchievementDTO(
                    "第一次获得提成",
                    "首次获得销售提成奖励",
                    "FIRST_COMMISSION"
                ));
            }

            // 3. 第一次达到3万业绩
            String first30kDate = dashboardMapper.getFirst30kPerformanceAchievementDate(employeeId);
            if (first30kDate != null) {
                AchievementDTO performance30kAchievement = new AchievementDTO(
                    "第一次达到3万业绩",
                    "单月业绩达到3万元",
                    "FIRST_30K_PERFORMANCE",
                    parseYearMonthToDateTime(first30kDate),
                    "单月业绩突破3万元大关！"
                );
                // 设置年月格式的日期字符串
                performance30kAchievement.setAchievedDateStr(first30kDate);
                achievements.add(performance30kAchievement);
            } else {
                achievements.add(new AchievementDTO(
                    "第一次达到3万业绩",
                    "单月业绩达到3万元",
                    "FIRST_30K_PERFORMANCE"
                ));
            }

            // 4. 还清薪资比（累计能力值转正）
            String salaryRatioClearedDate = calculateSalaryRatioClearedDate(employeeId);
            if (salaryRatioClearedDate != null) {
                AchievementDTO salaryRatioAchievement = new AchievementDTO(
                    "还清薪资比",
                    "成功还清所有薪资比",
                    "SALARY_RATIO_CLEARED",
                    parseYearMonthToDateTime(salaryRatioClearedDate),
                    "累计能力值转为正数，薪资比已还清！"
                );
                // 设置年月格式的日期字符串
                salaryRatioAchievement.setAchievedDateStr(salaryRatioClearedDate);
                achievements.add(salaryRatioAchievement);
            } else {
                achievements.add(new AchievementDTO(
                    "还清薪资比",
                    "成功还清所有薪资比",
                    "SALARY_RATIO_CLEARED"
                ));
            }

        } catch (Exception e) {
            e.printStackTrace();
            // 如果查询失败，返回默认的未达成成就列表
            achievements.clear();
            achievements.add(new AchievementDTO("第一次获得新客户", "成功获得第一个新客户", "FIRST_CLIENT"));
            achievements.add(new AchievementDTO("第一次获得提成", "首次获得销售提成奖励", "FIRST_COMMISSION"));
            achievements.add(new AchievementDTO("第一次达到3万业绩", "单月业绩达到3万元", "FIRST_30K_PERFORMANCE"));
            achievements.add(new AchievementDTO("还清薪资比", "成功还清所有薪资比", "SALARY_RATIO_CLEARED"));
        }

        return achievements;
    }

    /**
     * 将年月字符串转换为LocalDateTime（月初第一天）
     * @param yearMonth 年月字符串（格式：YYYY-MM）
     * @return LocalDateTime对象
     */
    private LocalDateTime parseYearMonthToDateTime(String yearMonth) {
        if (yearMonth == null || yearMonth.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(yearMonth + "-01 00:00:00",
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算薪资比还清的日期
     * 通过累计计算能力值，找到首次转为正数的月份
     * @param employeeId 员工ID
     * @return 还清日期的年月字符串，如果未还清则返回null
     */
    private String calculateSalaryRatioClearedDate(Integer employeeId) {
        try {
            // 获取员工的所有业绩记录（按日期升序）
            List<Map<String, Object>> performanceRecords = dashboardMapper.getPerformanceRecordsForCapability(employeeId);

            if (performanceRecords == null || performanceRecords.isEmpty()) {
                return null;
            }

            BigDecimal cumulativeCapability = BigDecimal.ZERO;

            for (Map<String, Object> record : performanceRecords) {
                String date = (String) record.get("date");
                BigDecimal estimatedPerformance = (BigDecimal) record.get("estimated_performance");

                if (estimatedPerformance == null) {
                    estimatedPerformance = BigDecimal.ZERO;
                }

                // 获取该月的工资信息
                Map<String, Object> salaryInfo = dashboardMapper.getSalaryByEmployeeAndDate(employeeId, date);
                BigDecimal basicSalary = BigDecimal.ZERO;
                BigDecimal performanceBonus = BigDecimal.ZERO;

                if (salaryInfo != null) {
                    basicSalary = salaryInfo.get("basic_salary") != null ?
                        (BigDecimal) salaryInfo.get("basic_salary") : BigDecimal.ZERO;
                    performanceBonus = salaryInfo.get("performance_bonus") != null ?
                        (BigDecimal) salaryInfo.get("performance_bonus") : BigDecimal.ZERO;
                }

                // 计算月度能力值：预估业绩 - ((基本工资 + 绩效奖金)×3)
                BigDecimal totalSalary = basicSalary.add(performanceBonus);
                BigDecimal totalCost = totalSalary.multiply(BigDecimal.valueOf(3));
                BigDecimal monthlyCapability = estimatedPerformance.subtract(totalCost);

                // 累加到总能力值
                cumulativeCapability = cumulativeCapability.add(monthlyCapability);

                // 如果累计能力值首次转为正数，返回该月份
                if (cumulativeCapability.compareTo(BigDecimal.ZERO) > 0) {
                    return date;
                }
            }

            // 如果累计能力值始终未转正，返回null
            return null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}