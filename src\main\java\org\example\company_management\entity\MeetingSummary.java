package org.example.company_management.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会议总结实体类
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingSummary {
    
    /**
     * 总结ID
     */
    private Long id;
    
    /**
     * 会议ID
     */
    private Long meetingId;
    
    /**
     * 总结人ID
     */
    private Integer employeeId;
    
    /**
     * 总结人姓名（非数据库字段）
     */
    private String employeeName;
    
    /**
     * 总结人部门（非数据库字段）
     */
    private String departmentName;
    
    /**
     * 总结内容
     */
    private String summaryContent;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
