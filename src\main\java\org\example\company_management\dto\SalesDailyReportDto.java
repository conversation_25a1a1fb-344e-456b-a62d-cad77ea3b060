package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 销售日报数据传输对象}}
 * {{CHENGQI: 任务ID: P2-LD-007}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-06-04 10:52:42 +08:00}}
 * {{CHENGQI: 描述: 销售日报请求和响应DTO，遵循数据传输对象设计模式}}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesDailyReportDto {

    /**
     * 日报ID（编辑时使用）
     */
    private Long id;

    /**
     * 日报日期（编辑时使用）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate reportDate;

    // 注：移除了后端自动生成的字段：employeeId, yearlyNewClients, monthlyNewClients, daysSinceLastNewClient
    // 这些字段由后端根据当前登录用户和系统时间自动设置

    // ==================== 客户选择字段 ====================
    
    /**
     * 询价客户ID列表
     */
    private List<Integer> inquiryClients;
    
    /**
     * 出货客户ID列表
     */
    private List<Integer> shippingClients;
    
    /**
     * 重点开发客户ID列表
     */
    private List<Integer> keyDevelopmentClients;
    
    // ==================== 评估字段 ====================
    
    /**
     * 责任心评级
     */
    @NotBlank(message = "责任心评级不能为空")
    @Pattern(regexp = "^(优秀|中等|差)$", message = "责任心评级必须为：优秀、中等、差")
    private String responsibilityLevel;
    
    // ==================== 工作检查清单 ====================

    /**
     * 下班准备工作检查清单（接受字符串数组格式）
     */
    private List<String> endOfDayChecklist;
    
    // ==================== 文本输入字段 ====================
    
    /**
     * 今日效果
     */
    @Size(max = 2000, message = "今日效果内容不能超过2000字符")
    private String dailyResults;
    
    /**
     * 会议报告
     */
    @Size(max = 2000, message = "会议报告内容不能超过2000字符")
    private String meetingReport;
    
    /**
     * 工作日记
     */
    @Size(max = 2000, message = "工作日记内容不能超过2000字符")
    private String workDiary;
    
    // 注：移除了系统字段和关联信息字段，这些由后端自动生成或查询
    // createTime, updateTime, employeeName, departmentName, 客户详情等
    
    // ==================== 内部DTO类 ====================
    
    /**
     * 检查清单DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChecklistDto {
        /**
         * 整理完桌面
         */
        private Boolean deskOrganized;
        
        /**
         * 整理完50通预计电话邮件上级
         */
        private Boolean emailsHandled;
        
        /**
         * 会议已开完
         */
        private Boolean meetingsCompleted;
        
        /**
         * 准备好明天工作资料
         */
        private Boolean materialsReady;
        
        /**
         * 问候领导后打卡离开
         */
        private Boolean greetedLeader;
        
        /**
         * 完成项目数量
         */
        private Integer completedCount;
        
        /**
         * 总项目数量
         */
        private Integer totalCount;
        
        /**
         * 最后更新时间
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime lastUpdated;
        
        /**
         * 计算完成率
         */
        public Double getCompletionRate() {
            if (totalCount == null || totalCount == 0) {
                return 0.0;
            }
            if (completedCount == null) {
                return 0.0;
            }
            return (double) completedCount / totalCount;
        }
    }
    
    /**
     * 客户DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClientDto {
        /**
         * 客户ID
         */
        private Integer clientId;
        
        /**
         * 客户名称
         */
        private String clientName;
        
        /**
         * 客户类别
         */
        private String category;
        
        /**
         * 客户状态
         */
        private String status;
        
        /**
         * 联系人
         */
        private String contactPerson;
        
        /**
         * 联系电话
         */
        private String contactPhone;
    }
}

// {{CHENGQI: 销售日报DTO创建完成}}
