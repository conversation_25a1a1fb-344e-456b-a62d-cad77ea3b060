package org.example.company_management.service;

import org.example.company_management.entity.PromotionAttachment;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * {{CHENGQI: 推广附件服务接口}}
 * {{CHENGQI: 任务ID: P4-LD-010}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 17:45:00 +08:00}}
 * {{CHENGQI: 描述: 推广附件业务逻辑接口，提供附件的上传、下载、管理等功能}}
 */
public interface PromotionAttachmentService {

    /**
     * 上传推广附件
     *
     * @param promotionId 推广ID
     * @param file        上传的文件
     * @return 上传结果，包含附件信息
     */
    Result<PromotionAttachment> uploadAttachment(Long promotionId, MultipartFile file);

    /**
     * 上传临时附件（用于新建推广）
     *
     * @param file 上传的文件
     * @return 上传结果，包含临时附件信息
     */
    Result<PromotionAttachment> uploadTempAttachment(MultipartFile file);

    // {{FUTURE_EXTENSION: 批量附件上传接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前使用单文件上传，批量上传功能未实现且不需要}}
    // 已删除 - 使用单文件上传接口

    /**
     * 删除推广附件
     *
     * @param attachmentId 附件ID
     * @return 删除结果
     */
    Result<Void> deleteAttachment(Long attachmentId);

    // {{FUTURE_EXTENSION: 单个附件查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件信息通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件信息通过推广详情接口获取

    // {{FUTURE_EXTENSION: 推广附件列表查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前附件列表通过推广详情获取，独立查询未使用}}
    // 已删除 - 附件列表通过推广详情接口获取

    // {{FUTURE_EXTENSION: 分页附件查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要复杂的附件分页查询功能}}
    // 已删除 - 附件查询通过推广详情接口实现

    /**
     * 下载附件文件
     *
     * @param attachmentId 附件ID
     * @return 文件下载结果
     */
    Result<byte[]> downloadAttachment(Long attachmentId);

    // {{FUTURE_EXTENSION: 获取下载URL接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前直接使用下载接口，不需要单独获取URL}}
    // 已删除 - 直接使用下载接口

    /**
     * 更新附件下载次数
     *
     * @param attachmentId 附件ID
     * @return 更新结果
     */
    Result<Void> incrementDownloadCount(Long attachmentId);

    // {{FUTURE_EXTENSION: 用户附件列表查询接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要按用户查询附件功能}}
    // 已删除 - 附件管理通过推广维度进行

    /**
     * 批量删除推广的所有附件（推广删除时使用）
     *
     * @param promotionId 推广ID
     * @return 删除结果
     */
    Result<Void> deleteAttachmentsByPromotionId(Long promotionId);

    // {{FUTURE_EXTENSION: 附件统计接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前不需要附件统计功能}}
    // 已删除 - 统计功能可能在管理后台实现

    // {{FUTURE_EXTENSION: 文件验证接口 - 删除}}
    // {{CREATED: 2025-01-27 18:30:00 +08:00}}
    // {{REASON: 当前文件验证在上传时进行，不需要独立验证接口}}
    // 已删除 - 文件验证集成在上传接口中

    /**
     * 附件统计信息内部类
     */
    class AttachmentStatistics {
        private Long promotionId;
        private Integer count;
        private Long totalSize;
        private String formattedTotalSize;

        // 构造函数
        public AttachmentStatistics() {
        }

        public AttachmentStatistics(Long promotionId, Integer count, Long totalSize) {
            this.promotionId = promotionId;
            this.count = count;
            this.totalSize = totalSize;
            this.formattedTotalSize = formatFileSize(totalSize);
        }

        // Getter和Setter方法
        public Long getPromotionId() {
            return promotionId;
        }

        public void setPromotionId(Long promotionId) {
            this.promotionId = promotionId;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }

        public Long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(Long totalSize) {
            this.totalSize = totalSize;
            this.formattedTotalSize = formatFileSize(totalSize);
        }

        public String getFormattedTotalSize() {
            return formattedTotalSize;
        }

        public void setFormattedTotalSize(String formattedTotalSize) {
            this.formattedTotalSize = formattedTotalSize;
        }

        /**
         * 格式化文件大小
         */
        private String formatFileSize(Long size) {
            if (size == null || size == 0) {
                return "0 B";
            }

            long fileSize = size;
            String[] units = {"B", "KB", "MB", "GB"};
            int unitIndex = 0;

            while (fileSize >= 1024 && unitIndex < units.length - 1) {
                fileSize /= 1024;
                unitIndex++;
            }

            return String.format("%.1f %s", (double) fileSize, units[unitIndex]);
        }
    }
}

// {{CHENGQI: 推广附件服务接口创建完成}}
