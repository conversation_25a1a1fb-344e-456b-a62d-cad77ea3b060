<script setup>
// {{CHENGQI: Element Plus 中文语言配置}}
// {{CHENGQI: 修复时间: 2025-07-09 12:30:00 +08:00}}
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
</script>

<template>
    <ElConfigProvider :locale="zhCn">
        <router-view></router-view>
    </ElConfigProvider>
</template>

<style>
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #333;
}

#app {
    width: 100%;
    height: 100%;
}
</style>
