package org.example.company_management.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.example.company_management.dto.MeetingDTO;

import java.time.LocalDateTime;

/**
 * 会议时间验证器
 */
public class MeetingTimeValidator implements ConstraintValidator<ValidMeetingTime, MeetingDTO> {
    
    @Override
    public void initialize(ValidMeetingTime constraintAnnotation) {
        // 初始化方法，可以为空
    }
    
    @Override
    public boolean isValid(MeetingDTO meetingDTO, ConstraintValidatorContext context) {
        if (meetingDTO == null) {
            return true; // 让其他验证器处理null值
        }
        
        LocalDateTime startTime = meetingDTO.getStartTime();
        LocalDateTime endTime = meetingDTO.getEndTime();
        
        // 开始时间必须存在
        if (startTime == null) {
            return true; // 让@NotNull注解处理
        }
        
        // 如果结束时间存在，必须大于开始时间
        if (endTime != null && !endTime.isAfter(startTime)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("会议结束时间必须晚于开始时间")
                   .addPropertyNode("endTime")
                   .addConstraintViolation();
            return false;
        }
        
        // 开始时间不能是过去时间（允许当前时间）
        LocalDateTime now = LocalDateTime.now();
        if (startTime.isBefore(now.minusMinutes(1))) { // 允许1分钟的时间差
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("会议开始时间不能早于当前时间")
                   .addPropertyNode("startTime")
                   .addConstraintViolation();
            return false;
        }
        
        return true;
    }
}
