package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.dto.StockWithdrawalQueryDTO;
import org.example.company_management.entity.StockWithdrawal;

import java.util.List;
import java.util.Map;

/**
 * 股票提现申请数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Mapper
public interface StockWithdrawalMapper {
    
    /**
     * 插入提现申请
     * 
     * @param stockWithdrawal 提现申请信息
     * @return 影响行数
     */
    int insert(StockWithdrawal stockWithdrawal);
    
    /**
     * 根据ID查询提现申请
     * 
     * @param id 提现申请ID
     * @return 提现申请信息
     */
    StockWithdrawal selectById(@Param("id") Long id);
    
    /**
     * 根据ID查询提现申请（包含关联信息）
     * 
     * @param id 提现申请ID
     * @return 提现申请信息（包含员工姓名、审核人姓名等）
     */
    StockWithdrawal selectByIdWithDetails(@Param("id") Long id);
    
    /**
     * 更新提现申请
     * 
     * @param stockWithdrawal 提现申请信息
     * @return 影响行数
     */
    int update(StockWithdrawal stockWithdrawal);
    
    /**
     * 根据条件查询提现申请列表
     * 
     * @param queryDTO 查询条件
     * @return 提现申请列表
     */
    List<StockWithdrawal> selectByCondition(StockWithdrawalQueryDTO queryDTO);
    
    /**
     * 查询员工的提现申请列表
     * 
     * @param employeeId 员工ID
     * @param status 状态（可选）
     * @return 提现申请列表
     */
    List<StockWithdrawal> selectByEmployeeId(@Param("employeeId") Integer employeeId, 
                                           @Param("status") String status);
    
    /**
     * 查询待审核的提现申请列表
     * 
     * @return 待审核提现申请列表
     */
    List<StockWithdrawal> selectPendingList();
    
    /**
     * 统计员工已提现的股票数量
     * 
     * @param employeeId 员工ID
     * @return 已提现股票数量
     */
    Integer getWithdrawnQuantity(@Param("employeeId") Integer employeeId);
    
    /**
     * 统计员工已提现的股票金额
     * 
     * @param employeeId 员工ID
     * @return 已提现股票金额
     */
    Map<String, Object> getWithdrawnAmount(@Param("employeeId") Integer employeeId);
    
    /**
     * 统计提现申请数量（按状态）
     * 
     * @param status 状态
     * @return 申请数量
     */
    Integer countByStatus(@Param("status") String status);
    
    /**
     * 统计员工提现申请数量
     * 
     * @param employeeId 员工ID
     * @param status 状态（可选）
     * @return 申请数量
     */
    Integer countByEmployeeId(@Param("employeeId") Integer employeeId, 
                            @Param("status") String status);
    
    /**
     * 检查员工是否有待审核的提现申请
     * 
     * @param employeeId 员工ID
     * @return 待审核申请数量
     */
    Integer countPendingByEmployeeId(@Param("employeeId") Integer employeeId);
}
