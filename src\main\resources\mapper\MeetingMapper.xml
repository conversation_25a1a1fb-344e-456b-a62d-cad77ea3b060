<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.MeetingMapper">

    <!-- 会议结果映射 -->
    <resultMap id="MeetingResultMap" type="org.example.company_management.entity.Meeting">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="location" column="location"/>
        <result property="locationId" column="location_id"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorName" column="creator_name"/>
        <result property="responsibleId" column="responsible_id"/>
        <result property="responsibleName" column="responsible_name"/>
        <result property="status" column="calculated_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="participantCount" column="participant_count"/>
        <result property="summaryCount" column="summary_count"/>
        <!-- 会议地点信息关联映射 -->
        <association property="locationInfo" javaType="org.example.company_management.entity.MeetingLocation">
            <id property="id" column="loc_id"/>
            <result property="name" column="loc_name"/>
            <result property="description" column="loc_description"/>
            <result property="capacity" column="loc_capacity"/>
            <result property="facilities" column="loc_facilities"/>
            <result property="openTime" column="loc_open_time"/>
            <result property="closeTime" column="loc_close_time"/>
            <result property="availableDays" column="loc_available_days"/>
            <result property="status" column="loc_status"/>
        </association>
    </resultMap>

    <!-- 参与者结果映射 -->
    <resultMap id="ParticipantResultMap" type="org.example.company_management.entity.MeetingParticipant">
        <id property="id" column="id"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="participantType" column="participant_type"/>
        <result property="participantId" column="participant_id"/>
        <result property="participantName" column="participant_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 总结结果映射 -->
    <resultMap id="SummaryResultMap" type="org.example.company_management.entity.MeetingSummary">
        <id property="id" column="id"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="employeeName" column="employee_name"/>
        <result property="departmentName" column="department_name"/>
        <result property="summaryContent" column="summary_content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 插入会议 -->
    <insert id="insert" parameterType="org.example.company_management.entity.Meeting" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meeting (title, content, start_time, end_time, location, location_id, creator_id, responsible_id, create_time, update_time)
        VALUES (#{title}, #{content}, #{startTime}, #{endTime}, #{location}, #{locationId}, #{creatorId}, #{responsibleId}, #{createTime}, #{updateTime})
    </insert>

    <!-- 根据ID删除会议 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM meeting WHERE id = #{id}
    </delete>

    <!-- 更新会议信息 -->
    <update id="update" parameterType="org.example.company_management.entity.Meeting">
        UPDATE meeting
        SET title = #{title},
            content = #{content},
            start_time = #{startTime},
            end_time = #{endTime},
            location = #{location},
            location_id = #{locationId},
            responsible_id = #{responsibleId},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 状态更新方法已移除，现在状态由查询时实时计算 -->

    <!-- 根据ID查询会议 -->
    <select id="selectById" parameterType="long" resultMap="MeetingResultMap">
        SELECT m.id, m.title, m.content, m.start_time, m.end_time, m.location, m.location_id,
               m.creator_id, m.responsible_id, m.create_time, m.update_time,
               e.name as creator_name, r.name as responsible_name,
               CASE
                   WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                   WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                   ELSE 'FINISHED'
               END as calculated_status,
               (SELECT COUNT(*) FROM meeting_participant mp WHERE mp.meeting_id = m.id) as participant_count,
               (SELECT COUNT(*) FROM meeting_summary ms WHERE ms.meeting_id = m.id) as summary_count,
               l.id as loc_id, l.name as loc_name, l.description as loc_description,
               l.capacity as loc_capacity, l.facilities as loc_facilities,
               l.open_time as loc_open_time, l.close_time as loc_close_time,
               l.available_days as loc_available_days, l.status as loc_status
        FROM meeting m
        LEFT JOIN employee e ON m.creator_id = e.employee_id
        LEFT JOIN employee r ON m.responsible_id = r.employee_id
        LEFT JOIN meeting_location l ON m.location_id = l.id
        WHERE m.id = #{id}
    </select>

    <!-- 分页查询会议列表 -->
    <select id="selectByPage" parameterType="map" resultMap="MeetingResultMap">
        SELECT m.id, m.title, m.content, m.start_time, m.end_time, m.location, m.location_id,
               m.creator_id, m.responsible_id, m.create_time, m.update_time,
               e.name as creator_name, r.name as responsible_name,
               CASE
                   WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                   WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                   ELSE 'FINISHED'
               END as calculated_status,
               (SELECT COUNT(*) FROM meeting_participant mp WHERE mp.meeting_id = m.id) as participant_count,
               (SELECT COUNT(*) FROM meeting_summary ms WHERE ms.meeting_id = m.id) as summary_count,
               l.id as loc_id, l.name as loc_name, l.description as loc_description,
               l.capacity as loc_capacity, l.facilities as loc_facilities,
               l.open_time as loc_open_time, l.close_time as loc_close_time,
               l.available_days as loc_available_days, l.status as loc_status
        FROM meeting m
        LEFT JOIN employee e ON m.creator_id = e.employee_id
        LEFT JOIN employee r ON m.responsible_id = r.employee_id
        LEFT JOIN meeting_location l ON m.location_id = l.id
        <where>
            <if test="title != null and title != ''">
                AND m.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null and status != ''">
                AND (CASE
                    WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                    WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                    ELSE 'FINISHED'
                END) = #{status}
            </if>
            <if test="creatorId != null">
                AND m.creator_id = #{creatorId}
            </if>
            <if test="startTime != null">
                AND m.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND m.start_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY m.start_time DESC, m.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 查询会议总数 -->
    <select id="countTotal" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM meeting m
        <where>
            <if test="title != null and title != ''">
                AND m.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null and status != ''">
                AND (CASE
                    WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                    WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                    ELSE 'FINISHED'
                END) = #{status}
            </if>
            <if test="creatorId != null">
                AND m.creator_id = #{creatorId}
            </if>
            <if test="startTime != null">
                AND m.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND m.start_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 查询员工相关的会议列表（分页） -->
    <select id="selectEmployeeMeetingsByPage" parameterType="map" resultMap="MeetingResultMap">
        SELECT DISTINCT m.id, m.title, m.content, m.start_time, m.end_time, m.location, m.location_id,
               m.creator_id, m.responsible_id, m.create_time, m.update_time,
               e.name as creator_name, r.name as responsible_name,
               CASE
                   WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                   WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                   ELSE 'FINISHED'
               END as calculated_status,
               (SELECT COUNT(*) FROM meeting_participant mp WHERE mp.meeting_id = m.id) as participant_count,
               (SELECT COUNT(*) FROM meeting_summary ms WHERE ms.meeting_id = m.id) as summary_count
        FROM meeting m
        LEFT JOIN employee e ON m.creator_id = e.employee_id
        LEFT JOIN employee r ON m.responsible_id = r.employee_id
        INNER JOIN meeting_participant mp ON m.id = mp.meeting_id
        LEFT JOIN employee emp ON mp.participant_id = emp.employee_id AND mp.participant_type = 'EMPLOYEE'
        LEFT JOIN department dept ON mp.participant_id = dept.department_id AND mp.participant_type = 'DEPARTMENT'
        LEFT JOIN employee dept_emp ON dept.department_id = dept_emp.department_id
        WHERE (
            (mp.participant_type = 'EMPLOYEE' AND mp.participant_id = #{employeeId})
            OR
            (mp.participant_type = 'DEPARTMENT' AND dept_emp.employee_id = #{employeeId})
        )
        <if test="status != null and status != ''">
            AND (CASE
                WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                ELSE 'FINISHED'
            END) = #{status}
        </if>
        <if test="title != null and title != ''">
            AND m.title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY m.start_time DESC, m.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 查询员工相关的会议总数 -->
    <select id="countEmployeeMeetingsTotal" parameterType="map" resultType="int">
        SELECT COUNT(DISTINCT m.id)
        FROM meeting m
        INNER JOIN meeting_participant mp ON m.id = mp.meeting_id
        LEFT JOIN employee emp ON mp.participant_id = emp.employee_id AND mp.participant_type = 'EMPLOYEE'
        LEFT JOIN department dept ON mp.participant_id = dept.department_id AND mp.participant_type = 'DEPARTMENT'
        LEFT JOIN employee dept_emp ON dept.department_id = dept_emp.department_id
        WHERE (
            (mp.participant_type = 'EMPLOYEE' AND mp.participant_id = #{employeeId})
            OR
            (mp.participant_type = 'DEPARTMENT' AND dept_emp.employee_id = #{employeeId})
        )
        <if test="status != null and status != ''">
            AND (CASE
                WHEN NOW() &lt; m.start_time THEN 'NOT_STARTED'
                WHEN m.end_time IS NULL OR NOW() &lt; m.end_time THEN 'IN_PROGRESS'
                ELSE 'FINISHED'
            END) = #{status}
        </if>
        <if test="title != null and title != ''">
            AND m.title LIKE CONCAT('%', #{title}, '%')
        </if>
    </select>

    <!-- 批量插入会议参与者 -->
    <insert id="insertParticipants" parameterType="list">
        INSERT INTO meeting_participant (meeting_id, participant_type, participant_id, create_time)
        VALUES
        <foreach collection="participants" item="participant" separator=",">
            (#{participant.meetingId}, #{participant.participantType}, #{participant.participantId}, #{participant.createTime})
        </foreach>
    </insert>

    <!-- 根据会议ID删除所有参与者 -->
    <delete id="deleteParticipantsByMeetingId" parameterType="long">
        DELETE FROM meeting_participant WHERE meeting_id = #{meetingId}
    </delete>

    <!-- 根据会议ID查询参与者列表 -->
    <select id="selectParticipantsByMeetingId" parameterType="long" resultMap="ParticipantResultMap">
        SELECT mp.id, mp.meeting_id, mp.participant_type, mp.participant_id, mp.create_time,
               CASE
                   WHEN mp.participant_type = 'DEPARTMENT' THEN d.department_name
                   WHEN mp.participant_type = 'EMPLOYEE' THEN e.name
                   ELSE ''
               END as participant_name
        FROM meeting_participant mp
        LEFT JOIN department d ON mp.participant_id = d.department_id AND mp.participant_type = 'DEPARTMENT'
        LEFT JOIN employee e ON mp.participant_id = e.employee_id AND mp.participant_type = 'EMPLOYEE'
        WHERE mp.meeting_id = #{meetingId}
        ORDER BY mp.participant_type, mp.participant_id
    </select>

    <!-- 插入会议总结 -->
    <insert id="insertSummary" parameterType="org.example.company_management.entity.MeetingSummary" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meeting_summary (meeting_id, employee_id, summary_content, create_time, update_time)
        VALUES (#{meetingId}, #{employeeId}, #{summaryContent}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新会议总结 -->
    <update id="updateSummary" parameterType="org.example.company_management.entity.MeetingSummary">
        UPDATE meeting_summary
        SET summary_content = #{summaryContent},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除会议总结 -->
    <delete id="deleteSummaryById" parameterType="long">
        DELETE FROM meeting_summary WHERE id = #{id}
    </delete>

    <!-- 根据会议ID查询总结列表 -->
    <select id="selectSummariesByMeetingId" parameterType="long" resultMap="SummaryResultMap">
        SELECT ms.id, ms.meeting_id, ms.employee_id, ms.summary_content, ms.create_time, ms.update_time,
               e.name as employee_name, d.department_name
        FROM meeting_summary ms
        LEFT JOIN employee e ON ms.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        WHERE ms.meeting_id = #{meetingId}
        ORDER BY ms.create_time DESC
    </select>

    <!-- 根据会议ID和员工ID查询总结 -->
    <select id="selectSummaryByMeetingIdAndEmployeeId" resultMap="SummaryResultMap">
        SELECT ms.id, ms.meeting_id, ms.employee_id, ms.summary_content, ms.create_time, ms.update_time,
               e.name as employee_name, d.department_name
        FROM meeting_summary ms
        LEFT JOIN employee e ON ms.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        WHERE ms.meeting_id = #{meetingId} AND ms.employee_id = #{employeeId}
    </select>

</mapper>
