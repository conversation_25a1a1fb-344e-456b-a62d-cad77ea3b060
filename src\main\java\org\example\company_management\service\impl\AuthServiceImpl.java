package org.example.company_management.service.impl;

import org.example.company_management.entity.Employee;
import org.example.company_management.mapper.EmployeeMapper;
import org.example.company_management.service.AuthService;
import org.example.company_management.service.DepartmentLeaderService;
import org.example.company_management.utils.Md5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 认证服务实现
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private DepartmentLeaderService departmentLeaderService;

    @Override
    public Employee login(String phone, String password) {
        // 根据手机号查询员工（包含密码，专用于认证）
        Employee employee = employeeMapper.selectByPhoneForAuth(phone);

        // 如果员工不存在，或者密码不匹配，抛出异常
        if (employee == null || !Md5Util.match(password, employee.getPassword())) {
            throw new RuntimeException("手机号或密码错误");
        }

        // 如果员工已离职，抛出异常
        if (employee.getExitDate() != null) {
            throw new RuntimeException("账号已被禁用，请联系管理员");
        }

        // 如果员工不是管理员，抛出异常
        if (!"admin".equalsIgnoreCase(employee.getRole())) {
            throw new RuntimeException("只有管理员才能登录后台系统");
        }

        // 将密码置空，避免返回给前端
        employee.setPassword(null);

        return employee;
    }

    @Override
    public Employee employeeLogin(String phone, String password) {
        // 根据手机号查询员工（包含密码，专用于认证）
        Employee employee = employeeMapper.selectByPhoneForAuth(phone);

        // 如果员工不存在，或者密码不匹配，抛出异常
        if (employee == null || !Md5Util.match(password, employee.getPassword())) {
            throw new RuntimeException("手机号或密码错误");
        }

        // 如果员工已离职，抛出异常
        if (employee.getExitDate() != null) {
            throw new RuntimeException("账号已被禁用，请联系管理员");
        }

        // 将密码置空，避免返回给前端
        employee.setPassword(null);

        return employee;
    }

    @Override
    public Employee getEmployeeByPhone(String phone) {
        Employee employee = employeeMapper.selectByPhone(phone);
        if (employee != null) {
            employee.setPassword(null);
        }
        return employee;
    }

    @Override
    public Employee getEmployeeById(Integer employeeId) {
        Employee employee = employeeMapper.selectById(employeeId);
        if (employee != null) {
            // 将密码置空，避免返回给前端
            employee.setPassword(null);

            // 查询用户负责的部门ID列表，用于前端菜单权限控制
            try {
                employee.setManagedDepartmentIds(
                    departmentLeaderService.getActiveDepartmentIds(employeeId)
                );
            } catch (Exception e) {
                // 如果查询失败，设置为空列表，确保不影响其他功能
                employee.setManagedDepartmentIds(new java.util.ArrayList<>());
            }
        }
        return employee;
    }

    @Override
    @Transactional
    public Employee registerAdmin(String name, String phone, String password) {
        // 检查手机号是否已存在
        Employee existingEmployeeByPhone = employeeMapper.selectByPhone(phone);
        if (existingEmployeeByPhone != null) {
            throw new RuntimeException("该手机号已被注册");
        }

        // 创建新管理员
        Employee employee = new Employee();
        employee.setName(name);
        employee.setPhone(phone);
        employee.setPassword(Md5Util.encrypt(password));
        employee.setRole("admin");
        employee.setEntryDate(new Date());
        employee.setCreateTime(new Date());
        employee.setUpdateTime(new Date());

        // 设置默认值，避免数据库非空约束错误
        employee.setIdCard("待完善"); // 设置默认身份证号
        employee.setLogisticsRoute("待完善"); // 设置默认物流航线

        // 保存到数据库
        employeeMapper.insert(employee);

        // 查询新创建的管理员
        Employee newAdmin = employeeMapper.selectByPhone(phone);
        if (newAdmin != null) {
            // 将密码置空，避免返回给前端
            newAdmin.setPassword(null);
        }

        return newAdmin;
    }

    @Override
    @Transactional
    public Employee updateProfile(Integer employeeId, String name, String phone, String email, String newPassword, String oldPassword) {
        Employee employee = employeeMapper.selectByIdForAuth(employeeId);
        if (employee == null) {
            throw new RuntimeException("员工不存在");
        }

        boolean hasChanges = false;

        // 更新姓名
        if (StringUtils.hasText(name) && !name.equals(employee.getName())) {
            employee.setName(name);
            hasChanges = true;
        }

        // 更新手机号
        if (StringUtils.hasText(phone) && !phone.equals(employee.getPhone())) {
            // 检查新手机号是否已被其他不同用户使用
            Employee existingByPhone = employeeMapper.selectByPhone(phone);
            if (existingByPhone != null && !existingByPhone.getEmployeeId().equals(employeeId)) {
                throw new RuntimeException("该手机号已被其他用户使用");
            }
            employee.setPhone(phone);
            hasChanges = true;
        }

        // 更新邮箱
        if (email != null) {
            if (!email.equals(employee.getEmail())) {
                if (StringUtils.hasText(email)) {
                    Employee existingByEmail = employeeMapper.selectByEmail(email);
                    if (existingByEmail != null && !existingByEmail.getEmployeeId().equals(employeeId)) {
                        throw new RuntimeException("该邮箱已被其他用户使用");
                    }
                    employee.setEmail(email);
                } else {
                    employee.setEmail(null);
                }
                hasChanges = true;
            }
        } else if (employee.getEmail() != null) {
            employee.setEmail(null);
            hasChanges = true;
        }

        // 更新密码
        if (StringUtils.hasText(newPassword)) {
            if (!StringUtils.hasText(oldPassword)) {
                throw new RuntimeException("必须提供旧密码才能更改密码");
            }
            if (!Md5Util.match(oldPassword, employee.getPassword())) {
                throw new RuntimeException("旧密码不正确");
            }
            employee.setPassword(Md5Util.encrypt(newPassword));
            hasChanges = true;
        } else if (hasChanges) {
            // If only other fields changed, we still need to persist the existing password (which is hashed)
            // or ensure the update method in mapper doesn't nullify password if not provided.
            // For safety, explicitly set password to existing if not changing, or ensure mapper handles it.
            // employee.setPassword(employee.getPassword()); // No, this is already set if not changing
        }

        if (hasChanges) {
            employee.setUpdateTime(new Date());
            employeeMapper.update(employee);
        }

        // 返回更新后的员工信息
        Employee updatedEmployee = employeeMapper.selectById(employeeId);
        if (updatedEmployee != null) {
            updatedEmployee.setPassword(null);
        }
        return updatedEmployee;
    }
} 