package org.example.company_management.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 部门负责人关联实体类
 * 支持一个部门配置多个负责人的多对多关系
 * 
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentLeader {
    
    /**
     * 关联记录ID
     */
    private Integer id;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 员工ID（负责人）
     */
    private Integer employeeId;
    
    /**
     * 负责人角色
     * PRIMARY - 主要负责人
     * DEPUTY - 副负责人  
     * ASSISTANT - 协助负责人
     */
    private String leaderRole;
    
    /**
     * 任职开始日期
     */
    private Date startDate;
    
    /**
     * 任职结束日期（NULL表示当前在职）
     */
    private Date endDate;
    
    /**
     * 是否激活状态
     */
    private Boolean isActive;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    // ========== 关联查询字段（非数据库字段） ==========
    
    /**
     * 部门名称（关联查询字段）
     */
    private String departmentName;
    
    /**
     * 部门描述（关联查询字段）
     */
    private String departmentDescription;
    
    /**
     * 负责人姓名（关联查询字段）
     */
    private String leaderName;
    
    /**
     * 负责人邮箱（关联查询字段）
     */
    private String leaderEmail;
    
    /**
     * 负责人手机号（关联查询字段）
     */
    private String leaderPhone;
    
    // ========== 业务方法 ==========
    
    /**
     * 判断是否为主要负责人
     * @return true-主要负责人，false-其他角色
     */
    public boolean isPrimaryLeader() {
        return "PRIMARY".equals(this.leaderRole);
    }
    
    /**
     * 判断是否为当前在职状态
     * @return true-在职，false-离职
     */
    public boolean isCurrentlyActive() {
        return Boolean.TRUE.equals(this.isActive) && 
               (this.endDate == null || this.endDate.after(new Date()));
    }
    
    /**
     * 获取角色显示名称
     * @return 角色的中文显示名称
     */
    public String getLeaderRoleDisplayName() {
        if (this.leaderRole == null) {
            return "未知";
        }
        switch (this.leaderRole) {
            case "PRIMARY":
                return "主要负责人";
            case "DEPUTY":
                return "副负责人";
            case "ASSISTANT":
                return "协助负责人";
            default:
                return this.leaderRole;
        }
    }
}
