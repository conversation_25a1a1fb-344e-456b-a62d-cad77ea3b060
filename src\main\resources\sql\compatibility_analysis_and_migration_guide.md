# 部门多负责人功能兼容性分析与迁移指南

## 📊 当前代码影响分析

### 1. **数据库层面影响**

#### 受影响的表和字段：

- `department.leader_id` - 将被移除
- 新增 `department_leader` 关联表
- 新增兼容性视图 `department_with_primary_leader`

#### 受影响的SQL查询：

- `DepartmentMapper.xml` 中所有涉及 `leader_id` 的查询
- 基础查询列 `Base_Column_List` 包含 `leader_id`
- 所有JOIN查询 `LEFT JOIN employee e ON d.leader_id = e.employee_id`

### 2. **Java代码层面影响**

#### 实体类：

- `Department.java` - `leaderId` 字段需要保留（向后兼容）
- 需要新增 `DepartmentLeader.java` 实体类

#### Service层：

- `DepartmentService.getDepartmentsByLeaderId()` - 需要适配新表结构
- `PromotionServiceImpl.getManagedDepartmentIds()` - 权限控制逻辑需要更新

#### Mapper层：

- `DepartmentMapper.xml` - 所有查询需要适配新的关联表结构
- 需要新增 `DepartmentLeaderMapper.xml`

### 3. **前端代码影响**

#### 管理端：

- `Department.vue` - 部门负责人选择需要支持多选
- `department.js` API调用需要适配

#### 用户端：

- 权限控制相关的前端逻辑需要更新

## 🔧 兼容性解决方案

### 方案一：渐进式迁移（推荐）

#### 第一阶段：数据库结构升级

1. 执行 `migration_department_leaders.sql`
2. 保留原 `department.leader_id` 字段
3. 创建兼容性视图和存储过程

#### 第二阶段：后端代码适配

1. 新增 `DepartmentLeader` 实体类
2. 创建 `DepartmentLeaderService` 和 `DepartmentLeaderMapper`
3. 更新现有查询逻辑，优先使用新表，fallback到旧字段

#### 第三阶段：前端代码适配

1. 更新部门管理界面，支持多负责人选择
2. 更新权限控制逻辑
3. 测试所有相关功能

#### 第四阶段：清理旧代码

1. 删除 `department.leader_id` 字段
2. 清理相关的旧代码和注释

### 方案二：一次性迁移（风险较高）

直接执行完整的数据库和代码迁移，适合测试环境或新项目。

## 📝 具体实施步骤

### 步骤1：创建新的实体类和Mapper

```java
// 新增 DepartmentLeader.java 实体类
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentLeader {
    private Integer id;
    private Integer departmentId;
    private Integer employeeId;
    private String leaderRole;
    private Date startDate;
    private Date endDate;
    private Boolean isActive;
    private String remark;
    private Date createTime;
    private Date updateTime;

    // 关联查询字段
    private String departmentName;
    private String leaderName;
    private String leaderEmail;
}
```

### 步骤2：更新DepartmentService接口

```java
public interface DepartmentService {
    // 保留原有方法以确保兼容性
    List<Department> getDepartmentsByLeaderId(Integer leaderId);

    // 新增多负责人相关方法
    List<DepartmentLeader> getDepartmentLeaders(Integer departmentId);

    List<DepartmentLeader> getEmployeeDepartments(Integer employeeId);

    void addDepartmentLeader(DepartmentLeader departmentLeader);

    void removeDepartmentLeader(Integer departmentId, Integer employeeId);

    void updateDepartmentLeader(DepartmentLeader departmentLeader);

    // 兼容性方法
    Integer getPrimaryLeaderId(Integer departmentId);

    List<Integer> getAllLeaderIds(Integer departmentId);
}
```

### 步骤3：更新权限控制逻辑

```java
// PromotionServiceImpl.java 中的权限控制方法需要更新
private List<Integer> getManagedDepartmentIds() {
    Integer currentEmployeeId = ThreadLocalUtil.getCurrentEmployeeId();

    // 使用新的多负责人查询
    List<DepartmentLeader> departmentLeaders = departmentLeaderService
            .getEmployeeDepartments(currentEmployeeId);

    List<Integer> departmentIds = new ArrayList<>();
    for (DepartmentLeader dl : departmentLeaders) {
        if (dl.getIsActive()) {
            departmentIds.add(dl.getDepartmentId());
            // 递归获取子部门
            List<Integer> childIds = getAllChildDepartmentIds(dl.getDepartmentId());
            departmentIds.addAll(childIds);
        }
    }

    return departmentIds;
}
```

## ⚠️ 风险评估与注意事项

### 高风险点：

1. **数据迁移失败** - 可能导致部门负责人信息丢失
2. **权限控制失效** - 可能影响推广审核等功能
3. **前端界面异常** - 部门管理界面可能无法正常显示

### 缓解措施：

1. **完整备份** - 执行任何操作前必须备份数据库
2. **分步测试** - 每个步骤完成后进行功能测试
3. **回滚准备** - 准备完整的回滚脚本和流程
4. **监控告警** - 部署后密切监控相关功能

## 🚀 执行时间表建议

### 测试环境（1-2天）：

- Day 1: 数据库迁移 + 后端代码适配
- Day 2: 前端代码适配 + 功能测试

### 生产环境（3-5天）：

- Day 1: 数据库备份 + 迁移脚本执行
- Day 2-3: 后端代码部署 + 测试
- Day 4: 前端代码部署 + 测试
- Day 5: 全面功能验证 + 性能监控

## 📞 技术支持

如在迁移过程中遇到问题，请：

1. 立即停止操作
2. 检查错误日志
3. 使用回滚脚本恢复
4. 联系技术团队支持
