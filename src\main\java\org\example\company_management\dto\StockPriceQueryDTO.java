package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 股票价格查询DTO，用于封装查询条件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockPriceQueryDTO {
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 备注关键词（模糊搜索）
     */
    private String remark;

    /**
     * 排序字段（默认按时间倒序）
     */
    private String orderBy = "time";

    /**
     * 排序方向（ASC/DESC，默认DESC）
     */
    private String orderDirection = "DESC";
}
