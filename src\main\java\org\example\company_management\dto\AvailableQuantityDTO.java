package org.example.company_management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 可提现数量DTO
 * 用于前端和后端之间的数据传输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AvailableQuantityDTO {
    /**
     * 可提现数量
     */
    private Integer availableQuantity;
    
    /**
     * 当前股票单价
     */
    private BigDecimal currentPrice;
    
    /**
     * 可提现金额
     */
    private BigDecimal availableValue;
}
