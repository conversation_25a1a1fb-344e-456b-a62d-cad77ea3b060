<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeStockMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.EmployeeStock">
        <id column="id" property="id"/>
        <result column="stock_id" property="stockId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="quantity" property="quantity"/>
        <result column="acquisition_time" property="acquisitionTime"/>
        <result column="unlock_time" property="unlockTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 扩展结果映射（包含员工和部门信息） -->
    <resultMap id="ExtendedResultMap" type="org.example.company_management.entity.EmployeeStock" extends="BaseResultMap">
        <result column="employee_name" property="employeeName"/>
        <result column="department_name" property="departmentName"/>
        <result column="current_price" property="currentPrice"/>
        <result column="total_value" property="totalValue"/>
        <result column="is_unlocked" property="isUnlocked"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        es.id, es.stock_id, es.employee_id, es.quantity, es.acquisition_time, 
        es.unlock_time, es.remark, es.create_time, es.update_time
    </sql>

    <!-- 扩展字段（包含关联信息） -->
    <sql id="Extended_Column_List">
        <include refid="Base_Column_List"/>,
        e.name as employee_name,
        d.department_name,
        sp.unit_price as current_price,
        (es.quantity * sp.unit_price) as total_value,
        CASE 
            WHEN es.unlock_time IS NULL THEN TRUE
            WHEN es.unlock_time &lt;= NOW() THEN TRUE
            ELSE FALSE
        END as is_unlocked
    </sql>

    <!-- 插入员工股票记录 -->
    <insert id="insert" parameterType="org.example.company_management.entity.EmployeeStock" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO employee_stock (stock_id, employee_id, quantity, acquisition_time, unlock_time, remark, create_time, update_time)
        VALUES (#{stockId}, #{employeeId}, #{quantity}, #{acquisitionTime}, #{unlockTime}, #{remark}, NOW(), NOW())
    </insert>

    <!-- 根据ID删除员工股票记录 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM employee_stock WHERE id = #{id}
    </delete>

    <!-- 更新员工股票记录 -->
    <update id="updateById" parameterType="org.example.company_management.entity.EmployeeStock">
        UPDATE employee_stock
        SET stock_id = #{stockId},
            employee_id = #{employeeId},
            quantity = #{quantity},
            acquisition_time = #{acquisitionTime},
            unlock_time = #{unlockTime},
            remark = #{remark},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询员工股票记录 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM employee_stock es
        WHERE es.id = #{id}
    </select>

    <!-- 分页查询员工股票记录（包含员工和部门信息） -->
    <select id="selectByPage" parameterType="map" resultMap="ExtendedResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        <where>
            <if test="employeeId != null">
                AND es.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="acquisitionStartTime != null">
                AND es.acquisition_time >= #{acquisitionStartTime}
            </if>
            <if test="acquisitionEndTime != null">
                AND es.acquisition_time &lt;= #{acquisitionEndTime}
            </if>
            <if test="isUnlocked != null">
                <choose>
                    <when test="isUnlocked == true">
                        AND (es.unlock_time IS NULL OR es.unlock_time &lt;= NOW())
                    </when>
                    <otherwise>
                        AND es.unlock_time IS NOT NULL AND es.unlock_time > NOW()
                    </otherwise>
                </choose>
            </if>
            <if test="remark != null and remark != ''">
                AND es.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                <choose>
                    <when test="orderBy == 'acquisition_time'">
                        ORDER BY es.acquisition_time
                    </when>
                    <when test="orderBy == 'quantity'">
                        ORDER BY es.quantity
                    </when>
                    <when test="orderBy == 'employee_name'">
                        ORDER BY e.name
                    </when>
                    <when test="orderBy == 'department_name'">
                        ORDER BY d.department_name
                    </when>
                    <otherwise>
                        ORDER BY es.acquisition_time
                    </otherwise>
                </choose>
                <if test="orderDirection != null and orderDirection != ''">
                    <choose>
                        <when test="orderDirection == 'ASC'">ASC</when>
                        <otherwise>DESC</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                ORDER BY es.acquisition_time DESC
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 分页查询员工股票记录（使用PageHelper） -->
    <select id="selectByPageWithPageHelper" parameterType="map" resultMap="ExtendedResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        <where>
            <if test="employeeId != null">
                AND es.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="acquisitionStartTime != null">
                AND es.acquisition_time >= #{acquisitionStartTime}
            </if>
            <if test="acquisitionEndTime != null">
                AND es.acquisition_time &lt;= #{acquisitionEndTime}
            </if>
            <if test="isUnlocked != null">
                <choose>
                    <when test="isUnlocked == true">
                        AND (es.unlock_time IS NULL OR es.unlock_time &lt;= NOW())
                    </when>
                    <otherwise>
                        AND es.unlock_time IS NOT NULL AND es.unlock_time > NOW()
                    </otherwise>
                </choose>
            </if>
            <if test="remark != null and remark != ''">
                AND es.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                <choose>
                    <when test="orderBy == 'acquisition_time'">
                        ORDER BY es.acquisition_time
                    </when>
                    <when test="orderBy == 'quantity'">
                        ORDER BY es.quantity
                    </when>
                    <when test="orderBy == 'employee_name'">
                        ORDER BY e.name
                    </when>
                    <when test="orderBy == 'department_name'">
                        ORDER BY d.department_name
                    </when>
                    <otherwise>
                        ORDER BY es.acquisition_time
                    </otherwise>
                </choose>
                <if test="orderDirection != null and orderDirection != ''">
                    <choose>
                        <when test="orderDirection == 'ASC'">ASC</when>
                        <otherwise>DESC</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                ORDER BY es.acquisition_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 统计员工股票记录总数 -->
    <select id="countTotal" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        <where>
            <if test="employeeId != null">
                AND es.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND e.name LIKE CONCAT('%', #{employeeName}, '%')
            </if>
            <if test="departmentId != null">
                AND e.department_id = #{departmentId}
            </if>
            <if test="acquisitionStartTime != null">
                AND es.acquisition_time >= #{acquisitionStartTime}
            </if>
            <if test="acquisitionEndTime != null">
                AND es.acquisition_time &lt;= #{acquisitionEndTime}
            </if>
            <if test="isUnlocked != null">
                <choose>
                    <when test="isUnlocked == true">
                        AND (es.unlock_time IS NULL OR es.unlock_time &lt;= NOW())
                    </when>
                    <otherwise>
                        AND es.unlock_time IS NOT NULL AND es.unlock_time > NOW()
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 根据员工ID查询员工股票记录 -->
    <select id="selectByEmployeeId" resultMap="ExtendedResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.employee_id = #{employeeId}
        ORDER BY es.acquisition_time DESC
    </select>

    <!-- 删除：selectByDepartmentId - 可通过分页查询+部门筛选实现 -->

    <!-- 查询指定员工的股票总数量 -->
    <select id="sumQuantityByEmployeeId" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(quantity), 0)
        FROM employee_stock
        WHERE employee_id = #{employeeId}
    </select>

    <!-- 查询指定员工的股票总价值 -->
    <select id="sumTotalValueByEmployeeId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(es.quantity * sp.unit_price), 0)
        FROM employee_stock es
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.employee_id = #{employeeId}
    </select>

    <!-- 查询已解禁的员工股票记录 -->
    <select id="selectUnlockedStocks" resultMap="ExtendedResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.unlock_time IS NULL OR es.unlock_time &lt;= NOW()
        ORDER BY es.acquisition_time DESC
    </select>

    <!-- 查询未解禁的员工股票记录 -->
    <select id="selectLockedStocks" resultMap="ExtendedResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.unlock_time IS NOT NULL AND es.unlock_time > NOW()
        ORDER BY es.acquisition_time DESC
    </select>

    <!-- 删除：selectByStockId - 使用场景较少 -->

    <!-- 检查员工是否已持有指定股票 -->
    <select id="countByEmployeeAndStock" resultType="int">
        SELECT COUNT(*)
        FROM employee_stock
        WHERE employee_id = #{employeeId} AND stock_id = #{stockId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 查询员工股票统计信息 -->
    <select id="selectStockStatistics" resultType="map">
        SELECT 
            d.department_name,
            COUNT(DISTINCT es.employee_id) as employee_count,
            SUM(es.quantity) as total_quantity,
            SUM(es.quantity * sp.unit_price) as total_value
        FROM employee_stock es
        LEFT JOIN employee e ON es.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        GROUP BY d.department_id, d.department_name
        ORDER BY total_value DESC
    </select>

    <!-- ==================== 用户端专用查询 ==================== -->

    <!-- 统计指定员工的已解禁股票数量 -->
    <select id="countUnlockedStocksByEmployee" resultType="java.lang.Integer">
        SELECT SUM(es.quantity)
        FROM employee_stock es
        WHERE es.employee_id = #{employeeId}
        AND es.unlock_time &lt;= NOW()
    </select>

    <!-- 统计指定员工的未解禁股票数量 -->
    <select id="countLockedStocksByEmployee" resultType="java.lang.Integer">
        SELECT SUM(es.quantity)
        FROM employee_stock es
        WHERE es.employee_id = #{employeeId}
        AND es.unlock_time > NOW()
    </select>

    <!-- 统计指定员工的股票记录总数 -->
    <select id="countStockRecordsByEmployee" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee_stock es
        WHERE es.employee_id = #{employeeId}
    </select>

    <!-- 计算指定员工的股票总数量（用户端专用） -->
    <select id="sumQuantityByEmployee" resultType="java.lang.Integer">
        SELECT SUM(es.quantity)
        FROM employee_stock es
        WHERE es.employee_id = #{employeeId}
    </select>

    <!-- 计算指定员工的股票总价值（用户端专用，使用最新股票价格） -->
    <select id="sumValueByEmployee" resultType="java.math.BigDecimal">
        SELECT SUM(es.quantity * sp.unit_price) as total_value
        FROM employee_stock es
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.employee_id = #{employeeId}
    </select>

    <!-- 查询员工股票详细统计信息（新增） -->
    <select id="selectDetailedStatisticsByEmployee" resultType="map">
        SELECT
            -- 总股票数量和价值
            COALESCE(SUM(es.quantity), 0) as totalQuantity,
            COALESCE(SUM(es.quantity * sp.unit_price), 0) as totalValue,

            -- 已解禁股票数量和价值
            COALESCE(SUM(CASE WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity ELSE 0 END), 0) as unlockedQuantity,
            COALESCE(SUM(CASE WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity * sp.unit_price ELSE 0 END), 0) as unlockedValue,

            -- 未解禁股票数量和价值
            COALESCE(SUM(CASE WHEN es.unlock_time IS NOT NULL AND es.unlock_time > NOW() THEN es.quantity ELSE 0 END), 0) as lockedQuantity,
            COALESCE(SUM(CASE WHEN es.unlock_time IS NOT NULL AND es.unlock_time > NOW() THEN es.quantity * sp.unit_price ELSE 0 END), 0) as lockedValue,

            -- 股票记录总数
            COUNT(es.id) as totalRecords,

            -- 最新股票价格和时间
            sp.unit_price as latestStockPrice,
            sp.time as latestStockPriceTime
        FROM employee_stock es
        LEFT JOIN (
            SELECT unit_price, time
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
        WHERE es.employee_id = #{employeeId}
        GROUP BY sp.unit_price, sp.time
    </select>

    <!-- 计算员工可提现数量（更新：支持股票提现表） -->
    <select id="calculateAvailableQuantity" resultType="map">
        SELECT
            COALESCE(unlocked.total_unlocked, 0) - COALESCE(withdrawn.total_withdrawn, 0) as availableQuantity,
            sp.unit_price as currentPrice,
            (COALESCE(unlocked.total_unlocked, 0) - COALESCE(withdrawn.total_withdrawn, 0)) * sp.unit_price as availableValue
        FROM (
            -- 已解禁股票总数
            SELECT SUM(quantity) as total_unlocked
            FROM employee_stock
            WHERE employee_id = #{employeeId}
            AND (unlock_time IS NULL OR unlock_time &lt;= NOW())
        ) unlocked
        LEFT JOIN (
            -- 已提现股票总数（从stock_withdrawal表查询已批准的提现数量）
            SELECT COALESCE(SUM(quantity), 0) as total_withdrawn
            FROM stock_withdrawal
            WHERE employee_id = #{employeeId} AND status = 'APPROVED'
        ) withdrawn ON 1=1
        LEFT JOIN (
            SELECT unit_price
            FROM stock_price
            WHERE time = (SELECT MAX(time) FROM stock_price)
        ) sp ON 1=1
    </select>

    <!-- 统计指定员工的股票记录总数（别名方法） -->
    <select id="countByEmployeeId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee_stock
        WHERE employee_id = #{employeeId}
    </select>

    <!-- 持股总览结果映射 -->
    <resultMap id="StockOverviewResultMap" type="org.example.company_management.dto.StockOverviewDTO">
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="unlocked_quantity" property="unlockedQuantity"/>
        <result column="locked_quantity" property="lockedQuantity"/>
        <result column="available_quantity" property="availableQuantity"/>
        <result column="withdrawn_quantity" property="withdrawnQuantity"/>
        <result column="withdrawn_total_value" property="withdrawnTotalValue"/>
    </resultMap>

    <!-- 查询持股总览数据（管理员专用） -->
    <select id="selectStockOverview" parameterType="map" resultMap="StockOverviewResultMap">
        SELECT
            e.employee_id,
            e.name as employee_name,
            d.department_id,
            d.department_name,
            COALESCE(SUM(CASE
                WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity
                ELSE 0
            END), 0) as unlocked_quantity,
            COALESCE(SUM(CASE
                WHEN es.unlock_time IS NOT NULL AND es.unlock_time > NOW() THEN es.quantity
                ELSE 0
            END), 0) as locked_quantity,
            COALESCE(SUM(CASE
                WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity
                ELSE 0
            END), 0) - COALESCE(withdrawn.withdrawn_quantity, 0) as available_quantity,
            COALESCE(withdrawn.withdrawn_quantity, 0) as withdrawn_quantity,
            COALESCE(withdrawn.withdrawn_total_value, 0) as withdrawn_total_value
        FROM employee e
        LEFT JOIN department d ON e.department_id = d.department_id
        LEFT JOIN employee_stock es ON e.employee_id = es.employee_id
        LEFT JOIN (
            SELECT
                employee_id,
                SUM(quantity) as withdrawn_quantity,
                SUM(total_amount) as withdrawn_total_value
            FROM stock_withdrawal
            WHERE status = 'APPROVED'
            GROUP BY employee_id
        ) withdrawn ON e.employee_id = withdrawn.employee_id
        WHERE 1=1
        <if test="employeeName != null and employeeName != ''">
            AND e.name LIKE #{employeeName}
        </if>
        <if test="departmentId != null">
            AND e.department_id = #{departmentId}
        </if>
        <if test="onlyWithStock != null and onlyWithStock == true">
            AND EXISTS (SELECT 1 FROM employee_stock es2 WHERE es2.employee_id = e.employee_id)
        </if>
        GROUP BY e.employee_id, e.name, d.department_id, d.department_name, withdrawn.withdrawn_quantity, withdrawn.withdrawn_total_value
        <if test="orderSql != null and orderSql != ''">
            ORDER BY ${orderSql}
        </if>
    </select>

</mapper>
