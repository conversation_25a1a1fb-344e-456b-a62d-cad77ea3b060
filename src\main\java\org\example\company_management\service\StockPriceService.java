package org.example.company_management.service;

import org.example.company_management.dto.StockPriceDTO;
import org.example.company_management.dto.StockPriceQueryDTO;
import org.example.company_management.utils.PageResult;

import java.time.LocalDate;
import java.util.List;

/**
 * 股票价格服务接口
 */
public interface StockPriceService {

    /**
     * 添加股票价格记录
     *
     * @param stockPriceDTO 股票价格DTO
     * @return 添加成功的股票价格DTO
     */
    StockPriceDTO addStockPrice(StockPriceDTO stockPriceDTO);

    /**
     * 根据ID删除股票价格记录
     *
     * @param id 股票价格ID
     * @return 是否删除成功
     */
    boolean deleteStockPrice(Long id);

    /**
     * 更新股票价格记录
     *
     * @param stockPriceDTO 股票价格DTO
     * @return 更新后的股票价格DTO
     */
    StockPriceDTO updateStockPrice(StockPriceDTO stockPriceDTO);

    // 删除：getStockPriceById - 前端不需要单独查看详情

    /**
     * 分页查询股票价格记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    PageResult<StockPriceDTO> getStockPriceByPage(StockPriceQueryDTO queryDTO);

    /**
     * 查询所有股票价格记录（不分页）
     *
     * @return 股票价格DTO列表
     */
    List<StockPriceDTO> getAllStockPrices();

    /**
     * 查询指定日期范围内的股票价格记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 股票价格DTO列表
     */
    List<StockPriceDTO> getStockPricesByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 查询最新的股票价格记录
     *
     * @return 最新的股票价格DTO
     */
    StockPriceDTO getLatestStockPrice();

    // 删除：getStockPricesByRemark - 通过分页查询的remark参数实现

    /**
     * 检查指定时间是否已存在股票价格记录
     * 保留：后端内部验证使用
     *
     * @param time      股票价格时间
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isTimeExists(LocalDate time, Long excludeId);

    // 删除：getStockPriceByTime - 功能重复，可用日期范围查询替代
}
