<template>
  <div class="meeting-management">
    <!-- 搜索工具栏 -->
    <div class="toolbar">
      <div class="search-box">
        <el-input
          v-model="searchForm.title"
          placeholder="搜索会议主题"
          clearable
          @keyup.enter="handleSearch"
          style="width: 200px;"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select
          v-model="searchForm.status"
          placeholder="选择状态"
          clearable
          style="width: 150px;"
        >
          <el-option label="未开始" value="NOT_STARTED" />
          <el-option label="进行中" value="IN_PROGRESS" />
          <el-option label="已结束" value="FINISHED" />
        </el-select>

        <el-date-picker
          v-model="searchForm.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="会议开始时间"
          end-placeholder="会议结束时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px;"
        />

        <el-button type="primary" @click="handleSearch" :icon="Search">
          搜索
        </el-button>
        <el-button @click="handleReset" :icon="Refresh">
          重置
        </el-button>
      </div>

      <div class="action-box">
        <el-button type="success" @click="handleAdd" :icon="Plus" class="add-btn">
          新增会议
        </el-button>
        <el-button type="primary" @click="handleLocationManagement" :icon="Setting">
          地点管理
        </el-button>
      </div>
    </div>

    <!-- 会议列表 -->
    <el-table
      :data="meetingList"
      v-loading="loading"
      stripe
      class="custom-table"
      style="width: 100%"
    >
        <el-table-column prop="title" label="会议主题" min-width="200" show-overflow-tooltip align="center" />
        <el-table-column label="会议时间" width="280" align="center">
          <template #default="{ row }">
            <div>
              <div><strong>开始：</strong>{{ formatDateTime(row.startTime) }}</div>
              <div v-if="row.endTime"><strong>结束：</strong>{{ formatDateTime(row.endTime) }}</div>
              <div v-else style="color: #909399; font-size: 12px;">结束时间未设定</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="会议地点" width="150" show-overflow-tooltip align="center" />
        <el-table-column label="会议负责人" width="120" align="center">
          <template #default="{ row }">
            {{ row.responsibleName || row.creatorName }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participantCount" label="参与人数" width="100" align="center" />
        <el-table-column prop="summaryCount" label="总结数" width="100" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="260" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button
                type="primary"
                size="small"
                :icon="View"
                @click="handleView(row)"
                class="operation-btn"
                title="查看"
              >
                查看
              </el-button>
              <el-button
                type="warning"
                size="small"
                :icon="Edit"
                @click="handleEdit(row)"
                class="operation-btn"
                title="编辑"
              >
                编辑
              </el-button>
              <!-- 状态操作已移除，现在由系统根据时间自动管理 -->
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="handleDelete(row)"
                class="operation-btn"
                title="删除"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 会议详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="会议详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentMeeting" class="meeting-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会议主题">
            <div class="text-overflow-field" :title="currentMeeting.title">
              {{ currentMeeting.title }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="会议状态">
            <el-tag :type="getStatusType(currentMeeting.status)">
              {{ getStatusText(currentMeeting.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            <div class="text-overflow-field" :title="formatDateTime(currentMeeting.startTime)">
              {{ formatDateTime(currentMeeting.startTime) }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            <div class="text-overflow-field" :title="currentMeeting.endTime ? formatDateTime(currentMeeting.endTime) : '未设定'">
              {{ currentMeeting.endTime ? formatDateTime(currentMeeting.endTime) : '未设定' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="会议地点">
            <div class="text-overflow-field" :title="currentMeeting.location || '无'">
              {{ currentMeeting.location || '无' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="会议负责人">
            <div class="text-overflow-field" :title="currentMeeting.responsibleName || currentMeeting.creatorName">
              {{ currentMeeting.responsibleName || currentMeeting.creatorName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="会议内容" :span="2">
            <div class="content-text-overflow" :title="currentMeeting.content || '无'">
              {{ currentMeeting.content || '无' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 参与者列表 -->
        <div class="participants-section">
          <h4>参与者列表</h4>
          <div class="participants-groups">
            <!-- 部门参与者 -->
            <div v-if="getDepartmentParticipants(currentMeeting.participants).length > 0" class="participant-group">
              <div class="group-header">
                <el-tag type="success" size="small">部门</el-tag>
                <span class="group-count">({{ getDepartmentParticipants(currentMeeting.participants).length }})</span>
              </div>
              <div class="participant-list">
                <el-tag
                  v-for="dept in getDepartmentParticipants(currentMeeting.participants)"
                  :key="dept.participantId"
                  type="success"
                  effect="plain"
                  size="small"
                  class="participant-tag text-overflow-tag"
                  :title="dept.participantName"
                >
                  {{ dept.participantName }}
                </el-tag>
              </div>
            </div>

            <!-- 员工参与者 -->
            <div v-if="getEmployeeParticipants(currentMeeting.participants).length > 0" class="participant-group">
              <div class="group-header">
                <el-tag type="primary" size="small">员工</el-tag>
                <span class="group-count">({{ getEmployeeParticipants(currentMeeting.participants).length }})</span>
              </div>
              <div class="participant-list">
                <el-tag
                  v-for="emp in getEmployeeParticipants(currentMeeting.participants)"
                  :key="emp.participantId"
                  type="primary"
                  effect="plain"
                  size="small"
                  class="participant-tag text-overflow-tag"
                  :title="emp.participantName"
                >
                  {{ emp.participantName }}
                </el-tag>
              </div>
            </div>

            <!-- 无参与者提示 -->
            <div v-if="!currentMeeting.participants || currentMeeting.participants.length === 0" class="no-participants">
              <el-empty description="暂无参与者" :image-size="60" />
            </div>
          </div>
        </div>

        <!-- 会议总结列表 -->
        <div class="summaries-section" v-if="currentMeeting.summaries && currentMeeting.summaries.length > 0">
          <h4>会议总结</h4>
          <div v-for="summary in currentMeeting.summaries" :key="summary.id" class="summary-item">
            <div class="summary-header">
              <span class="author text-overflow-field" :title="summary.employeeName">{{ summary.employeeName }}</span>
              <span class="department text-overflow-field" :title="summary.departmentName">{{ summary.departmentName }}</span>
              <span class="time text-overflow-field" :title="formatDateTime(summary.createTime)">{{ formatDateTime(summary.createTime) }}</span>
            </div>
            <div class="summary-content text-overflow-content" :title="summary.summaryContent">{{ summary.summaryContent }}</div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑会议对话框 -->
    <MeetingForm
      ref="meetingFormRef"
      v-model:visible="formDialogVisible"
      :meeting-data="editingMeeting"
      @success="handleFormSuccess"
    />

    <!-- 地点管理对话框 -->
    <el-dialog
      v-model="locationDialogVisible"
      title="会议地点管理"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="location-management">
        <!-- 地点管理工具栏 -->
        <div class="location-toolbar">
          <div class="location-search-box">
            <el-input
              v-model="locationSearchForm.name"
              placeholder="搜索地点名称"
              clearable
              @keyup.enter="handleLocationSearch"
              style="width: 200px;"
            >
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>

            <el-select
              v-model="locationSearchForm.status"
              placeholder="选择状态"
              clearable
              style="width: 120px;"
            >
              <el-option label="启用" value="ACTIVE" />
              <el-option label="禁用" value="INACTIVE" />
            </el-select>

            <el-button type="primary" @click="handleLocationSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="handleLocationReset" :icon="Refresh">
              重置
            </el-button>
          </div>

          <div class="location-action-box">
            <el-button type="success" @click="handleLocationAdd" :icon="Plus">
              新增地点
            </el-button>
          </div>
        </div>

        <!-- 地点列表 -->
        <el-table
          :data="locationList"
          v-loading="locationLoading"
          stripe
          class="custom-table"
          style="width: 100%"
        >
          <el-table-column prop="name" label="地点名称" min-width="150" show-overflow-tooltip align="center" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip align="center" />
          <el-table-column prop="capacity" label="容纳人数" width="100" align="center" />
          <el-table-column label="开放时间" width="150" align="center">
            <template #default="{ row }">
              <div style="font-size: 13px;">
                {{ formatTimeRange(row.openTime, row.closeTime) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开放日期" width="180" align="center">
            <template #default="{ row }">
              <div style="font-size: 13px;">
                {{ formatDateRange(row.availableStartDate, row.availableEndDate) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="可用天数" width="120" align="center">
            <template #default="{ row }">
              <div style="font-size: 12px;">
                {{ getAvailableDaysText(row.availableDays) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
                {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center">
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleLocationEdit(row)"
                  :icon="Edit"
                  class="operation-btn"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleLocationDelete(row)"
                  :icon="Delete"
                  class="operation-btn"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 地点分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="locationPagination.pageNum"
            v-model:page-size="locationPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="locationPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleLocationSizeChange"
            @current-change="handleLocationCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑地点对话框 -->
    <el-dialog
      v-model="locationFormDialogVisible"
      :title="editingLocation.id ? '编辑地点' : '新增地点'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="locationFormRef"
        :model="editingLocation"
        :rules="locationFormRules"
        label-width="100px"
      >
        <el-form-item label="地点名称" prop="name">
          <el-input v-model="editingLocation.name" placeholder="请输入地点名称" />
        </el-form-item>

        <el-form-item label="地点描述" prop="description">
          <el-input
            v-model="editingLocation.description"
            type="textarea"
            :rows="3"
            placeholder="请输入地点描述"
          />
        </el-form-item>

        <el-form-item label="容纳人数" prop="capacity">
          <el-input-number
            v-model="editingLocation.capacity"
            :min="1"
            :max="9999"
            placeholder="请输入容纳人数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="设施设备" prop="facilities">
          <el-input
            v-model="editingLocation.facilities"
            type="textarea"
            :rows="3"
            placeholder="请输入设施设备描述"
          />
        </el-form-item>

        <el-form-item label="开放日期" prop="dateRange">
          <div style="display: flex; align-items: center; gap: 10px; width: 100%;">
            <el-date-picker
              v-model="editingLocation.availableStartDate"
              type="date"
              placeholder="开始日期（可选）"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="flex: 1;"
              clearable
            />
            <span>至</span>
            <el-date-picker
              v-model="editingLocation.availableEndDate"
              type="date"
              placeholder="结束日期（可选）"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="flex: 1;"
              clearable
            />
          </div>
          <div class="form-help-text">
            留空表示无日期限制；仅设置开始日期表示从该日期开始永久开放；仅设置结束日期表示开放到该日期为止
          </div>
        </el-form-item>

        <el-form-item label="开放时间" prop="openTime">
          <div style="display: flex; align-items: center; gap: 10px; width: 100%;">
            <el-time-picker
              v-model="editingLocation.openTime"
              placeholder="开始时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              style="flex: 1;"
            />
            <span>至</span>
            <el-time-picker
              v-model="editingLocation.closeTime"
              placeholder="结束时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              style="flex: 1;"
            />
          </div>
        </el-form-item>

        <el-form-item label="可用天数" prop="availableDays">
          <el-checkbox-group v-model="selectedDays">
            <el-checkbox value="1">周一</el-checkbox>
            <el-checkbox value="2">周二</el-checkbox>
            <el-checkbox value="3">周三</el-checkbox>
            <el-checkbox value="4">周四</el-checkbox>
            <el-checkbox value="5">周五</el-checkbox>
            <el-checkbox value="6">周六</el-checkbox>
            <el-checkbox value="7">周日</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editingLocation.status">
            <el-radio value="ACTIVE">启用</el-radio>
            <el-radio value="INACTIVE">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleLocationSubmit" :loading="locationSubmitting">
            {{ editingLocation.id ? '更新' : '创建' }}
          </el-button>
          <el-button @click="locationFormDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 操作按钮容器样式 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  min-width: 240px;
}

/* 操作按钮统一样式 */
.operation-btn {
  padding: 4px 8px;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  margin: 0;
}

/* 响应式适配 - 小屏幕设备 */
@media (max-width: 768px) {
  .operation-buttons {
    gap: 2px;
    min-width: 220px;
  }

  .operation-btn {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* 参与者分组显示样式 */
.participants-section {
  margin-top: 20px;
}

.participants-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.participants-groups {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.participant-group {
  margin-bottom: 15px;
}

.participant-group:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.group-count {
  color: #909399;
  font-size: 12px;
}

.participant-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.participant-tag {
  margin: 0;
}

.no-participants {
  text-align: center;
  padding: 20px;
}
</style>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, View, Edit, Delete, Setting } from '@element-plus/icons-vue'
import { getMeetingPage, deleteMeeting, getMeetingById } from '@/api/meeting'
import { getMeetingLocationPage, createMeetingLocation, updateMeetingLocation, deleteMeetingLocation } from '@/api/meetingLocation'
import MeetingForm from '@/components/MeetingForm.vue'

// 响应式数据
const loading = ref(false)
const meetingList = ref([])
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)
const currentMeeting = ref(null)
const editingMeeting = ref(null)
const meetingFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  status: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 地点管理相关数据
const locationDialogVisible = ref(false)
const locationFormDialogVisible = ref(false)
const locationLoading = ref(false)
const locationSubmitting = ref(false)
const locationList = ref([])
const locationFormRef = ref(null)

// 地点搜索表单
const locationSearchForm = reactive({
  name: '',
  status: ''
})

// 地点分页信息
const locationPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 编辑中的地点
const editingLocation = ref({
  id: null,
  name: '',
  description: '',
  capacity: null,
  facilities: '',
  openTime: '',
  closeTime: '',
  availableDays: '',
  availableStartDate: null,
  availableEndDate: null,
  status: 'ACTIVE'
})

// 选中的可用天数
const selectedDays = ref([])

// 地点表单验证规则
const locationFormRules = {
  name: [
    { required: true, message: '请输入地点名称', trigger: 'blur' },
    { max: 100, message: '地点名称不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '地点描述不能超过500个字符', trigger: 'blur' }
  ],
  capacity: [
    { required: true, message: '请输入容纳人数', trigger: 'blur' },
    { type: 'number', min: 1, max: 9999, message: '容纳人数必须在1-9999之间', trigger: 'blur' }
  ],
  facilities: [
    { max: 1000, message: '设施设备描述不能超过1000个字符', trigger: 'blur' }
  ],
  openTime: [
    { required: true, message: '请选择开放开始时间', trigger: 'change' }
  ],
  closeTime: [
    { required: true, message: '请选择开放结束时间', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取会议列表
const getMeetingList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      title: searchForm.title,
      status: searchForm.status
    }

    // 处理时间范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    const response = await getMeetingPage(params)
    if (response.code === 200) {
      meetingList.value = response.data.list
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取会议列表失败')
    }
  } catch (error) {
    console.error('获取会议列表失败:', error)
    ElMessage.error('获取会议列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  getMeetingList()
}

// 重置
const handleReset = () => {
  searchForm.title = ''
  searchForm.status = ''
  searchForm.dateRange = []
  pagination.pageNum = 1
  getMeetingList()
}

// 新增会议
const handleAdd = () => {
  editingMeeting.value = null
  formDialogVisible.value = true
}

// 查看会议详情
const handleView = async (row) => {
  try {
    const response = await getMeetingById(row.id)
    if (response.code === 200) {
      currentMeeting.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取会议详情失败')
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    ElMessage.error('获取会议详情失败')
  }
}

// 编辑会议
const handleEdit = async (row) => {
  try {
    // 获取完整的会议详情，包括参与者信息
    const response = await getMeetingById(row.id)
    if (response.code === 200) {
      editingMeeting.value = response.data
      formDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取会议详情失败')
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    ElMessage.error('获取会议详情失败')
  }
}

// 删除会议
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会议"${row.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteMeeting(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      // 如果当前页只有一条数据且不是第一页，则回到上一页
      if (meetingList.value.length === 1 && pagination.pageNum > 1) {
        pagination.pageNum--
      }
      getMeetingList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会议失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态更新函数已移除，现在由系统根据时间自动管理

// 表单提交成功回调
const handleFormSuccess = () => {
  formDialogVisible.value = false
  editingMeeting.value = null // 清理编辑数据
  getMeetingList()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  getMeetingList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getMeetingList()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'NOT_STARTED': 'info',
    'IN_PROGRESS': 'warning',
    'FINISHED': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'NOT_STARTED': '未开始',
    'IN_PROGRESS': '进行中',
    'FINISHED': '已结束'
  }
  return statusMap[status] || '未知'
}

// 获取部门参与者
const getDepartmentParticipants = (participants) => {
  if (!participants) return []
  return participants.filter(p => p.participantType === 'DEPARTMENT')
}

// 获取员工参与者
const getEmployeeParticipants = (participants) => {
  if (!participants) return []
  return participants.filter(p => p.participantType === 'EMPLOYEE')
}

// 地点管理相关方法
const handleLocationManagement = () => {
  locationDialogVisible.value = true
  getMeetingLocationList()
}

const getMeetingLocationList = async () => {
  locationLoading.value = true
  try {
    const params = {
      pageNum: locationPagination.pageNum,
      pageSize: locationPagination.pageSize,
      name: locationSearchForm.name,
      status: locationSearchForm.status
    }

    const response = await getMeetingLocationPage(params)
    if (response.code === 200) {
      locationList.value = response.data.list
      locationPagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取地点列表失败')
    }
  } catch (error) {
    console.error('获取地点列表失败:', error)
    ElMessage.error('获取地点列表失败')
  } finally {
    locationLoading.value = false
  }
}

const handleLocationSearch = () => {
  locationPagination.pageNum = 1
  getMeetingLocationList()
}

const handleLocationReset = () => {
  locationSearchForm.name = ''
  locationSearchForm.status = ''
  locationPagination.pageNum = 1
  getMeetingLocationList()
}

const handleLocationAdd = () => {
  editingLocation.value = {
    id: null,
    name: '',
    description: '',
    capacity: null,
    facilities: '',
    openTime: '',
    closeTime: '',
    availableDays: '',
    availableStartDate: null,
    availableEndDate: null,
    status: 'ACTIVE'
  }
  selectedDays.value = []
  locationFormDialogVisible.value = true
}

const handleLocationEdit = (row) => {
  editingLocation.value = { ...row }
  // 解析可用天数
  if (row.availableDays) {
    selectedDays.value = row.availableDays.split(',')
  } else {
    selectedDays.value = []
  }
  locationFormDialogVisible.value = true
}

const handleLocationDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除地点"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteMeetingLocation(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getMeetingLocationList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地点失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleLocationSubmit = async () => {
  if (!locationFormRef.value) return

  try {
    await locationFormRef.value.validate()
  } catch (error) {
    return
  }

  // 验证时间设置
  if (editingLocation.value.openTime && editingLocation.value.closeTime) {
    if (editingLocation.value.openTime >= editingLocation.value.closeTime) {
      ElMessage.error('开放开始时间必须早于结束时间')
      return
    }
  }

  // 验证可用天数
  if (selectedDays.value.length === 0) {
    ElMessage.error('请至少选择一个可用天数')
    return
  }

  // 验证日期范围设置
  if (editingLocation.value.availableStartDate && editingLocation.value.availableEndDate) {
    const startDate = new Date(editingLocation.value.availableStartDate)
    const endDate = new Date(editingLocation.value.availableEndDate)
    if (startDate > endDate) {
      ElMessage.error('开放开始日期不能晚于结束日期')
      return
    }
  }

  locationSubmitting.value = true
  try {
    // 设置可用天数
    editingLocation.value.availableDays = selectedDays.value.join(',')

    let response
    if (editingLocation.value.id) {
      response = await updateMeetingLocation(editingLocation.value.id, editingLocation.value)
    } else {
      response = await createMeetingLocation(editingLocation.value)
    }

    if (response.code === 200) {
      ElMessage.success(editingLocation.value.id ? '更新成功' : '创建成功')
      locationFormDialogVisible.value = false
      getMeetingLocationList()

      // 刷新会议表单中的地点列表
      if (meetingFormRef.value && typeof meetingFormRef.value.refreshLocationList === 'function') {
        meetingFormRef.value.refreshLocationList()
      }
    } else {
      ElMessage.error(response.message || (editingLocation.value.id ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交地点信息失败:', error)
    ElMessage.error(editingLocation.value.id ? '更新失败' : '创建失败')
  } finally {
    locationSubmitting.value = false
  }
}

const handleLocationSizeChange = (size) => {
  locationPagination.pageSize = size
  locationPagination.pageNum = 1
  getMeetingLocationList()
}

const handleLocationCurrentChange = (page) => {
  locationPagination.pageNum = page
  getMeetingLocationList()
}

const getAvailableDaysText = (availableDays) => {
  if (!availableDays) return ''
  const dayMap = {
    '1': '一',
    '2': '二',
    '3': '三',
    '4': '四',
    '5': '五',
    '6': '六',
    '7': '日'
  }
  return availableDays.split(',').map(day => `周${dayMap[day]}`).join('、')
}

// 格式化时间范围
const formatTimeRange = (openTime, closeTime) => {
  if (!openTime && !closeTime) return '未设置'
  if (!openTime) return `至 ${closeTime}`
  if (!closeTime) return `${openTime} 起`
  return `${openTime} - ${closeTime}`
}

// 格式化日期范围
const formatDateRange = (startDate, endDate) => {
  if (!startDate && !endDate) return '无限制'
  if (!startDate) return `至 ${endDate}`
  if (!endDate) return `${startDate} 起`
  return `${startDate} 至 ${endDate}`
}

// 监听表单对话框关闭，清理编辑数据
watch(() => formDialogVisible.value, (visible) => {
  if (!visible) {
    // 对话框关闭时清理编辑数据
    editingMeeting.value = null
  }
})

// 组件挂载时获取数据
onMounted(() => {
  getMeetingList()
})
</script>

<style scoped>
.meeting-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 100px);
  position: relative;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.search-box {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: nowrap;
}

.search-box .el-input,
.search-box .el-select {
  width: 220px;
}

.search-box .el-form-item {
  margin-bottom: 0;
}

.action-box {
  display: flex;
  gap: 10px;
}

.add-btn {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background-color: #66b1ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: scale(1.05);
}

.el-button .el-icon {
  margin-right: 4px;
}

.pagination-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1;
}

:deep(.custom-table) {
  margin-bottom: 60px;
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden;
}

:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-table__row) {
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: #ecf5ff !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式统一 - 与股票管理模块保持一致 */

/* 会议详情对话框样式 */
.meeting-detail {
  max-height: 600px;
  overflow-y: auto;
}

/* ========== 文本溢出处理样式 ========== */

/* 基础文本溢出字段样式 */
.text-overflow-field {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: block;
  width: 100%;
  box-sizing: border-box;
  line-height: 1.5;
  min-height: 22px;
}

/* 会议内容文本溢出处理 */
.content-text-overflow {
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4; /* 最多显示4行 */
  line-clamp: 4; /* 标准属性 */
  -webkit-box-orient: vertical;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  width: 100%;
  box-sizing: border-box;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* 参与者标签文本溢出处理 */
.text-overflow-tag {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: top;
  margin: 3px 6px 3px 0;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 参与者标签内部文本优化 */
.text-overflow-tag :deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: block;
  line-height: 1.4;
  padding: 2px 4px;
}

/* 部门参与者标签特殊样式 */
.participant-list .text-overflow-tag[type="success"] {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #529b2e;
}

/* 员工参与者标签特殊样式 */
.participant-list .text-overflow-tag[type="primary"] {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #337ecc;
}

/* 总结内容文本溢出处理 */
.text-overflow-content {
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 最多显示3行 */
  line-clamp: 3; /* 标准属性 */
  -webkit-box-orient: vertical;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式文本溢出处理 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  :deep(.el-dialog__body) {
    padding: 15px;
    max-height: 60vh;
  }

  :deep(.el-descriptions-item__label) {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
    font-size: 13px;
  }

  .text-overflow-field {
    max-width: 200px;
    font-size: 13px;
  }

  .text-overflow-tag {
    max-width: 120px;
    font-size: 12px;
    margin: 2px 4px 2px 0;
  }

  .participant-list {
    padding: 6px;
    gap: 6px;
  }

  .group-count {
    font-size: 12px;
    padding: 1px 6px;
  }

  .content-text-overflow {
    -webkit-line-clamp: 3;
    line-clamp: 3;
    max-height: 75px;
    font-size: 13px;
  }

  .text-overflow-content {
    -webkit-line-clamp: 2;
    line-clamp: 2;
    max-height: 60px;
  }

  .summary-header .author {
    max-width: 80px;
    font-size: 13px;
  }

  .summary-header .department {
    max-width: 80px;
    font-size: 11px;
  }

  .summary-header .time {
    max-width: 120px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 98% !important;
    margin: 0 auto;
  }

  :deep(.el-dialog__body) {
    padding: 10px;
    max-height: 50vh;
  }

  :deep(.el-descriptions-item__label) {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    font-size: 12px;
  }

  .text-overflow-field {
    max-width: 150px;
    font-size: 12px;
  }

  .text-overflow-tag {
    max-width: 100px;
    font-size: 11px;
    margin: 2px 3px 2px 0;
    letter-spacing: 0.2px;
  }

  .participant-list {
    padding: 4px;
    gap: 4px;
    max-height: 80px;
  }

  .group-count {
    font-size: 11px;
    padding: 1px 4px;
  }

  .content-text-overflow {
    -webkit-line-clamp: 2;
    line-clamp: 2;
    max-height: 50px;
    font-size: 12px;
  }

  .text-overflow-content {
    -webkit-line-clamp: 2;
    line-clamp: 2;
    max-height: 50px;
    font-size: 12px;
  }

  .summary-header .author {
    max-width: 60px;
    font-size: 12px;
  }

  .summary-header .department {
    max-width: 60px;
    font-size: 10px;
  }

  .summary-header .time {
    max-width: 100px;
    font-size: 10px;
  }
}

/* Element Plus Dialog 内容区域优化 */
:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  word-wrap: break-word;
}

/* Element Plus 描述列表优化 */
:deep(.el-descriptions) {
  width: 100%;
  table-layout: fixed;
}

:deep(.el-descriptions__table) {
  width: 100%;
  table-layout: fixed;
}

:deep(.el-descriptions-item__label) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-word;
  vertical-align: top;
}

:deep(.el-descriptions-item__content) {
  overflow: hidden;
  word-break: break-word;
  vertical-align: top;
  max-width: calc(100% - 140px);
}

/* 针对跨列的内容项特殊处理 */
:deep(.el-descriptions-item__content[colspan="2"]) {
  max-width: 100%;
}

/* 确保描述列表内的文本溢出字段正确显示 */
:deep(.el-descriptions-item__content .text-overflow-field) {
  max-width: 100%;
  display: block;
}

:deep(.el-descriptions-item__content .content-text-overflow) {
  max-width: 100%;
  width: 100%;
}

/* 保持原有的content-text样式作为备用 */
.content-text {
  max-height: 100px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 特殊优化：确保长文本字段在 Dialog 中的显示效果 */
.meeting-detail .text-overflow-field:hover {
  background-color: #f8f9fa;
  cursor: help;
  transition: background-color 0.2s ease;
}

.meeting-detail .content-text-overflow:hover {
  background-color: #f0f9ff;
  cursor: help;
  transition: background-color 0.2s ease;
}

/* 确保 Dialog 标题也能处理长文本 */
:deep(.el-dialog__title) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 50px);
}

/* 优化 Dialog 整体布局 */
:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

:deep(.el-dialog__header) {
  flex-shrink: 0;
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
  padding: 10px 20px 20px 20px;
}

/* 确保描述列表在小屏幕上的布局 */
:deep(.el-descriptions--small .el-descriptions-item__label) {
  width: 100px !important;
}

:deep(.el-descriptions--small .el-descriptions-item__content) {
  max-width: calc(100% - 120px) !important;
}

/* 无参与者状态的优化 */
.no-participants {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.no-participants :deep(.el-empty__description) {
  margin-top: 10px;
}

.participants-section,
.summaries-section {
  margin-top: 20px;
  width: 100%;
  box-sizing: border-box;
}

.participants-section h4,
.summaries-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 参与者组样式优化 */
.participant-group {
  margin-bottom: 15px;
  width: 100%;
  box-sizing: border-box;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  overflow: hidden;
  padding: 4px 0;
}

.group-count {
  margin-left: 8px;
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.participant-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #fafbfc;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  line-height: 1.6;
}

.summary-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
  width: 100%;
  overflow: hidden;
}

.summary-header .author {
  font-weight: bold;
  color: #303133;
  margin-right: 10px;
  flex-shrink: 0;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.summary-header .department {
  background: #f0f9ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.summary-header .time {
  margin-left: auto;
  flex-shrink: 0;
  font-size: 12px;
  color: #909399;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.summary-content {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  width: 100%;
  box-sizing: border-box;
}

/* 地点管理相关样式 */
.location-management {
  padding: 0;
}

.location-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.location-search-box {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: nowrap;
}

.location-action-box {
  display: flex;
  gap: 10px;
}

/* 地点表单样式 */
.location-form .el-form-item {
  margin-bottom: 20px;
}

.location-form .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.location-form .el-checkbox {
  margin-right: 0;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单帮助文本样式 */
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
