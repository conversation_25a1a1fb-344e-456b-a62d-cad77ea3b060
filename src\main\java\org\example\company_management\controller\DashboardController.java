package org.example.company_management.controller;

import org.example.company_management.dto.AchievementDTO;
import org.example.company_management.service.DashboardService;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 仪表盘控制器
 * <p>
 * 提供系统首页的数据统计信息
 * </p>
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    private final DashboardService dashboardService;

    public DashboardController(DashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    /**
     * 获取首页统计数据
     * <p>
     * 包括员工总数、在职/离职数量、部门数量、职位数量、总业绩等信息
     * </p>
     *
     * @return 统计数据集合
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        Map<String, Object> stats = dashboardService.getDashboardStats();
        return Result.success(stats);
    }

    /**
     * 获取当前登录员工的成就列表
     * <p>
     * 包括四个成就指标：第一次获得新客户、第一次获得提成、第一次达到3万业绩、还清薪资比
     * 使用ThreadLocal获取当前登录员工信息进行数据过滤
     * </p>
     *
     * @return 成就列表
     */
    @GetMapping("/achievements")
    public Result<List<AchievementDTO>> getAchievements() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            // 获取员工成就列表
            List<AchievementDTO> achievements = dashboardService.getEmployeeAchievements(employeeId);

            return Result.success(achievements);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取成就信息失败：" + e.getMessage());
        }
    }
}