package org.example.company_management.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.example.company_management.dto.DepartmentPerformanceStatsDTO;
import org.example.company_management.dto.PerformanceImportDTO;
import org.example.company_management.entity.Employee;
import org.example.company_management.entity.Performance;
import org.example.company_management.entity.Salary;
import org.example.company_management.mapper.*;
import org.example.company_management.service.PerformanceService;
import org.example.company_management.utils.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PerformanceServiceImpl implements PerformanceService {

    @Autowired
    private PerformanceMapper performanceMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private SalaryMapper salaryMapper;

    @Autowired
    private PettyCashMapper pettyCashMapper;

    @Autowired
    private Validator validator;

    @Override
    public Page<Performance> getPerformancePage(Integer page, Integer size, Integer employeeId,
                                                String employeeName, Integer departmentId, String yearMonth) {
        try {
            page = (page == null || page < 1) ? 1 : page;
            size = (size == null || size < 1) ? 10 : size;

            Page<Performance> pageResult = PageHelper.startPage(page, size);
            List<Performance> performances = performanceMapper.selectByCondition(employeeId, employeeName, departmentId, yearMonth);
            // `performances` is now the list held by pageResult.getResult(), so we iterate that directly.

            for (Performance perf : pageResult.getResult()) {
                if (perf.getEmployeeId() == null) continue;

                Integer empDeptId = perf.getEmployeeDepartmentId();

                // Total Petty Cash is now populated by selectByCondition via BaseResultMap
                // Ensure it's not null if no records exist
                if (perf.getTotalPettyCash() == null) {
                    perf.setTotalPettyCash(BigDecimal.ZERO);
                }

                // Total Salary is also from selectByCondition
                if (perf.getTotalSalary() == null) {
                    perf.setTotalSalary(BigDecimal.ZERO);
                }

                BigDecimal totalEmpOtherExp = performanceMapper.sumTotalEmployeeOtherExpensesByEmployeeIdAndMonth(perf.getEmployeeId(), perf.getDate());
                perf.setTotalEmployeeOtherExpenses(totalEmpOtherExp != null ? totalEmpOtherExp : BigDecimal.ZERO);

                BigDecimal avgDeptExpense = BigDecimal.ZERO;
                if (empDeptId != null) {
                    BigDecimal totalDeptExp = performanceMapper.sumTotalDepartmentExpensesByDepartmentIdAndMonth(empDeptId, perf.getDate());
                    Integer activeEmpCount = employeeMapper.countActiveEmployeesByDepartmentId(empDeptId);
                    if (activeEmpCount != null && activeEmpCount > 0 && totalDeptExp != null) {
                        avgDeptExpense = totalDeptExp.divide(BigDecimal.valueOf(activeEmpCount), 2, RoundingMode.HALF_UP);
                    }
                }
                perf.setAverageDepartmentExpense(avgDeptExpense);

                BigDecimal estPerformance = perf.getEstimatedPerformance() != null ? perf.getEstimatedPerformance() : BigDecimal.ZERO;
                BigDecimal actualPerformance = perf.getActualPerformance() != null ? perf.getActualPerformance() : BigDecimal.ZERO;
                // totalPettyCash and totalSalary are already on perf and checked for null

                BigDecimal estProfitLoss = estPerformance
                        .subtract(perf.getTotalPettyCash())
                        .subtract(perf.getTotalSalary())
                        .subtract(perf.getAverageDepartmentExpense())
                        .subtract(perf.getTotalEmployeeOtherExpenses());
                perf.setEstimatedMonthlyProfitLoss(estProfitLoss);

                BigDecimal actualProfitLoss = actualPerformance
                        .subtract(perf.getTotalPettyCash())
                        .subtract(perf.getTotalSalary())
                        .subtract(perf.getAverageDepartmentExpense())
                        .subtract(perf.getTotalEmployeeOtherExpenses());
                perf.setActualMonthlyProfitLoss(actualProfitLoss);

                //  计算月度能力值：预估业绩 - ((基本工资 + 绩效奖金)×3)
                BigDecimal monthlyCapabilityValue = calculateMonthlyCapabilityValue(perf.getEmployeeId(), perf.getDate(), estPerformance);
                perf.setMonthlyCapabilityValue(monthlyCapabilityValue);
            }

            return pageResult;
        } catch (Exception e) {
            e.printStackTrace();
            return new Page<>();
        }
    }

    @Override
    public Performance getById(Integer id) {
        return performanceMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional
    public boolean addPerformance(Performance performance) {
        // 检查是否已存在同一员工同一时间的记录
        Performance existing = performanceMapper.selectByEmployeeIdAndDate(
                performance.getEmployeeId(),
                performance.getDate()
        );

        if (existing != null) {
            return false; // 已存在记录，不允许添加
        }

        return performanceMapper.insert(performance) > 0;
    }

    @Override
    @Transactional
    public boolean updatePerformance(Performance performance) {
        // 检查是否存在同一员工同一时间的其他记录
        Performance existing = performanceMapper.selectByEmployeeIdAndDate(
                performance.getEmployeeId(),
                performance.getDate()
        );

        if (existing != null && !existing.getId().equals(performance.getId())) {
            return false; // 已存在其他记录，不允许更新
        }

        return performanceMapper.updateByPrimaryKey(performance) > 0;
    }

    @Override
    @Transactional
    public boolean deletePerformance(Integer id) {
        return performanceMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    @Transactional
    public boolean batchDeletePerformance(List<Integer> ids) {
        for (Integer id : ids) {
            if (performanceMapper.deleteByPrimaryKey(id) <= 0) {
                return false;
            }
        }
        return true;
    }

    private List<String> generateMonthsInRange(String startDateStr, String endDateStr) {
        List<String> months = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        YearMonth startMonth;
        YearMonth endMonth;

        try {
            startMonth = YearMonth.parse(startDateStr, formatter);
            endMonth = YearMonth.parse(endDateStr, formatter);
        } catch (Exception e) {
            // Handle invalid date format, perhaps default to a single month or error
            // For now, if parsing fails, assume single month from startDateStr or current month
            try {
                YearMonth currentMonth = YearMonth.parse(startDateStr, formatter);
                months.add(currentMonth.format(formatter));
            } catch (Exception ex) {
                // Fallback to current system month if everything fails
                months.add(YearMonth.now().format(formatter));
            }
            return months;
        }


        if (startMonth.isAfter(endMonth)) { // Swap if start is after end
            YearMonth temp = startMonth;
            startMonth = endMonth;
            endMonth = temp;
        }

        while (!startMonth.isAfter(endMonth)) {
            months.add(startMonth.format(formatter));
            startMonth = startMonth.plusMonths(1);
        }
        return months;
    }

    @Override
    public Map<String, Object> getMultiDepartmentPerformances(List<Integer> departmentIds, Integer page, Integer size, String startDate, String endDate, String employeeName) {
        Map<String, Object> result = new HashMap<>();
        page = (page == null || page < 1) ? 1 : page;
        size = (size == null || size < 1) ? 10 : size;

        if (departmentIds == null || departmentIds.isEmpty()) {
            result.put("total", 0);
            result.put("records", new ArrayList<>());
            return result;
        }

        List<String> monthsToProcess;
        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            monthsToProcess = generateMonthsInRange(startDate, endDate);
        } else if (startDate != null && !startDate.isEmpty()) {
            monthsToProcess = generateMonthsInRange(startDate, startDate); // Process as a single month range
        } else if (endDate != null && !endDate.isEmpty()) {
            monthsToProcess = generateMonthsInRange(endDate, endDate); // Process as a single month range
        } else {
            monthsToProcess = generateMonthsInRange(YearMonth.now().format(DateTimeFormatter.ofPattern("yyyy-MM")), YearMonth.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }

        if (monthsToProcess.isEmpty()) {
            monthsToProcess.add(YearMonth.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }

        List<Employee> employeesInSelectedDepartments = employeeMapper.selectEmployeesWithDetailsByDepartmentIds(departmentIds);
        List<Performance> comprehensivePerformanceList = new ArrayList<>();

        // Cache for department-level data to reduce DB calls
        Map<String, BigDecimal> monthlyDeptExpensesCache = new HashMap<>();
        Map<Integer, Long> activeEmployeeCountsCache = new HashMap<>();

        for (Integer deptId : departmentIds) {
            long count = employeesInSelectedDepartments.stream()
                    .filter(e -> e.getDepartmentId() != null && e.getDepartmentId().equals(deptId) && "Active".equals(e.getStatus()))
                    .count();
            activeEmployeeCountsCache.put(deptId, count);
        }

        for (String month : monthsToProcess) {
            for (Integer deptId : departmentIds) {
                monthlyDeptExpensesCache.computeIfAbsent(deptId + "_" + month,
                        k -> performanceMapper.sumTotalDepartmentExpensesByDepartmentIdAndMonth(deptId, month));
            }
        }

        for (Employee employee : employeesInSelectedDepartments) {
            if (employee.getDepartmentId() == null) continue; // Skip if employee has no department

            for (String month : monthsToProcess) {
                Performance listItem = new Performance();
                listItem.setEmployeeId(employee.getEmployeeId());
                listItem.setEmployeeName(employee.getName());
                listItem.setDepartment(employee.getDepartmentName());
                listItem.setPosition(employee.getPositionName());
                listItem.setDate(month);

                Performance actualPerfData = performanceMapper.selectPerformanceDataByEmployeeIdAndDate(employee.getEmployeeId(), month);
                Optional<Salary> salaryDataOptional = salaryMapper.findByEmployeeIdAndDate(employee.getEmployeeId(), month);
                Salary salaryData = salaryDataOptional.orElse(null);
                BigDecimal pettyCashSum = pettyCashMapper.sumApprovedAmountByEmployeeIdAndDate(employee.getEmployeeId(), month);

                listItem.setEstimatedPerformance(actualPerfData != null && actualPerfData.getEstimatedPerformance() != null ? actualPerfData.getEstimatedPerformance() : BigDecimal.ZERO);
                listItem.setActualPerformance(actualPerfData != null && actualPerfData.getActualPerformance() != null ? actualPerfData.getActualPerformance() : BigDecimal.ZERO);
                listItem.setTotalSalary(salaryData != null && salaryData.getTotalSalary() != null ? salaryData.getTotalSalary() : BigDecimal.ZERO);
                listItem.setTotalPettyCash(pettyCashSum != null ? pettyCashSum : BigDecimal.ZERO);

                Integer currentDepartmentId = employee.getDepartmentId();
                long activeEmployeeCountInDept = activeEmployeeCountsCache.getOrDefault(currentDepartmentId, 0L);
                BigDecimal totalDeptExpenseForMonth = monthlyDeptExpensesCache.getOrDefault(currentDepartmentId + "_" + month, BigDecimal.ZERO);

                BigDecimal averageDeptExpense = BigDecimal.ZERO;
                if (activeEmployeeCountInDept > 0 && totalDeptExpenseForMonth != null) {
                    averageDeptExpense = totalDeptExpenseForMonth.divide(BigDecimal.valueOf(activeEmployeeCountInDept), 2, RoundingMode.HALF_UP);
                }
                listItem.setAverageDepartmentExpense(averageDeptExpense);

                BigDecimal totalEmpOtherExpenses = performanceMapper.sumTotalEmployeeOtherExpensesByEmployeeIdAndMonth(employee.getEmployeeId(), month);
                listItem.setTotalEmployeeOtherExpenses(totalEmpOtherExpenses != null ? totalEmpOtherExpenses : BigDecimal.ZERO);

                BigDecimal estPerformance = listItem.getEstimatedPerformance();
                BigDecimal actualPerformance = listItem.getActualPerformance();
                BigDecimal totalPettyCash = listItem.getTotalPettyCash();
                BigDecimal totalSalary = listItem.getTotalSalary();
                // averageDepartmentExpense is already calculated
                // totalEmployeeOtherExpenses is listItem.getTotalEmployeeOtherExpenses()

                BigDecimal estProfitLoss = estPerformance
                        .subtract(totalPettyCash)
                        .subtract(totalSalary)
                        .subtract(averageDeptExpense)
                        .subtract(listItem.getTotalEmployeeOtherExpenses());
                listItem.setEstimatedMonthlyProfitLoss(estProfitLoss);

                BigDecimal actualProfitLoss = actualPerformance
                        .subtract(totalPettyCash)
                        .subtract(totalSalary)
                        .subtract(averageDeptExpense)
                        .subtract(listItem.getTotalEmployeeOtherExpenses());
                listItem.setActualMonthlyProfitLoss(actualProfitLoss);

                // 计算月度能力值：预估业绩 - ((基本工资 + 绩效奖金)×3)
                BigDecimal monthlyCapabilityValue = calculateMonthlyCapabilityValue(employee.getEmployeeId(), month, estPerformance);
                listItem.setMonthlyCapabilityValue(monthlyCapabilityValue);

                listItem.setId(null);
                comprehensivePerformanceList.add(listItem);
            }
        }


        List<Performance> filteredList;
        if (employeeName != null && !employeeName.trim().isEmpty()) {
            String searchNameLower = employeeName.trim().toLowerCase();
            filteredList = comprehensivePerformanceList.stream()
                    .filter(p -> p.getEmployeeName() != null && p.getEmployeeName().toLowerCase().contains(searchNameLower))
                    .collect(Collectors.toList());
        } else {
            filteredList = comprehensivePerformanceList;
        }

        // Sort by date (desc) and then employeeId (asc) for consistent view
        filteredList.sort(Comparator.comparing(Performance::getDate, Comparator.reverseOrder())
                .thenComparing(Performance::getEmployeeId, Comparator.nullsLast(Comparator.naturalOrder())));


        // Manual Pagination
        int total = filteredList.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Performance> paginatedList = (start >= total) ? new ArrayList<>() : filteredList.subList(start, end);

        result.put("records", paginatedList);
        result.put("total", total);
        return result;
    }

    @Override
    public List<DepartmentPerformanceStatsDTO> getPerformanceStatsByDepartments(
            List<Integer> departmentIds, String startDate, String endDate) {
        try {
            // 处理日期参数
            String dateParam = null;
            if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
                dateParam = "range_" + startDate + "_" + endDate;
            } else if (startDate != null && !startDate.isEmpty()) {
                dateParam = startDate;
            } else if (endDate != null && !endDate.isEmpty()) {
                dateParam = endDate;
            }

            // 调用Mapper查询
            List<DepartmentPerformanceStatsDTO> results = performanceMapper.selectDepartmentStats(departmentIds, dateParam);

            // 如果查询结果为null，返回空列表
            return results != null ? results : new ArrayList<>();
        } catch (Exception e) {
            throw new RuntimeException("获取部门业绩统计数据失败", e);
        }
    }

    @Override
    public ExcelUtil.ImportResult<PerformanceImportDTO> importPerformancesFromExcel(MultipartFile file) {
        ExcelUtil.RowProcessor<PerformanceImportDTO> performanceRowProcessor = (rowIndex, dto) -> {
            // 1. JSR 303/380 Bean Validation
            Set<ConstraintViolation<PerformanceImportDTO>> violations = validator.validate(dto);
            if (!violations.isEmpty()) {
                StringBuilder errorMessages = new StringBuilder();
                for (ConstraintViolation<PerformanceImportDTO> violation : violations) {
                    errorMessages.append(violation.getMessage()).append("; ");
                }
                return "第" + rowIndex + "行：" + errorMessages.toString().trim();
            }

            // 2. 根据身份证查询员工
            Employee employee = employeeMapper.selectByIdCard(dto.getIdCard());
            if (employee == null) {
                return "第" + rowIndex + "行：身份证号码 '" + dto.getIdCard() + "' 未找到对应员工";
            }

            // 3. 组装Performance实体
            Performance performance = new Performance();
            performance.setEmployeeId(employee.getEmployeeId());
            performance.setDate(dto.getDate());
            performance.setEstimatedPerformance(dto.getEstimatedPerformance() != null ? dto.getEstimatedPerformance() : BigDecimal.ZERO);
            performance.setActualPerformance(dto.getActualPerformance() != null ? dto.getActualPerformance() : BigDecimal.ZERO);

            // 4. 检查是否已存在记录 (或者直接尝试插入，由数据库唯一约束处理，但那样错误信息不够友好)
            Performance existing = performanceMapper.selectByEmployeeIdAndDate(performance.getEmployeeId(), performance.getDate());
            if (existing != null) {
                // 策略：本次是跳过并提示
                return "第" + rowIndex + "行：员工 '" + employee.getName() + "' (身份证: " + dto.getIdCard() +
                        ") 在 '" + dto.getDate() + "' 已存在业绩记录，本次跳过";
            }

            // 5. 插入新纪录
            try {
                int inserted = performanceMapper.insert(performance);
                if (inserted > 0) {
                    return null; // 成功
                } else {
                    return "第" + rowIndex + "行：数据库插入失败，影响行数为0";
                }
            } catch (DataIntegrityViolationException e) {
                // Log e for details
                return "第" + rowIndex + "行：数据插入冲突 (可能原因：唯一键冲突，但查重逻辑应已覆盖)。详细错误: " + e.getMessage();
            } catch (Exception e) {
                // Log e for details
                return "第" + rowIndex + "行：保存业绩时发生未知数据库错误: " + e.getMessage();
            }
        };

        try {
            return ExcelUtil.readExcel(file.getInputStream(), PerformanceImportDTO.class, performanceRowProcessor);
        } catch (IOException e) {
            // Log e for details
            ExcelUtil.ImportResult<PerformanceImportDTO> errorResult = new ExcelUtil.ImportResult<>();
            errorResult.getGeneralErrors().add("读取Excel文件IO异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 计算月度能力值
     * 公式：预估业绩 - ((基本工资 + 绩效奖金)×3)
     *
     * @param employeeId           员工ID
     * @param date                 年月（格式：yyyy-MM）
     * @param estimatedPerformance 预估业绩
     * @return 月度能力值
     */
    private BigDecimal calculateMonthlyCapabilityValue(Integer employeeId, String date, BigDecimal estimatedPerformance) {
        try {
            // 获取该员工该月份的工资数据
            Optional<Salary> salaryOptional = salaryMapper.findByEmployeeIdAndDate(employeeId, date);

            BigDecimal basicSalary = BigDecimal.ZERO;
            BigDecimal performanceBonus = BigDecimal.ZERO;

            if (salaryOptional.isPresent()) {
                Salary salary = salaryOptional.get();
                basicSalary = salary.getBasicSalary() != null ? salary.getBasicSalary() : BigDecimal.ZERO;
                performanceBonus = salary.getPerformanceBonus() != null ? salary.getPerformanceBonus() : BigDecimal.ZERO;
            }

            // 确保预估业绩不为null
            BigDecimal estPerformance = estimatedPerformance != null ? estimatedPerformance : BigDecimal.ZERO;

            // 计算能力值：预估业绩 - ((基本工资 + 绩效奖金)×3)
            BigDecimal totalSalary = basicSalary.add(performanceBonus);
            BigDecimal totalCost = totalSalary.multiply(BigDecimal.valueOf(3));
            BigDecimal capabilityValue = estPerformance.subtract(totalCost);

            return capabilityValue;
        } catch (Exception e) {
            // 如果计算失败，返回0
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<String, Object> getTotalCapabilityValue(Integer employeeId, String endDate) {
        try {
            // 获取该员工的所有业绩记录（用于计算累计能力值）
            List<Performance> allPerformances = performanceMapper.selectByCondition(employeeId, null, null, endDate);

            BigDecimal totalCapabilityValue = BigDecimal.ZERO;
            int recordCount = 0;
            String earliestDate = null;
            String latestDate = null;

            for (Performance perf : allPerformances) {
                // 计算每月的能力值
                BigDecimal estPerformance = perf.getEstimatedPerformance() != null ? perf.getEstimatedPerformance() : BigDecimal.ZERO;
                BigDecimal monthlyCapabilityValue = calculateMonthlyCapabilityValue(perf.getEmployeeId(), perf.getDate(), estPerformance);

                totalCapabilityValue = totalCapabilityValue.add(monthlyCapabilityValue);
                recordCount++;

                // 记录日期范围
                if (earliestDate == null || perf.getDate().compareTo(earliestDate) < 0) {
                    earliestDate = perf.getDate();
                }
                if (latestDate == null || perf.getDate().compareTo(latestDate) > 0) {
                    latestDate = perf.getDate();
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCapabilityValue", totalCapabilityValue);
            result.put("recordCount", recordCount);
            result.put("earliestDate", earliestDate);
            result.put("latestDate", latestDate);
            result.put("endDate", endDate);
            result.put("calculationFormula", "预估业绩 - ((基本工资 + 绩效奖金)×3)");
            result.put("dataSource", "基于数据库层面的精确计算");
            result.put("statisticsScope", endDate != null ? "截止到" + endDate + "的累计数据" : "全部历史数据");

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 返回默认值
            Map<String, Object> result = new HashMap<>();
            result.put("totalCapabilityValue", BigDecimal.ZERO);
            result.put("recordCount", 0);
            result.put("error", "计算累计能力值时发生错误：" + e.getMessage());
            return result;
        }
    }
}

