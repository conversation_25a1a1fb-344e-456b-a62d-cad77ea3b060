package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 成就数据传输对象
 * 用于前端展示员工成就信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementDTO {
    
    /**
     * 成就名称
     */
    private String name;
    
    /**
     * 成就描述
     */
    private String description;
    
    /**
     * 是否已达成
     */
    private Boolean achieved;
    
    /**
     * 达成时间（如果已达成）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime achievedDate;
    
    /**
     * 达成时间的字符串格式（用于前端显示）
     */
    private String achievedDateStr;
    
    /**
     * 成就类型标识
     */
    private String type;
    
    /**
     * 额外信息（如业绩金额、客户数量等）
     */
    private String extraInfo;
    
    /**
     * 构造方法 - 用于未达成的成就
     */
    public AchievementDTO(String name, String description, String type) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.achieved = false;
        this.achievedDate = null;
        this.achievedDateStr = null;
        this.extraInfo = null;
    }
    
    /**
     * 构造方法 - 用于已达成的成就
     */
    public AchievementDTO(String name, String description, String type, LocalDateTime achievedDate, String extraInfo) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.achieved = true;
        this.achievedDate = achievedDate;
        this.achievedDateStr = achievedDate != null ? achievedDate.toString() : null;
        this.extraInfo = extraInfo;
    }
}
