package org.example.company_management.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.example.company_management.entity.Employee;

import java.util.List;

/**
 * 员工列表查询结果DTO
 * <p>
 * 用于员工列表接口的返回数据，支持分页和搜索功能。
 * 包含员工列表、总数、分页信息等。
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeListResult {
    
    /**
     * 员工列表
     */
    private List<Employee> employees;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 是否还有更多数据
     */
    private boolean hasMore;
    
    /**
     * 当前页码
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 构造方法 - 用于创建分页结果
     * 
     * @param employees 员工列表
     * @param total 总记录数
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     */
    public EmployeeListResult(List<Employee> employees, long total, int currentPage, int pageSize) {
        this.employees = employees;
        this.total = total;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.hasMore = (long) currentPage * pageSize < total;
    }
}
