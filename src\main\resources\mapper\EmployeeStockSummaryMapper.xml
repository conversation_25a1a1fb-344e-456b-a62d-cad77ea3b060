<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.company_management.mapper.EmployeeStockSummaryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="org.example.company_management.entity.EmployeeStockSummary">
        <id column="employee_id" property="employeeId" jdbcType="INTEGER"/>
        <result column="total_quantity" property="totalQuantity" jdbcType="INTEGER"/>
        <result column="unlocked_quantity" property="unlockedQuantity" jdbcType="INTEGER"/>
        <result column="withdrawn_quantity" property="withdrawnQuantity" jdbcType="INTEGER"/>
        <result column="available_quantity" property="availableQuantity" jdbcType="INTEGER"/>
        <result column="last_calculated_time" property="lastCalculatedTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 包含关联信息的结果映射 -->
    <resultMap id="DetailResultMap" type="org.example.company_management.entity.EmployeeStockSummary" extends="BaseResultMap">
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        employee_id, total_quantity, unlocked_quantity, withdrawn_quantity, available_quantity,
        last_calculated_time, create_time, update_time
    </sql>

    <!-- 包含关联信息的字段 -->
    <sql id="Detail_Column_List">
        ess.employee_id, ess.total_quantity, ess.unlocked_quantity, ess.withdrawn_quantity, ess.available_quantity,
        ess.last_calculated_time, ess.create_time, ess.update_time,
        e.name as employee_name, d.department_name
    </sql>

    <!-- 根据员工ID查询 -->
    <select id="selectByEmployeeId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM employee_stock_summary
        WHERE employee_id = #{employeeId}
    </select>

    <!-- 根据员工ID查询（包含关联信息） -->
    <select id="selectByEmployeeIdWithDetails" parameterType="java.lang.Integer" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM employee_stock_summary ess
        LEFT JOIN employee e ON ess.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        WHERE ess.employee_id = #{employeeId}
    </select>

    <!-- 插入或更新 -->
    <insert id="insertOrUpdate" parameterType="org.example.company_management.entity.EmployeeStockSummary">
        INSERT INTO employee_stock_summary (
            employee_id, total_quantity, unlocked_quantity, withdrawn_quantity, last_calculated_time
        ) VALUES (
            #{employeeId}, #{totalQuantity}, #{unlockedQuantity}, #{withdrawnQuantity}, #{lastCalculatedTime}
        ) ON DUPLICATE KEY UPDATE
            total_quantity = VALUES(total_quantity),
            unlocked_quantity = VALUES(unlocked_quantity),
            withdrawn_quantity = VALUES(withdrawn_quantity),
            last_calculated_time = VALUES(last_calculated_time)
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="org.example.company_management.entity.EmployeeStockSummary">
        UPDATE employee_stock_summary
        <set>
            <if test="totalQuantity != null">total_quantity = #{totalQuantity},</if>
            <if test="unlockedQuantity != null">unlocked_quantity = #{unlockedQuantity},</if>
            <if test="withdrawnQuantity != null">withdrawn_quantity = #{withdrawnQuantity},</if>
            <if test="lastCalculatedTime != null">last_calculated_time = #{lastCalculatedTime},</if>
            update_time = NOW()
        </set>
        WHERE employee_id = #{employeeId}
    </update>

    <!-- 删除 -->
    <delete id="deleteByEmployeeId" parameterType="java.lang.Integer">
        DELETE FROM employee_stock_summary WHERE employee_id = #{employeeId}
    </delete>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM employee_stock_summary
        ORDER BY total_quantity DESC
    </select>

    <!-- 查询所有（包含关联信息） -->
    <select id="selectAllWithDetails" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM employee_stock_summary ess
        LEFT JOIN employee e ON ess.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        ORDER BY ess.total_quantity DESC
    </select>

    <!-- 根据部门ID查询 -->
    <select id="selectByDepartmentId" parameterType="java.lang.Integer" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM employee_stock_summary ess
        LEFT JOIN employee e ON ess.employee_id = e.employee_id
        LEFT JOIN department d ON e.department_id = d.department_id
        WHERE d.department_id = #{departmentId}
        ORDER BY ess.total_quantity DESC
    </select>

    <!-- 重新计算指定员工的股票汇总数据 -->
    <update id="recalculateByEmployeeId" parameterType="java.lang.Integer">
        INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, withdrawn_quantity, last_calculated_time)
        SELECT 
            #{employeeId} as employee_id,
            COALESCE(stock_data.total_quantity, 0) as total_quantity,
            COALESCE(stock_data.unlocked_quantity, 0) as unlocked_quantity,
            COALESCE(withdrawal_data.withdrawn_quantity, 0) as withdrawn_quantity,
            NOW() as last_calculated_time
        FROM (
            SELECT 
                SUM(quantity) as total_quantity,
                SUM(CASE WHEN unlock_time IS NULL OR unlock_time &lt;= NOW() THEN quantity ELSE 0 END) as unlocked_quantity
            FROM employee_stock 
            WHERE employee_id = #{employeeId}
        ) stock_data
        LEFT JOIN (
            SELECT SUM(quantity) as withdrawn_quantity
            FROM stock_withdrawal 
            WHERE employee_id = #{employeeId} AND status = 'APPROVED'
        ) withdrawal_data ON 1=1
        ON DUPLICATE KEY UPDATE
            total_quantity = VALUES(total_quantity),
            unlocked_quantity = VALUES(unlocked_quantity),
            withdrawn_quantity = VALUES(withdrawn_quantity),
            last_calculated_time = VALUES(last_calculated_time)
    </update>

    <!-- 重新计算所有员工的股票汇总数据 -->
    <update id="recalculateAll">
        INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, withdrawn_quantity, last_calculated_time)
        SELECT 
            es.employee_id,
            COALESCE(SUM(es.quantity), 0) as total_quantity,
            COALESCE(SUM(CASE WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity ELSE 0 END), 0) as unlocked_quantity,
            COALESCE(withdrawn.withdrawn_quantity, 0) as withdrawn_quantity,
            NOW() as last_calculated_time
        FROM employee_stock es
        LEFT JOIN (
            SELECT employee_id, SUM(quantity) as withdrawn_quantity
            FROM stock_withdrawal 
            WHERE status = 'APPROVED'
            GROUP BY employee_id
        ) withdrawn ON es.employee_id = withdrawn.employee_id
        GROUP BY es.employee_id, withdrawn.withdrawn_quantity
        ON DUPLICATE KEY UPDATE
            total_quantity = VALUES(total_quantity),
            unlocked_quantity = VALUES(unlocked_quantity),
            withdrawn_quantity = VALUES(withdrawn_quantity),
            last_calculated_time = VALUES(last_calculated_time)
    </update>

    <!-- 检查数据一致性 -->
    <select id="checkDataConsistency" resultType="map">
        SELECT 
            ess.employee_id,
            e.name as employee_name,
            ess.total_quantity as summary_total,
            ess.unlocked_quantity as summary_unlocked,
            ess.withdrawn_quantity as summary_withdrawn,
            ess.available_quantity as summary_available,
            COALESCE(actual.actual_total, 0) as actual_total,
            COALESCE(actual.actual_unlocked, 0) as actual_unlocked,
            COALESCE(actual.actual_withdrawn, 0) as actual_withdrawn,
            (COALESCE(actual.actual_unlocked, 0) - COALESCE(actual.actual_withdrawn, 0)) as actual_available,
            ess.last_calculated_time
        FROM employee_stock_summary ess
        LEFT JOIN employee e ON ess.employee_id = e.employee_id
        LEFT JOIN (
            SELECT 
                es.employee_id,
                SUM(es.quantity) as actual_total,
                SUM(CASE WHEN es.unlock_time IS NULL OR es.unlock_time &lt;= NOW() THEN es.quantity ELSE 0 END) as actual_unlocked,
                COALESCE(sw.withdrawn_quantity, 0) as actual_withdrawn
            FROM employee_stock es
            LEFT JOIN (
                SELECT employee_id, SUM(quantity) as withdrawn_quantity
                FROM stock_withdrawal 
                WHERE status = 'APPROVED'
                GROUP BY employee_id
            ) sw ON es.employee_id = sw.employee_id
            GROUP BY es.employee_id, sw.withdrawn_quantity
        ) actual ON ess.employee_id = actual.employee_id
        WHERE ess.total_quantity != COALESCE(actual.actual_total, 0)
           OR ess.unlocked_quantity != COALESCE(actual.actual_unlocked, 0)
           OR ess.withdrawn_quantity != COALESCE(actual.actual_withdrawn, 0)
        ORDER BY ess.employee_id
    </select>

    <!-- 统计信息 -->
    <select id="getStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_employees,
            SUM(total_quantity) as total_stock_quantity,
            SUM(unlocked_quantity) as total_unlocked_quantity,
            SUM(withdrawn_quantity) as total_withdrawn_quantity,
            SUM(available_quantity) as total_available_quantity,
            AVG(total_quantity) as avg_stock_per_employee,
            MAX(total_quantity) as max_stock_per_employee,
            MIN(total_quantity) as min_stock_per_employee
        FROM employee_stock_summary
    </select>

    <!-- 查询有股票的员工数量 -->
    <select id="countEmployeesWithStock" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM employee_stock_summary WHERE total_quantity > 0
    </select>

    <!-- 查询总股票数量 -->
    <select id="getTotalStockQuantity" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(total_quantity), 0) FROM employee_stock_summary
    </select>

    <!-- 查询总可提现数量 -->
    <select id="getTotalAvailableQuantity" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(available_quantity), 0) FROM employee_stock_summary
    </select>

    <!-- 查询需要重新计算的记录 -->
    <select id="selectNeedRecalculate" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT employee_id 
        FROM employee_stock_summary 
        WHERE last_calculated_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY last_calculated_time ASC
    </select>

</mapper>
