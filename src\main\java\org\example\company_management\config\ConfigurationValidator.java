package org.example.company_management.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 配置验证器
 * 在应用启动时验证关键配置是否正确加载
 */
@Slf4j
@Component
public class ConfigurationValidator implements CommandLineRunner {

    @Value("${server.servlet.multipart.max-file-size:1MB}")
    private String maxFileSize;

    @Value("${server.servlet.multipart.max-request-size:1MB}")
    private String maxRequestSize;

    @Value("${app.upload.path}")
    private String uploadPath;

    @Value("${app.upload.url-prefix}")
    private String urlPrefix;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 应用配置验证 ===");
        log.info("文件上传配置:");
        log.info("  - 单个文件最大大小: {}", maxFileSize);
        log.info("  - 整个请求最大大小: {}", maxRequestSize);
        log.info("  - 上传根路径: {}", uploadPath);
        log.info("  - URL访问前缀: {}", urlPrefix);
        log.info("===================");
        
        // 验证文件大小配置
        if ("1MB".equals(maxFileSize)) {
            log.warn("⚠️  警告：文件上传大小限制仍为默认的1MB，可能配置未生效！");
        } else {
            log.info("✅ 文件上传大小配置已正确加载");
        }
    }
}
