package org.example.company_management.validation;

/**
 * 验证分组类
 */
public class ValidationGroups {

    /**
     * 添加操作的验证分组
     */
    public interface Add {
    }

    /**
     * 更新操作的验证分组
     */
    public interface Update {
    }

    /**
     * 批量记录创建操作的校验组
     */
    public interface CreateBatch {}

    /**
     * 推广提交审核操作的验证分组
     */
    public interface PromotionSubmit {}

    /**
     * 推广审核操作的验证分组
     */
    public interface PromotionAudit {}
}