package org.example.company_management.utils;

import org.example.company_management.dto.MeetingDTO;
import org.example.company_management.dto.MeetingLocationDTO;
import org.example.company_management.entity.MeetingLocation;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会议相关验证工具类
 * 提供应用层的数据验证方法，替代数据库CHECK约束
 * 确保在不同MySQL版本中的兼容性
 */
public class MeetingValidationUtil {

    /**
     * 验证会议地点的日期范围设置
     * 
     * @param availableStartDate 开始日期
     * @param availableEndDate 结束日期
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateLocationDateRange(LocalDate availableStartDate, LocalDate availableEndDate) {
        // 如果两个日期都为null，表示无限制，验证通过
        if (availableStartDate == null && availableEndDate == null) {
            return null;
        }
        
        // 如果只有一个日期为null，表示单边限制，验证通过
        if (availableStartDate == null || availableEndDate == null) {
            return null;
        }
        
        // 如果两个日期都不为null，检查开始日期是否不晚于结束日期
        if (availableStartDate.isAfter(availableEndDate)) {
            return "开放开始日期不能晚于开放结束日期";
        }
        
        return null;
    }

    /**
     * 验证会议地点DTO的日期范围
     * 
     * @param locationDTO 会议地点DTO
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateLocationDateRange(MeetingLocationDTO locationDTO) {
        if (locationDTO == null) {
            return null;
        }
        return validateLocationDateRange(locationDTO.getAvailableStartDate(), locationDTO.getAvailableEndDate());
    }

    /**
     * 验证会议地点实体的日期范围
     * 
     * @param location 会议地点实体
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateLocationDateRange(MeetingLocation location) {
        if (location == null) {
            return null;
        }
        return validateLocationDateRange(location.getAvailableStartDate(), location.getAvailableEndDate());
    }

    /**
     * 验证会议时间顺序
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeetingTimeOrder(LocalDateTime startTime, LocalDateTime endTime) {
        // 开始时间必须存在
        if (startTime == null) {
            return "会议开始时间不能为空";
        }
        
        // 如果结束时间存在，必须大于开始时间
        if (endTime != null && !endTime.isAfter(startTime)) {
            return "会议结束时间必须晚于开始时间";
        }
        
        return null;
    }

    /**
     * 验证会议DTO的时间顺序
     * 
     * @param meetingDTO 会议DTO
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeetingTimeOrder(MeetingDTO meetingDTO) {
        if (meetingDTO == null) {
            return null;
        }
        return validateMeetingTimeOrder(meetingDTO.getStartTime(), meetingDTO.getEndTime());
    }

    /**
     * 验证会议日期是否在地点的可用日期范围内
     * 
     * @param meetingDate 会议日期
     * @param availableStartDate 地点开放开始日期
     * @param availableEndDate 地点开放结束日期
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeetingDateInRange(LocalDate meetingDate, 
                                                   LocalDate availableStartDate, 
                                                   LocalDate availableEndDate) {
        if (meetingDate == null) {
            return "会议日期不能为空";
        }
        
        // 检查是否在开始日期之后
        if (availableStartDate != null && meetingDate.isBefore(availableStartDate)) {
            return String.format("会议日期不能早于地点开放开始日期 %s", availableStartDate);
        }
        
        // 检查是否在结束日期之前
        if (availableEndDate != null && meetingDate.isAfter(availableEndDate)) {
            return String.format("会议日期不能晚于地点开放结束日期 %s", availableEndDate);
        }
        
        return null;
    }

    /**
     * 验证会议是否在地点的可用日期范围内
     * 
     * @param meetingDTO 会议DTO
     * @param location 会议地点
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeetingDateInRange(MeetingDTO meetingDTO, MeetingLocation location) {
        if (meetingDTO == null || meetingDTO.getStartTime() == null) {
            return "会议信息不完整";
        }
        
        if (location == null) {
            return "会议地点信息不存在";
        }
        
        LocalDate meetingDate = meetingDTO.getStartTime().toLocalDate();
        return validateMeetingDateInRange(meetingDate, 
                                        location.getAvailableStartDate(), 
                                        location.getAvailableEndDate());
    }

    /**
     * 综合验证会议地点信息
     * 
     * @param locationDTO 会议地点DTO
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeetingLocation(MeetingLocationDTO locationDTO) {
        if (locationDTO == null) {
            return "会议地点信息不能为空";
        }
        
        // 验证日期范围
        String dateRangeError = validateLocationDateRange(locationDTO);
        if (dateRangeError != null) {
            return dateRangeError;
        }
        
        // 可以在这里添加其他业务验证逻辑
        
        return null;
    }

    /**
     * 综合验证会议信息
     * 
     * @param meetingDTO 会议DTO
     * @param location 会议地点（可选）
     * @return 验证结果消息，null表示验证通过
     */
    public static String validateMeeting(MeetingDTO meetingDTO, MeetingLocation location) {
        if (meetingDTO == null) {
            return "会议信息不能为空";
        }
        
        // 验证时间顺序
        String timeOrderError = validateMeetingTimeOrder(meetingDTO);
        if (timeOrderError != null) {
            return timeOrderError;
        }
        
        // 如果提供了地点信息，验证会议日期是否在地点可用范围内
        if (location != null) {
            String dateRangeError = validateMeetingDateInRange(meetingDTO, location);
            if (dateRangeError != null) {
                return dateRangeError;
            }
        }
        
        return null;
    }
}
