# 开发环境配置
server:
  port: 8080
  servlet:
    multipart:
      max-file-size: 50MB      # 单个文件最大大小
      max-request-size: 50MB   # 整个请求最大大小

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: 123456
  sql:
    init:
      mode: never
      schema-locations: classpath:sql/init.sql

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 开发环境显示SQL日志

# 文件上传配置
app:
  upload:
    path: D:/Jobs/Project/company_management/ManaSysSer/uploads  # 开发环境使用绝对路径
    base-url: http://localhost:8080  # 开发环境后端地址

# 文件上传配置（与app.upload.path保持一致）
file:
  upload:
    path: D:/Jobs/Project/company_management/ManaSysSer/uploads  # 文件上传根路径，使用绝对路径

# JWT配置
jwt:
  secret: company_management_system_dev
  expiration: 86400000  # 24小时
