package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.MeetingLocation;
import org.example.company_management.dto.LocationAvailabilityDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会议地点数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Mapper
public interface MeetingLocationMapper {
    
    /**
     * 插入会议地点
     * 
     * @param meetingLocation 会议地点信息
     * @return 影响行数
     */
    int insert(MeetingLocation meetingLocation);
    
    /**
     * 根据ID删除会议地点
     * 
     * @param id 地点ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);
    
    /**
     * 更新会议地点
     * 
     * @param meetingLocation 会议地点信息
     * @return 影响行数
     */
    int update(MeetingLocation meetingLocation);
    
    /**
     * 根据ID查询会议地点
     * 
     * @param id 地点ID
     * @return 会议地点信息
     */
    MeetingLocation selectById(@Param("id") Integer id);
    
    /**
     * 分页查询会议地点列表
     * 
     * @param params 查询参数
     * @return 会议地点列表
     */
    List<MeetingLocation> selectByPage(Map<String, Object> params);
    
    /**
     * 查询会议地点总数
     * 
     * @param params 查询参数
     * @return 总数
     */
    int countByPage(Map<String, Object> params);
    
    /**
     * 查询所有启用的会议地点
     *
     * @return 启用的会议地点列表
     */
    List<MeetingLocation> selectActiveLocations();

    /**
     * 查询所有会议地点（包括禁用的）
     *
     * @return 所有会议地点列表
     */
    List<MeetingLocation> selectAllLocations();
    
    /**
     * 根据名称查询会议地点（用于重名检查）
     *
     * @param name 地点名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 会议地点信息
     */
    MeetingLocation selectByName(@Param("name") String name, @Param("excludeId") Integer excludeId);

    /**
     * 查询指定地点在指定时间范围内的已预订时间段
     *
     * @param locationId 地点ID
     * @param startDate 查询开始日期
     * @param endDate 查询结束日期
     * @param excludeMeetingId 排除的会议ID（编辑模式下使用）
     * @return 已预订时间段列表
     */
    List<LocationAvailabilityDTO.BookedTimeSlot> selectBookedTimeSlots(
        @Param("locationId") Integer locationId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        @Param("excludeMeetingId") Long excludeMeetingId
    );
}
