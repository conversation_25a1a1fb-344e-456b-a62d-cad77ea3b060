<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    View,
    Loading,
} from '@element-plus/icons-vue';
import {
    getEmployeePage,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateEmployeeStatus,
} from '@/api/employee';
import { getDepartmentList } from '@/api/department';
import { getPositionsByDepartmentId, getPositionList } from '@/api/position';
import { hierarchicalAdminMenuItems } from '@/config/adminMenuConfig';

// 员工数据
const employeeData = ref([]);
const loading = ref(true);
const searchText = ref('');
const searchDepartmentId = ref('');
const searchPositionId = ref('');
const searchStatus = ref('');

// 部门和职位列表
const departmentList = ref([]);
const positionList = ref([]);
const allPositionList = ref([]); // 用于搜索的所有职位列表
const loadingDepartments = ref(false);
const loadingPositions = ref(false);

// 分页设置
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    employee_id: null,
    name: '',
    email: '',
    phone: '',
    password: '',
    entry_date: '',
    exit_date: '',
    id_card: '',
    department_id: null,
    position_id: null,
    logistics_route: '',
    role: 'employee',
    accessibleMenuIds: [],
    status: 'Active',
});

// 表单规则
const rules = {
    name: [
        { required: true, message: '请输入员工姓名', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    ],
    phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
    ],
    password: [
        {
            required: () => dialogType.value === 'add', // 只有添加时必填
            message: '请输入密码，需包含大小写字母和数字，长度至少8位',
            trigger: 'blur',
            validator: validatePassword,
        },
    ],
    entry_date: [
        { required: true, message: '请选择入职日期', trigger: 'change' },
    ],
    id_card: [
        { required: true, message: '请输入身份证号', trigger: 'blur' },
    ],
    department_id: [
        { required: true, message: '请选择部门', trigger: 'change' },
    ],
    position_id: [{ required: true, message: '请选择职位', trigger: 'change' }],
    role: [{ required: true, message: '请选择角色', trigger: 'change' }],
};

// 密码验证
function validatePassword(rule, value, callback) {
    if (dialogType.value === 'edit' && !value) {
        // 编辑模式下，密码可以为空（表示不修改密码）
        callback();
    } else if (dialogType.value === 'add' && !value) {
        // 添加模式下，密码必填
        callback(new Error('请输入密码，需包含大小写字母和数字，长度至少8位'));
    } else if (value && value.length < 8) {
        callback(new Error('密码长度不能少于8个字符'));
    } else if (value && !/[A-Z]/.test(value)) {
        callback(new Error('密码必须包含大写字母'));
    } else if (value && !/[a-z]/.test(value)) {
        callback(new Error('密码必须包含小写字母'));
    } else if (value && !/[0-9]/.test(value)) {
        callback(new Error('密码必须包含数字'));
    } else {
        callback();
    }
}

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取部门列表失败');
        }
    } catch (error) {
        console.error('加载部门列表失败:', error);
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 加载所有职位列表（用于搜索下拉框）
const loadAllPositionList = async () => {
    try {
        const res = await getPositionList();
        if (res.code === 200) {
            allPositionList.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取职位列表失败');
        }
    } catch (error) {
        console.error('加载职位列表失败:', error);
        ElMessage.error('加载职位列表失败: ' + (error.message || '未知错误'));
    }
};

// 根据部门ID加载职位列表
const loadPositionsByDepartmentId = async (departmentId) => {
    if (!departmentId) {
        positionList.value = [];
        return;
    }

    loadingPositions.value = true;
    try {
        const res = await getPositionsByDepartmentId(departmentId);
        if (res.code === 200) {
            positionList.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取职位列表失败');
        }
    } catch (error) {
        console.error('加载职位列表失败:', error);
        ElMessage.error('加载职位列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingPositions.value = false;
    }
};

// 当部门改变时，更新职位列表
const handleDepartmentChange = async (departmentId) => {
    form.position_id = null; // 清空已选的职位
    if (!departmentId) {
        positionList.value = [];
        return;
    }

    try {
        await loadPositionsByDepartmentId(departmentId);
        // 如果加载后没有职位，显示提示
        if (positionList.value.length === 0) {
            ElMessage({
                type: 'warning',
                message: '当前选择的部门暂无职位，请先添加职位',
                duration: 3000,
            });
        }
    } catch (error) {
        console.error('部门变更加载职位失败:', error);
        ElMessage.error('加载职位数据失败');
    }
};

// 加载员工数据
const loadEmployeeData = async () => {
    loading.value = true;
    employeeData.value = [];

    try {
        const res = await getEmployeePage({
            pageNum: pagination.currentPage,
            pageSize: pagination.pageSize,
            name: searchText.value || undefined,
            departmentId: searchDepartmentId.value || undefined,
            position: searchPositionId.value || undefined,
            status: searchStatus.value || undefined,
        });

        if (res.code === 200) {
            employeeData.value = res.data.list || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.msg || '获取员工数据失败');
        }
    } catch (error) {
        console.error('加载员工数据失败:', error);
        ElMessage.error('加载员工数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.employee_id = null;
    form.name = '';
    form.email = '';
    form.phone = '';
    form.password = '';
    form.entry_date = '';
    form.exit_date = '';
    form.id_card = '';
    form.department_id = null;
    form.position_id = null;
    form.logistics_route = '';
    form.role = 'employee';
    form.accessibleMenuIds = [];
    form.status = 'Active';
    positionList.value = [];
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    // 将后端返回的数据格式转换为表单所需格式
    form.employee_id = row.employeeId;
    form.name = row.name;
    form.email = row.email;
    form.phone = row.phone || '';
    form.password = ''; // 编辑时密码默认为空，表示不修改
    form.entry_date = row.entryDate;
    form.exit_date = row.exitDate || '';
    form.id_card = row.idCard;
    form.department_id = row.departmentId;
    form.role = row.role;
    form.logistics_route = row.logisticsRoute || '';
    form.status = row.status;

    // 处理 accessibleMenuIds
    if (row.accessibleMenuIdsJson) {
        try {
            const parsedMenuIds = JSON.parse(row.accessibleMenuIdsJson);
            if (Array.isArray(parsedMenuIds)) {
                form.accessibleMenuIds = parsedMenuIds;
            } else {
                form.accessibleMenuIds = [];
            }
        } catch (e) {
            console.error("解析 accessibleMenuIdsJson 失败:", e, row.accessibleMenuIdsJson);
            form.accessibleMenuIds = []; // 解析失败则置空
        }
    } else {
        form.accessibleMenuIds = [];
    }

    // 先加载该部门下的职位列表，确保职位列表已更新
    try {
        await loadPositionsByDepartmentId(row.departmentId);
        // 检查加载的职位列表中是否包含当前员工的职位
        const positionExists = positionList.value.some(
            (p) => p.positionId === row.positionId
        );
        if (positionExists) {
            form.position_id = row.positionId;
        } else {
            // 若职位不在当前部门，则清空选择
            form.position_id = null;
            console.warn(
                `员工职位ID ${row.positionId} 不属于部门ID ${row.departmentId}，已清空职位选择`
            );
        }
    } catch (error) {
        console.error('加载职位失败:', error);
        ElMessage.error('加载职位数据失败');
    }

    dialogVisible.value = true;
};

// 确认删除员工
const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要删除员工 "${row.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            loading.value = true;
            try {
                const res = await deleteEmployee(row.employeeId);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除员工成功',
                        duration: 2000,
                    });
                    loadEmployeeData();
                } else {
                    ElMessage({
                        type: 'error',
                        message: res.msg || '删除失败',
                        duration: 3000,
                    });
                    loading.value = false;
                }
            } catch (error) {
                console.error('删除员工失败:', error);
                ElMessage({
                    type: 'error',
                    message: '删除失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// Helper function to find a node by ID in the menu tree
function findNodeById(tree, nodeId) {
    for (const node of tree) {
        if (node.id === nodeId) return node;
        if (node.children) {
            const found = findNodeById(node.children, nodeId);
            if (found) return found;
        }
    }
    return null;
}

// Helper function to collect all children IDs for a given node
function collectAllChildrenForNode(node, collectedIdsSet) {
    if (node && node.children) {
        for (const child of node.children) {
            collectedIdsSet.add(child.id);
            collectAllChildrenForNode(child, collectedIdsSet);
        }
    }
}

// Helper function to build a map of childId: parentId
function getParentMap(tree, map = {}, parentId = null) {
    for (const node of tree) {
        if (parentId) {
            map[node.id] = parentId;
        }
        if (node.children) {
            getParentMap(node.children, map, node.id);
        }
    }
    return map;
}

// Processes selected menu IDs to ensure parent-child dependencies are met
function processMenuPermissions(currentSelectedIds, menuTree) {
    if (!currentSelectedIds || currentSelectedIds.length === 0) {
        return [];
    }
    const finalIds = new Set(currentSelectedIds); // Start with user's direct selections
    const parentMap = getParentMap(menuTree);

    // Iterate over a copy, as we might add to finalIds during iteration affecting parent/child checks
    const initialSelections = [...currentSelectedIds];

    for (const id of initialSelections) {
        // 1. Ensure all children are added if a (parent) node is selected
        const node = findNodeById(menuTree, id);
        if (node) {
            collectAllChildrenForNode(node, finalIds); // Adds all children of this selected node
        }

        // 2. Ensure all parents are added for every selected node (direct or indirect child)
        let currentIdForParentLookup = id;
        while (parentMap[currentIdForParentLookup]) {
            const parentId = parentMap[currentIdForParentLookup];
            finalIds.add(parentId);
            currentIdForParentLookup = parentId;
        }
    }
    return Array.from(finalIds);
}

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid, fields) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备提交的数据，转换为后端需要的格式
                const submitData = {
                    employeeId: form.employee_id,
                    name: form.name,
                    email: form.email,
                    phone: form.phone,
                    entryDate: form.entry_date,
                    exitDate: form.exit_date || null,
                    idCard: form.id_card,
                    departmentId: form.department_id,
                    positionId: form.position_id,
                    logisticsRoute: form.logistics_route || null,
                    role: form.role,
                    accessibleMenuIdsJson: null, // Initialize
                };

                // 只有在有密码时才添加密码字段
                if (form.password) {
                    submitData.password = form.password;
                }

                if (form.role === 'admin') {
                    const idsToSubmit = form.accessibleMenuIds || [];
                    // 当 check-strictly 为 false 时, form.accessibleMenuIds (即 el-tree-select 的 v-model)
                    // 已经包含了所有被勾选节点的 ID (包括被勾选父节点的所有子节点)。
                    // 直接存储这个列表，应该能让 el-tree-select 在重新加载时正确渲染包括半选在内的状态。
                    submitData.accessibleMenuIdsJson = JSON.stringify(idsToSubmit);
                } else {
                    // 对于非 admin 角色，权限列表为空
                    submitData.accessibleMenuIdsJson = JSON.stringify([]);
                }

                let res;
                if (dialogType.value === 'add') {
                    // 添加员工
                    res = await addEmployee(submitData);
                } else {
                    // 更新员工
                    res = await updateEmployee(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加员工成功'
                                : '更新员工成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadEmployeeData();
                } else {
                    // ElMessage({
                    //     type: 'error',
                    //     message:
                    //         res.msg ||
                    //         (dialogType.value === 'add'
                    //             ? '添加失败'
                    //             : '更新失败'),
                    //     duration: 3000,
                    // });
                    // Assuming global error handler in request utility will show the message from backend
                }
            } catch (error) {
                console.error('提交表单失败:', error);
                // ElMessage({
                //     type: 'error',
                //     message:
                //         res.msg ||
                //         (dialogType.value === 'add'
                //             ? '添加失败'
                //             : '更新失败'),
                //     duration: 3000,
                // });
                // Assuming global error handler in request utility will show the message from backend
            } finally {
                formLoading.value = false;
            }
        } else {
            console.log('表单验证失败', fields);

            // 构建缺失字段的错误信息
            const errorFields = [];
            for (const key in fields) {
                if (fields.hasOwnProperty(key)) {
                    const errorField = fields[key];
                    if (errorField && errorField.length > 0) {
                        errorFields.push(errorField[0].message);
                    }
                }
            }

            // 显示所有错误信息
            ElMessage({
                type: 'warning',
                message: '请完善表单信息：' + errorFields.join('；'),
                duration: 5000,
            });
            return false;
        }
    });
};

// 更新员工状态
const handleStatusChange = async (row) => {
    const newStatus = row.status === 'Active' ? 'Inactive' : 'Active';
    const statusText = newStatus === 'Active' ? '启用' : '禁用';

    ElMessageBox.confirm(
        `确定要${statusText}员工 "${row.name}" 吗？`,
        '状态更新确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            try {
                const res = await updateEmployeeStatus(
                    row.employeeId,
                    newStatus
                );

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: `员工状态已更新为${statusText}`,
                        duration: 2000,
                    });
                    loadEmployeeData();
                } else {
                    ElMessage({
                        type: 'error',
                        message: res.msg || '更新状态失败',
                        duration: 3000,
                    });
                }
            } catch (error) {
                console.error('更新员工状态失败:', error);
                ElMessage({
                    type: 'error',
                    message: '更新状态失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
            }
        })
        .catch(() => {
            // 取消操作，不做处理
        });
};

// 搜索
const handleSearch = () => {
    pagination.currentPage = 1;
    loadEmployeeData();
};

// 刷新
const handleRefresh = () => {
    searchText.value = '';
    searchDepartmentId.value = '';
    searchPositionId.value = '';
    searchStatus.value = '';
    pagination.currentPage = 1;
    loadEmployeeData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    loadEmployeeData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    loadEmployeeData();
};

// 格式化时间
const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    // 只返回日期部分，不包含时间
    const date = new Date(dateTimeString);
    return date
        .toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
        })
        .replace(/\//g, '-');
};

// 格式化完整日期时间 (YYYY-MM-DD HH:mm:ss)
const formatFullDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    const date = new Date(dateTimeString);
    if (isNaN(date.getTime())) return dateTimeString; // 如果无效则返回原始字符串
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }).replace(/\//g, '-'); // 替换 / 为 -
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 获取职位名称
const getPositionName = (positionId, departmentId) => {
    // 如果已经加载了该部门的职位列表，直接从中查找
    const position = positionList.value.find(
        (item) => item.positionId === positionId
    );
    if (position) {
        return position.positionName;
    }

    // 如果还没有加载，则返回空字符串
    return '';
};

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadAllPositionList();
    loadEmployeeData();
});
</script>

<template>
    <div class="employee-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchText"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    style="width: 180px"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>
                <el-select
                    v-model="searchPositionId"
                    placeholder="选择职位"
                    clearable
                    style="width: 180px"
                >
                    <el-option
                        v-for="item in allPositionList"
                        :key="item.positionId"
                        :label="item.positionName"
                        :value="item.positionId"
                    />
                </el-select>
                <el-select
                    v-model="searchStatus"
                    placeholder="选择状态"
                    clearable
                    style="width: 120px"
                >
                    <el-option
                        label="在职"
                        value="Active"
                    />
                    <el-option
                        label="离职"
                        value="Inactive"
                    />
                </el-select>
                <el-button
                    type="primary"
                    @click="handleSearch"
                >
                    <el-icon>
                        <Search />
                    </el-icon>搜索
                </el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>
            <div class="action-box">
                <el-button
                    type="primary"
                    @click="handleAdd"
                    class="add-btn"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加员工
                </el-button>
            </div>
        </div>

        <!-- 表格 -->
        <el-table
            v-loading="loading"
            :data="employeeData"
            border
            row-key="employeeId"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="index"
                width="60"
                align="center"
                label="序号"
                fixed
                class-name="index-column"
            />
            <el-table-column
                prop="name"
                label="姓名"
                min-width="100"
                show-overflow-tooltip
            />
            <el-table-column
                prop="email"
                label="邮箱"
                min-width="150"
                show-overflow-tooltip
            />
            <el-table-column
                prop="phone"
                label="手机号"
                min-width="120"
                show-overflow-tooltip
            />
            <el-table-column
                prop="idCard"
                label="身份证号"
                min-width="180"
                show-overflow-tooltip
            />
            <el-table-column
                label="部门"
                min-width="120"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ getDepartmentName(row.departmentId) }}
                </template>
            </el-table-column>
            <el-table-column
                label="职位"
                min-width="120"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ scope.row.positionName || '----' }}
                </template>
            </el-table-column>
            <el-table-column
                label="物流航线"
                min-width="120"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ scope.row.logisticsRoute || '----' }}
                </template>
            </el-table-column>
            <el-table-column
                label="入职日期"
                min-width="120"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ formatDateTime(scope.row.entryDate) }}
                </template>
            </el-table-column>
            <el-table-column
                label="状态"
                width="100"
                align="center"
                show-overflow-tooltip
            >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.exitDate"
                        :content="`离职日期: ${formatDateTime(scope.row.exitDate)}`"
                        placement="top"
                    >
                        <el-tag
                            :type="scope.row.status === 'Active' ? 'success' : 'danger'"
                            class="status-tag"
                        >
                            {{ scope.row.status === 'Active' ? '在职' : '离职' }}
                        </el-tag>
                    </el-tooltip>
                    <el-tag
                        v-else
                        :type="scope.row.status === 'Active' ? 'success' : 'danger'"
                        class="status-tag"
                    >
                        {{ scope.row.status === 'Active' ? '在职' : '离职' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                label="创建时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ formatFullDateTime(scope.row.createTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="更新时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ formatFullDateTime(scope.row.updateTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="180"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加员工' : '编辑员工'"
            width="600px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="left"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="姓名"
                    prop="name"
                    required
                >
                    <el-input
                        v-model="form.name"
                        placeholder="请输入员工姓名"
                    />
                </el-form-item>
                <el-form-item
                    label="邮箱"
                    prop="email"
                >
                    <el-input
                        v-model="form.email"
                        placeholder="请输入邮箱地址"
                    />
                </el-form-item>
                <el-form-item
                    label="手机号"
                    prop="phone"
                    required
                >
                    <el-input
                        v-model="form.phone"
                        placeholder="请输入手机号码"
                    />
                </el-form-item>
                <el-form-item
                    label="密码"
                    prop="password"
                    :required="dialogType === 'add'"
                >
                    <el-input
                        v-model="form.password"
                        type="password"
                        show-password
                        :placeholder="dialogType === 'edit' ? '不填写则不修改密码，填写需包含大小写字母和数字，长度至少8位' : '密码需包含大小写字母和数字，长度至少8位'"
                    />
                </el-form-item>
                <el-form-item
                    label="身份证号"
                    prop="id_card"
                    required
                >
                    <el-input
                        v-model="form.id_card"
                        placeholder="请输入身份证号"
                    />
                </el-form-item>
                <el-form-item
                    label="入职日期"
                    prop="entry_date"
                    required
                >
                    <el-date-picker
                        v-model="form.entry_date"
                        type="date"
                        placeholder="选择入职日期"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item
                    label="离职日期"
                    prop="exit_date"
                >
                    <el-date-picker
                        v-model="form.exit_date"
                        type="date"
                        placeholder="选择离职日期（可选）"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
                <el-form-item
                    label="部门"
                    prop="department_id"
                    required
                >
                    <el-select
                        v-model="form.department_id"
                        placeholder="请选择部门"
                        style="width: 100%"
                        :loading="loadingDepartments"
                        @change="handleDepartmentChange"
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="职位"
                    prop="position_id"
                    required
                >
                    <el-select
                        v-model="form.position_id"
                        placeholder="请选择职位"
                        style="width: 100%"
                        :loading="loadingPositions"
                        :disabled="!form.department_id"
                    >
                        <el-option
                            v-if="positionList.length === 0 && !loadingPositions && form.department_id"
                            label="当前部门暂无职位"
                            value=""
                            disabled
                        />
                        <el-option
                            v-for="item in positionList"
                            :key="item.positionId"
                            :label="item.positionName"
                            :value="item.positionId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="物流航线"
                    prop="logistics_route"
                >
                    <el-input
                        v-model="form.logistics_route"
                        placeholder="请输入物流航线（可选）"
                    />
                </el-form-item>
                <el-form-item
                    label="角色"
                    prop="role"
                    required
                >
                    <el-select
                        v-model="form.role"
                        placeholder="请选择角色"
                        style="width: 100%"
                    >
                        <el-option
                            label="员工"
                            value="employee"
                        />
                        <el-option
                            label="部门负责人"
                            value="manager"
                        />
                        <el-option
                            label="管理员"
                            value="admin"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="后台菜单权限"
                    prop="accessibleMenuIds"
                    v-if="form.role === 'admin'"
                    key="admin-menu-permission-item"
                    class="admin-menu-permission-item"
                >
                    <el-tree-select
                        v-model="form.accessibleMenuIds"
                        :data="hierarchicalAdminMenuItems"
                        multiple
                        show-checkbox
                        :check-strictly="false"
                        node-key="id"
                        :props="{ label: 'title', children: 'children', value: 'id' }" 
                        placeholder="选择后台菜单权限"
                        style="width: 100%"
                        check-on-click-node
                        default-expand-all
                        class="admin-menu-tree-select"
                    />
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<style scoped>
.employee-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

:deep(.status-tag:hover) {
    transform: scale(1.1);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.admin-menu-permission-item .el-form-item__label {
    line-height: normal;
}

/* Optional: Style for the tree select if needed */
.admin-menu-tree-select :deep(.el-select__tags-text) {
    max-width: 150px; /* Adjust as needed */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
}

.admin-menu-tree-select :deep(.el-tag) {
    max-width: 200px; /* Adjust as needed */
}

:deep(.inactive-row) {
    color: #c0c4cc;
    background-color: #f9f9f9;
}
</style> 