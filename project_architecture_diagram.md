# 公司管理系统架构图

## 系统总体架构流程图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        U1[员工用户]
        U2[部门负责人]
        U3[系统管理员]
    end

    %% 前端应用层
    subgraph "前端应用层 (Frontend Layer)"
        subgraph "员工端 (User Client)"
            F1[登录页面]
            F2[个人仪表盘]
            F3[客户管理]
            F4[销售日报]
            F5[业绩查询]
            F6[推广管理]
            F7[部门功能]
        end
        
        subgraph "管理端 (Admin Client)"
            A1[管理员登录]
            A2[员工管理]
            A3[部门管理]
            A4[业绩分析]
            A5[日报管理]
            A6[系统配置]
        end
    end

    %% API网关层
    subgraph "API层 (API Gateway)"
        G1[JWT认证拦截器]
        G2[权限验证]
        G3[请求路由]
    end

    %% 后端服务层
    subgraph "后端服务层 (Backend Services)"
        subgraph "Controller层"
            C1[AuthController<br/>认证控制器]
            C2[EmployeeController<br/>员工控制器]
            C3[DepartmentController<br/>部门控制器]
            C4[SalesReportController<br/>日报控制器]
            C5[ClientController<br/>客户控制器]
            C6[PerformanceController<br/>业绩控制器]
            C7[PettyCashController<br/>备用金控制器]
        end
        
        subgraph "Service层"
            S1[AuthService<br/>认证服务]
            S2[EmployeeService<br/>员工服务]
            S3[DepartmentService<br/>部门服务]
            S4[SalesReportService<br/>日报服务]
            S5[ClientService<br/>客户服务]
            S6[NotificationService<br/>通知服务]
            S7[PerformanceService<br/>业绩服务]
            S8[PettyCashService<br/>备用金服务]
        end
        
        subgraph "Mapper层"
            M1[EmployeeMapper]
            M2[DepartmentMapper]
            M3[SalesReportMapper]
            M4[ClientMapper]
            M5[PerformanceMapper]
            M6[PettyCashMapper]
            M7[PromotionMapper]
        end
    end

    %% 数据层
    subgraph "数据层 (Data Layer)"
        subgraph "MySQL数据库"
            DB1[(employee<br/>员工表)]
            DB2[(department<br/>部门表)]
            DB3[(client<br/>客户表)]
            DB4[(sales_daily_report<br/>销售日报表)]
            DB5[(performance<br/>业绩表)]
            DB6[(salary<br/>工资表)]
            DB7[(promotion<br/>推广表)]
            DB8[(petty_cash<br/>备用金表)]
            DB9[(stock_price<br/>股票价格表)]
            DB10[(employee_stock<br/>员工股票表)]
        end
        
        subgraph "文件存储"
            FS1["图片存储/img"]
            FS2["附件存储/uploads"]
        end
    end

    %% 连接关系
    U1 --> F1
    U1 --> F2
    U1 --> F3
    U1 --> F4
    U1 --> F5
    U1 --> F6
    
    U2 --> F7
    U3 --> A1
    U3 --> A2
    U3 --> A3
    U3 --> A4
    U3 --> A5
    U3 --> A6

    F1 --> G1
    F2 --> G2
    F3 --> G3
    F4 --> G3
    F5 --> G3
    F6 --> G3
    F7 --> G3
    
    A1 --> G1
    A2 --> G2
    A3 --> G3
    A4 --> G3
    A5 --> G3
    A6 --> G3

    G1 --> C1
    G2 --> C2
    G2 --> C3
    G3 --> C4
    G3 --> C5
    G3 --> C6
    G3 --> C7

    C1 --> S1
    C2 --> S2
    C3 --> S3
    C4 --> S4
    C5 --> S5
    C6 --> S7
    C7 --> S8

    S1 --> M1
    S2 --> M1
    S2 --> M2
    S3 --> M2
    S4 --> M3
    S4 --> M4
    S5 --> M4
    S6 --> M1
    S7 --> M5
    S8 --> M6

    M1 --> DB1
    M2 --> DB2
    M3 --> DB4
    M4 --> DB3
    M5 --> DB5
    M6 --> DB8
    M7 --> DB7

    DB1 -.-> DB2
    DB1 -.-> DB3
    DB1 -.-> DB4
    DB1 -.-> DB5
    DB1 -.-> DB6
    DB1 -.-> DB7
    DB1 -.-> DB8
    DB9 -.-> DB10

    S4 --> FS1
    S4 --> FS2

    %% 样式定义
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backendClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class U1,U2,U3 userClass
    class F1,F2,F3,F4,F5,F6,F7,A1,A2,A3,A4,A5,A6 frontendClass
    class G1,G2,G3,C1,C2,C3,C4,C5,C6,C7,S1,S2,S3,S4,S5,S6,S7,S8,M1,M2,M3,M4,M5,M6,M7 backendClass
    class DB1,DB2,DB3,DB4,DB5,DB6,DB7,DB8,DB9,DB10,FS1,FS2 dataClass
```

## 数据库关系图

```mermaid
erDiagram
    EMPLOYEE {
        int employee_id PK
        string name
        string phone UK
        string email UK
        string password
        date entry_date
        date exit_date
        string id_card
        int department_id FK
        int position_id FK
        string role
        string status
    }
    
    DEPARTMENT {
        int department_id PK
        string department_name
        int leader_id FK
        int parent_department_id FK
        string status
    }
    
    POSITION {
        int position_id PK
        string position_name
        string position_description
        int department_id FK
    }
    
    CLIENT {
        int client_id PK
        string name
        int employee_id FK
        string email
        string phone
        string category
        string status
        string client_status
    }
    
    SALES_DAILY_REPORT {
        bigint id PK
        int employee_id FK
        date report_date
        int yearly_new_clients
        int monthly_new_clients
        json inquiry_clients
        json shipping_clients
        enum responsibility_level
        text daily_results
        text work_diary
    }
    
    PERFORMANCE {
        int id PK
        int employee_id FK
        string date
        decimal estimated_performance
        decimal actual_performance
    }
    
    SALARY {
        int id PK
        int employee_id FK
        string date
        decimal basic_salary
        decimal performance_bonus
        decimal sum_salary
    }
    
    PETTY_CASH {
        int id PK
        int employee_id FK
        string purpose
        decimal amount
        string status
        string date
    }
    
    PROMOTION {
        bigint id PK
        string title
        longtext content
        enum content_type
        json attachments
        int author_id FK
        string status
    }

    EMPLOYEE ||--o{ CLIENT : "负责"
    EMPLOYEE ||--o{ SALES_DAILY_REPORT : "提交"
    EMPLOYEE ||--o{ PERFORMANCE : "业绩"
    EMPLOYEE ||--o{ SALARY : "工资"
    EMPLOYEE ||--o{ PETTY_CASH : "备用金"
    EMPLOYEE ||--o{ PROMOTION : "推广"
    EMPLOYEE }o--|| DEPARTMENT : "归属"
    EMPLOYEE }o--|| POSITION : "职位"
    DEPARTMENT ||--o| EMPLOYEE : "负责人"
    DEPARTMENT ||--o{ DEPARTMENT : "上级部门"
    POSITION }o--|| DEPARTMENT : "所属部门"
```

## 技术栈架构图

```mermaid
graph LR
    subgraph "前端技术栈"
        FE1[Vue 3]
        FE2[Vite]
        FE3[Element Plus]
        FE4[Pinia]
        FE5[Vue Router]
        FE6[Axios]
    end
    
    subgraph "后端技术栈"
        BE1[Spring Boot 3.2.0]
        BE2[MyBatis 3.0.3]
        BE3[JWT]
        BE4[Lombok]
        BE5[Validation]
        BE6[Java 21]
    end
    
    subgraph "数据存储"
        DB1[MySQL 8.0]
        DB2[文件系统]
    end
    
    subgraph "开发工具"
        TOOL1[Maven]
        TOOL2[Git]
        TOOL3[IDE]
    end
    
    FE1 --> FE2
    FE1 --> FE3
    FE1 --> FE4
    FE1 --> FE5
    FE1 --> FE6
    
    BE1 --> BE2
    BE1 --> BE3
    BE1 --> BE4
    BE1 --> BE5
    BE6 --> BE1
    
    FE6 -.->|HTTP/HTTPS| BE1
    BE2 -.->|JDBC| DB1
    BE1 -.->|File I/O| DB2
    
    TOOL1 --> BE1
    TOOL2 --> FE1
    TOOL2 --> BE1
    TOOL3 --> FE1
    TOOL3 --> BE1
```

## 业务流程图

### 员工登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant C as AuthController
    participant S as AuthService
    participant M as EmployeeMapper
    participant D as 数据库

    U->>F: 输入手机号密码
    F->>A: POST /auth/employee/login
    A->>C: 路由请求
    C->>S: 调用employeeLogin
    S->>M: selectByPhoneForAuth
    M->>D: 查询员工信息
    D-->>M: 返回员工数据
    M-->>S: 返回员工对象
    S->>S: 验证密码
    S->>S: 生成JWT Token
    S-->>C: 返回Token
    C-->>A: 返回响应
    A-->>F: 返回Token
    F->>F: 存储Token到localStorage
    F-->>U: 跳转到仪表盘
```

### 销售日报提交流程
```mermaid
sequenceDiagram
    participant E as 员工
    participant F as 前端
    participant A as API网关
    participant C as SalesReportController
    participant S as SalesReportService
    participant M as SalesReportMapper
    participant D as 数据库

    E->>F: 填写日报表单
    F->>A: POST /sales-report
    A->>A: JWT验证
    A->>C: 路由请求
    C->>S: submitReport
    S->>S: 计算客户统计
    S->>M: insertOrUpdate
    M->>D: 保存日报数据
    D-->>M: 返回结果
    M-->>S: 返回操作结果
    S-->>C: 返回业务结果
    C-->>A: 返回响应
    A-->>F: 返回成功信息
    F-->>E: 显示提交成功
```

## 权限控制架构图

```mermaid
graph TD
    subgraph "权限控制层"
        A1[JWT Token验证]
        A2[角色权限检查]
        A3[数据权限过滤]
    end

    subgraph "角色定义"
        R1[admin - 系统管理员]
        R2[manager - 部门负责人]
        R3[employee - 普通员工]
    end

    subgraph "权限矩阵"
        P1[员工管理权限]
        P2[部门管理权限]
        P3[客户管理权限]
        P4[日报管理权限]
        P5[业绩查看权限]
        P6[系统配置权限]
    end

    subgraph "数据权限"
        D1[查看所有数据]
        D2[查看部门数据]
        D3[查看个人数据]
    end

    A1 --> A2
    A2 --> A3

    R1 --> P1
    R1 --> P2
    R1 --> P3
    R1 --> P4
    R1 --> P5
    R1 --> P6

    R2 --> P3
    R2 --> P4
    R2 --> P5

    R3 --> P3
    R3 --> P4
    R3 --> P5

    R1 --> D1
    R2 --> D2
    R3 --> D3
```

## 文件存储架构图

```mermaid
graph TB
    subgraph "文件上传流程"
        U1[用户选择文件]
        U2[前端预处理]
        U3[发送到后端]
        U4[后端验证]
        U5[保存到文件系统]
        U6[返回访问路径]
    end

    subgraph "文件存储结构"
        S1[uploads根目录]
        S2[promotions推广附件]
        S3[img图片目录]
        S4[promotion推广图片]
    end

    subgraph "文件访问"
        A1[API统一访问前缀]
        A2[WebMvcConfig路径映射]
        A3[静态资源服务]
    end

    U1 --> U2
    U2 --> U3
    U3 --> U4
    U4 --> U5
    U5 --> U6

    U5 --> S1
    S1 --> S2
    S1 --> S3
    S3 --> S4

    A1 --> A2
    A2 --> A3
    A3 --> S1

    %% 样式定义
    classDef uploadClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef storageClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef accessClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class U1,U2,U3,U4,U5,U6 uploadClass
    class S1,S2,S3,S4 storageClass
    class A1,A2,A3 accessClass
```

## 系统部署架构图

```mermaid
graph TB
    subgraph "开发环境"
        DEV1["前端开发服务器localhost:5173/5174"]
        DEV2["后端开发服务器localhost:8080"]
        DEV3["MySQL开发数据库localhost:3306"]
    end

    subgraph "生产环境"
        PROD1[Nginx Web服务器]
        PROD2[Spring Boot应用<br/>JAR包部署]
        PROD3[MySQL生产数据库]
        PROD4[文件存储系统]
    end

    subgraph "构建流程"
        BUILD1["前端构建npm run build"]
        BUILD2["后端构建mvn clean package"]
        BUILD3["部署脚本start-prod.bat"]
    end

    DEV1 -.->|开发代理| DEV2
    DEV2 -.->|JDBC| DEV3

    BUILD1 --> PROD1
    BUILD2 --> PROD2
    BUILD3 --> PROD2

    PROD1 --> PROD2
    PROD2 --> PROD3
    PROD2 --> PROD4
```


