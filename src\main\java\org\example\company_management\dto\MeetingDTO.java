package org.example.company_management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;
import org.example.company_management.validation.ValidMeetingTime;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ValidMeetingTime(groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
public class MeetingDTO {
    
    /**
     * 会议ID
     */
    private Long id;
    
    /**
     * 会议主题
     */
    @NotBlank(message = "会议主题不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @Size(max = 200, message = "会议主题长度不能超过200个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String title;
    
    /**
     * 会议内容描述
     */
    @Size(max = 5000, message = "会议内容描述长度不能超过5000个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String content;
    
    /**
     * 会议开始时间
     */
    @NotNull(message = "会议开始时间不能为空", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 会议结束时间（可选）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    /**
     * 会议地点（文本描述，保留兼容性）
     */
    @Size(max = 200, message = "会议地点长度不能超过200个字符", groups = {ValidationGroups.Add.class, ValidationGroups.Update.class})
    private String location;

    /**
     * 会议地点ID
     */
    private Integer locationId;

    /**
     * 发起人ID（管理员，保留原有功能）
     */
    private Integer creatorId;

    /**
     * 发起人姓名（非数据库字段）
     */
    private String creatorName;

    /**
     * 会议负责人ID
     */
    private Integer responsibleId;

    /**
     * 会议负责人姓名（非数据库字段）
     */
    private String responsibleName;
    
    /**
     * 会议状态：NOT_STARTED-未开始、IN_PROGRESS-进行中、FINISHED-已结束
     * 注意：此字段由查询时实时计算，不存储在数据库中
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 参与者列表
     */
    @NotNull(message = "参与者列表不能为空", groups = {ValidationGroups.Add.class})
    @Size(min = 1, message = "至少需要选择一个参与者", groups = {ValidationGroups.Add.class})
    private List<MeetingParticipantDTO> participants;
    
    /**
     * 会议总结列表
     */
    private List<MeetingSummaryDTO> summaries;
    
    /**
     * 参与者数量（非数据库字段）
     */
    private Integer participantCount;
    
    /**
     * 总结数量（非数据库字段）
     */
    private Integer summaryCount;
}
