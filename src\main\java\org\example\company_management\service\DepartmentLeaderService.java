package org.example.company_management.service;

import org.example.company_management.entity.DepartmentLeader;

import java.util.List;

/**
 * 部门负责人服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface DepartmentLeaderService {
    
    // ========== 基础CRUD操作 ==========
    
    /**
     * 添加部门负责人
     * @param departmentLeader 部门负责人信息
     */
    void add(DepartmentLeader departmentLeader);
    
    /**
     * 根据ID删除部门负责人关联
     * @param id 关联记录ID
     */
    void deleteById(Integer id);
    
    /**
     * 删除指定部门的指定负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     */
    void removeDepartmentLeader(Integer departmentId, Integer employeeId);
    
    /**
     * 更新部门负责人信息
     * @param departmentLeader 部门负责人信息
     */
    void update(DepartmentLeader departmentLeader);
    
    /**
     * 根据ID查询部门负责人信息
     * @param id 关联记录ID
     * @return 部门负责人信息
     */
    DepartmentLeader getById(Integer id);
    
    // ========== 业务查询方法 ==========
    
    /**
     * 查询部门的所有负责人
     * @param departmentId 部门ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 部门负责人列表
     */
    List<DepartmentLeader> getDepartmentLeaders(Integer departmentId, Boolean includeInactive);
    
    /**
     * 查询部门的所有激活状态负责人
     * @param departmentId 部门ID
     * @return 部门负责人列表
     */
    List<DepartmentLeader> getActiveDepartmentLeaders(Integer departmentId);
    
    /**
     * 查询员工负责的所有部门
     * @param employeeId 员工ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 部门负责人列表
     */
    List<DepartmentLeader> getEmployeeDepartments(Integer employeeId, Boolean includeInactive);
    
    /**
     * 查询员工负责的所有激活状态部门
     * @param employeeId 员工ID
     * @return 部门负责人列表
     */
    List<DepartmentLeader> getActiveEmployeeDepartments(Integer employeeId);
    
    /**
     * 查询部门的主要负责人
     * @param departmentId 部门ID
     * @return 主要负责人信息，如果没有则返回null
     */
    DepartmentLeader getPrimaryLeader(Integer departmentId);
    
    /**
     * 查询部门的所有激活状态负责人ID列表
     * @param departmentId 部门ID
     * @return 负责人ID列表
     */
    List<Integer> getActiveLeaderIds(Integer departmentId);
    
    /**
     * 查询员工负责的所有激活状态部门ID列表
     * @param employeeId 员工ID
     * @return 部门ID列表
     */
    List<Integer> getActiveDepartmentIds(Integer employeeId);
    
    // ========== 权限检查方法 ==========
    
    /**
     * 检查员工是否为指定部门的负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @return true-是负责人，false-不是负责人
     */
    boolean isLeader(Integer departmentId, Integer employeeId);
    
    /**
     * 检查员工是否为指定部门的主要负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @return true-是主要负责人，false-不是主要负责人
     */
    boolean isPrimaryLeader(Integer departmentId, Integer employeeId);
    
    /**
     * 检查员工是否为任何部门的负责人
     * @param employeeId 员工ID
     * @return true-是负责人，false-不是负责人
     */
    boolean isAnyDepartmentLeader(Integer employeeId);
    
    // ========== 批量操作方法 ==========
    
    /**
     * 批量添加部门负责人
     * @param departmentLeaders 部门负责人列表
     */
    void batchAdd(List<DepartmentLeader> departmentLeaders);
    
    /**
     * 设置部门负责人（替换现有的所有负责人）
     * @param departmentId 部门ID
     * @param leaderIds 新的负责人ID列表
     * @param primaryLeaderId 主要负责人ID
     */
    void setDepartmentLeaders(Integer departmentId, List<Integer> leaderIds, Integer primaryLeaderId);
    
    /**
     * 更新部门负责人的激活状态
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @param isActive 激活状态
     */
    void updateActiveStatus(Integer departmentId, Integer employeeId, Boolean isActive);
    
    // ========== 兼容性方法（用于向后兼容） ==========
    
    /**
     * 获取部门的主要负责人ID（兼容原有的leaderId字段）
     * @param departmentId 部门ID
     * @return 主要负责人ID，如果没有则返回null
     */
    Integer getPrimaryLeaderId(Integer departmentId);
    
    /**
     * 设置部门的主要负责人（兼容原有的leaderId字段）
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     */
    void setPrimaryLeader(Integer departmentId, Integer employeeId);
}
