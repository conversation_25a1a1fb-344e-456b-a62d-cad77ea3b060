package org.example.company_management.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * {{CHENGQI: 内容处理工具类}}
 * {{CHENGQI: 任务ID: P4-LD-009}}
 * {{CHENGQI: 负责人: LD}}
 * {{CHENGQI: 创建时间: 2025-01-27 17:35:00 +08:00}}
 * {{CHENGQI: 描述: 富文本内容处理工具，提供HTML内容清理、摘要提取等功能}}
 */
@Slf4j
public class ContentProcessingUtils {

    // HTML标签匹配正则表达式
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    
    // 多个空白字符匹配正则表达式
    private static final Pattern MULTIPLE_WHITESPACE_PATTERN = Pattern.compile("\\s+");
    
    // HTML实体字符映射
    private static final String[][] HTML_ENTITIES = {
        {"&nbsp;", " "},
        {"&lt;", "<"},
        {"&gt;", ">"},
        {"&amp;", "&"},
        {"&quot;", "\""},
        {"&#39;", "'"},
        {"&apos;", "'"}
    };

    /**
     * 从HTML内容中提取纯文本摘要
     * 
     * @param htmlContent HTML内容
     * @param maxLength 最大长度
     * @return 纯文本摘要
     */
    public static String extractTextSummary(String htmlContent, int maxLength) {
        if (!StringUtils.hasText(htmlContent)) {
            return "";
        }

        try {
            // 1. 移除HTML标签
            String textContent = removeHtmlTags(htmlContent);
            
            // 2. 解码HTML实体
            textContent = decodeHtmlEntities(textContent);
            
            // 3. 清理空白字符
            textContent = cleanWhitespace(textContent);
            
            // 4. 截取指定长度
            if (textContent.length() > maxLength) {
                textContent = textContent.substring(0, maxLength);
                // 避免在单词中间截断
                int lastSpace = textContent.lastIndexOf(' ');
                if (lastSpace > maxLength * 0.8) { // 如果最后一个空格位置合理
                    textContent = textContent.substring(0, lastSpace);
                }
                textContent += "...";
            }
            
            return textContent.trim();
        } catch (Exception e) {
            log.warn("提取文本摘要失败，使用原始内容: {}", e.getMessage());
            // 如果处理失败，返回原始内容的截取
            String fallback = htmlContent.replaceAll("<[^>]+>", "").trim();
            return fallback.length() > maxLength ? 
                fallback.substring(0, maxLength) + "..." : fallback;
        }
    }

    /**
     * 移除HTML标签
     * 
     * @param htmlContent HTML内容
     * @return 移除标签后的文本
     */
    public static String removeHtmlTags(String htmlContent) {
        if (!StringUtils.hasText(htmlContent)) {
            return "";
        }
        
        // 先处理一些特殊的HTML标签，转换为相应的文本
        String processed = htmlContent
            .replaceAll("(?i)<br\\s*/?>", "\n")           // <br> 转换为换行
            .replaceAll("(?i)<p[^>]*>", "\n")             // <p> 开始标签转换为换行
            .replaceAll("(?i)</p>", "\n")                 // </p> 结束标签转换为换行
            .replaceAll("(?i)<div[^>]*>", "\n")           // <div> 开始标签转换为换行
            .replaceAll("(?i)</div>", "\n")               // </div> 结束标签转换为换行
            .replaceAll("(?i)<li[^>]*>", "\n• ")          // <li> 转换为列表项
            .replaceAll("(?i)</li>", "\n");               // </li> 转换为换行
        
        // 移除所有剩余的HTML标签
        return HTML_TAG_PATTERN.matcher(processed).replaceAll("");
    }

    /**
     * 解码HTML实体字符
     * 
     * @param text 包含HTML实体的文本
     * @return 解码后的文本
     */
    public static String decodeHtmlEntities(String text) {
        if (!StringUtils.hasText(text)) {
            return "";
        }
        
        String result = text;
        for (String[] entity : HTML_ENTITIES) {
            result = result.replace(entity[0], entity[1]);
        }
        return result;
    }

    /**
     * 清理多余的空白字符
     * 
     * @param text 原始文本
     * @return 清理后的文本
     */
    public static String cleanWhitespace(String text) {
        if (!StringUtils.hasText(text)) {
            return "";
        }
        
        return MULTIPLE_WHITESPACE_PATTERN.matcher(text.trim()).replaceAll(" ");
    }

    /**
     * 检测内容是否为HTML格式
     * 
     * @param content 内容
     * @return 是否为HTML格式
     */
    public static boolean isHtmlContent(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }
        
        // 简单检测：包含HTML标签则认为是HTML内容
        return HTML_TAG_PATTERN.matcher(content).find();
    }

    /**
     * 清理和验证HTML内容
     * 
     * @param htmlContent HTML内容
     * @return 清理后的HTML内容
     */
    public static String sanitizeHtmlContent(String htmlContent) {
        if (!StringUtils.hasText(htmlContent)) {
            return "";
        }
        
        // 基础的HTML清理（可以根据需要扩展）
        String sanitized = htmlContent
            // 移除潜在的危险标签
            .replaceAll("(?i)<script[^>]*>.*?</script>", "")
            .replaceAll("(?i)<style[^>]*>.*?</style>", "")
            .replaceAll("(?i)<iframe[^>]*>.*?</iframe>", "")
            .replaceAll("(?i)<object[^>]*>.*?</object>", "")
            .replaceAll("(?i)<embed[^>]*>.*?</embed>", "")
            // 移除事件处理属性
            .replaceAll("(?i)\\s*on\\w+\\s*=\\s*[\"'][^\"']*[\"']", "")
            .replaceAll("(?i)\\s*javascript\\s*:", "");
        
        return sanitized.trim();
    }

    /**
     * 将纯文本转换为基础HTML格式
     * 
     * @param textContent 纯文本内容
     * @return HTML格式内容
     */
    public static String convertTextToHtml(String textContent) {
        if (!StringUtils.hasText(textContent)) {
            return "";
        }
        
        return textContent
            .replace("&", "&amp;")      // 转义&符号
            .replace("<", "&lt;")       // 转义<符号
            .replace(">", "&gt;")       // 转义>符号
            .replace("\"", "&quot;")    // 转义引号
            .replace("\n", "<br/>")     // 换行转换为<br>
            .replace("\r", "");         // 移除回车符
    }

    /**
     * 获取内容的字符数（不包含HTML标签）
     * 
     * @param content 内容（可能包含HTML）
     * @return 实际字符数
     */
    public static int getTextLength(String content) {
        if (!StringUtils.hasText(content)) {
            return 0;
        }
        
        if (isHtmlContent(content)) {
            return removeHtmlTags(content).length();
        } else {
            return content.length();
        }
    }

    /**
     * 验证内容长度是否符合要求
     * 
     * @param content 内容
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否符合要求
     */
    public static boolean validateContentLength(String content, int minLength, int maxLength) {
        int textLength = getTextLength(content);
        return textLength >= minLength && textLength <= maxLength;
    }
}

// {{CHENGQI: 内容处理工具类创建完成}}
