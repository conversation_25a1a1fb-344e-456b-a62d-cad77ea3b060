import request from '@/utils/request'

/**
 * 获取员工列表（支持分页和搜索）
 * @param {Object} params 查询参数
 * @param {number} [params.pageNum] 页码，默认为1
 * @param {number} [params.pageSize] 每页大小，默认为100，最大100
 * @param {string} [params.name] 员工姓名模糊搜索，可选
 * @returns {Promise<any>}
 */
export function getEmployeeList(params = {}) {
	return request({
		url: '/employee/list',
		method: 'get',
		params,
	})
}

/**
 * 分页查询员工
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.name] 员工姓名(可选)
 * @param {number} [params.departmentId] 部门ID(可选)
 * @returns {Promise<any>}
 */
export function getEmployeePage(params) {
	return request({
		url: '/employee/page',
		method: 'get',
		params,
	})
}

/**
 * 根据ID查询员工
 * @param {number} id 员工ID
 * @returns {Promise<any>}
 */
export function getEmployeeById(id) {
	return request({
		url: `/employee/${id}`,
		method: 'get',
	})
}

/**
 * 根据部门ID查询员工
 * @param {number} departmentId 部门ID
 * @returns {Promise<any>}
 */
export function getEmployeesByDepartmentId(departmentId) {
	return request({
		url: `/employee/department/${departmentId}`,
		method: 'get',
	})
}

/**
 * 新增员工
 * @param {Object} data 员工信息
 * @returns {Promise<any>}
 */
export function addEmployee(data) {
	return request({
		url: '/employee/add',
		method: 'post',
		data,
	})
}

/**
 * 更新员工
 * @param {Object} data 员工信息
 * @returns {Promise<any>}
 */
export function updateEmployee(data) {
	return request({
		url: '/employee/update',
		method: 'put',
		data,
	})
}

/**
 * 删除员工
 * @param {number} id 员工ID
 * @returns {Promise<any>}
 */
export function deleteEmployee(id) {
	return request({
		url: `/employee/${id}`,
		method: 'delete',
	})
}

/**
 * 更新员工状态
 * @param {number} id 员工ID
 * @param {string} status 状态
 * @returns {Promise<any>}
 */
export function updateEmployeeStatus(id, status) {
	return request({
		url: '/employee/status',
		method: 'put',
		params: { id, status },
	})
}

/**
 * 根据姓名搜索员工
 * @param {string} name 员工姓名(模糊搜索)
 * @returns {Promise<any>}
 */
export function searchEmployeeByName(name) {
	return request({
		url: '/employee/page',
		method: 'get',
		params: {
			pageNum: 1,
			pageSize: 10,
			name: name,
		},
	})
}

// /**
//  * 获取所有员工（用于下拉选择）
//  * @returns {Promise} - 返回请求的Promise对象
//  */
// export function getAllEmployees() {
// 	return request({
// 		url: '/employee/list',
// 		method: 'get',
// 	})
// }

// /**
//  * 批量导入员工
//  * @param {Object} data - 员工数据文件
//  * @returns {Promise} - 返回请求的Promise对象
//  */
// export function batchImportEmployees(data) {
// 	return request({
// 		url: '/employee/batch/import',
// 		method: 'post',
// 		data,
// 		headers: {
// 			'Content-Type': 'multipart/form-data',
// 		},
// 	})
// }

// /**
//  * 导出员工数据
//  * @param {Object} params - 导出过滤参数
//  * @returns {Promise} - 返回请求的Promise对象
//  */
// export function exportEmployees(params) {
// 	return request({
// 		url: '/employee/export',
// 		method: 'get',
// 		params,
// 		responseType: 'blob',
// 	})
// }

/**
 * 获取员工统计数据
 * @returns {Promise} - 返回请求的Promise对象
 */
export function getEmployeeStats() {
	return request({
		url: '/employee/stats',
		method: 'get',
	})
}
