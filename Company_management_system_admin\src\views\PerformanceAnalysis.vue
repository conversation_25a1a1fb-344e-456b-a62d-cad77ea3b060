<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Calendar,
    Loading,
    Upload,
} from '@element-plus/icons-vue';
import {
    getPerformancePage,
    addPerformance,
    updatePerformance,
    deletePerformance,
    getPerformanceById,
    batchDeletePerformance,
    importPerformanceExcel,
} from '@/api/performance';
import { getDepartmentList } from '@/api/department';
import { getEmployeeList, getEmployeesByDepartmentId, getEmployeeById, getEmployeePage } from '@/api/employee';
import ExcelImportDialog from '@/components/ExcelImportDialog.vue';
import ImportErrorsDialog from '@/components/ImportErrorsDialog.vue';

// 业绩数据
const performanceData = ref([]);
const loading = ref(true);
const searchEmployeeName = ref('');
const searchDepartmentId = ref('');
const searchYearMonth = ref('');

// 部门列表和员工列表
const departmentList = ref([]);
const employeeList = ref([]);
const loadingDepartments = ref(false);
const loadingEmployees = ref(false);
const formDepartmentId = ref(''); // 用于部门选择框的绑定值

// 员工选择分页相关
const employeeSearchQuery = ref('');
const employeeCurrentPage = ref(1);
const employeeHasMore = ref(true);
const employeePageSize = ref(100);

// 选中的记录
const selectedPerformances = ref([]);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    id: null,
    employeeId: null,
    employeeName: '',
    departmentId: null,
    department: '',
    position: '',
    date: '',
    estimatedPerformance: 0,
    actualPerformance: 0,
});

// 表单规则
const rules = {
    // 部门不再是必填项，会从员工信息中自动获取
    employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
    date: [{ required: true, message: '请选择年月', trigger: 'change' }],
    estimatedPerformance: [
        { required: true, message: '请输入预估业绩', trigger: 'blur' },
        { type: 'number', message: '预估业绩必须为数字', trigger: 'blur' },
    ],
    actualPerformance: [
        { required: true, message: '请输入实际业绩', trigger: 'blur' },
        { type: 'number', message: '实际业绩必须为数字', trigger: 'blur' },
    ],
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data;
        } else {
            ElMessage.error(res.message || '获取部门列表失败');
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 处理部门变更
const handleDepartmentChange = (departmentId) => {
    // 当部门变更时，清空员工选择
    form.employeeId = '';
    form.position = '';

    // 同步设置 form.departmentId
    form.departmentId = departmentId;

    // 重置员工选择相关状态
    employeeList.value = [];
    employeeSearchQuery.value = '';
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;

    // 如果选择了部门，自动加载该部门的员工
    if (departmentId) {
        loadEmployees('', 1, false);
    }
};

// 加载员工数据（支持分页和搜索，支持部门过滤）
const loadEmployees = async (query = '', page = 1, append = false) => {
    // 确保部门列表已加载
    if (departmentList.value.length === 0) {
        await loadDepartmentList();
    }

    loadingEmployees.value = true;
    try {
        // 根据是否选择了部门来决定使用哪个API
        let res;
        if (formDepartmentId.value) {
            // 如果选择了部门，使用分页API并传入部门ID
            res = await getEmployeePage({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined,
                departmentId: formDepartmentId.value
            });
        } else {
            // 如果没有选择部门，使用新的员工列表API获取所有员工
            res = await getEmployeeList({
                pageNum: page,
                pageSize: employeePageSize.value,
                name: query || undefined
            });
        }

        if (res.code === 200) {
            let employees = [];
            let total = 0;
            let hasMore = false;

            if (formDepartmentId.value) {
                // 部门过滤模式：使用分页API的返回格式
                employees = res.data.list || [];
                total = res.data.total || 0;
                hasMore = page * employeePageSize.value < total;
            } else {
                // 全员工模式：使用新API的返回格式
                const result = res.data;
                employees = result.employees || [];
                total = result.total || 0;
                hasMore = result.hasMore;
            }

            // 为每个员工添加部门名称和离职状态
            employees.forEach(emp => {
                if (emp.departmentId) {
                    const dept = departmentList.value.find(d =>
                        d.departmentId === emp.departmentId ||
                        d.departmentId === parseInt(emp.departmentId, 10));
                    emp.departmentName = dept ? dept.departmentName : '未知部门';
                } else {
                    emp.departmentName = '未知部门';
                }

                // 检查是否已离职
                emp.isResigned = !!(emp.exitDate);
            });

            // 根据append参数决定是追加还是替换
            if (append) {
                employeeList.value = [...employeeList.value, ...employees];
            } else {
                employeeList.value = employees;
            }

            // 更新分页状态
            employeeCurrentPage.value = page;
            employeeHasMore.value = hasMore;

        } else {
            ElMessage.error(res.message || '加载员工失败');
        }
    } catch (error) {
        ElMessage.error('加载员工失败: ' + (error.message || '未知错误'));
    } finally {
        loadingEmployees.value = false;
    }
};

// 加载更多员工数据
const loadMoreEmployees = async () => {
    if (!employeeHasMore.value || loadingEmployees.value) {
        return;
    }

    const nextPage = employeeCurrentPage.value + 1;
    await loadEmployees(employeeSearchQuery.value, nextPage, true);
};

// 处理下拉框滚动事件
const handleEmployeeScroll = (event) => {
    const { target } = event;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 当滚动到底部附近时（距离底部10px以内），加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10 && employeeHasMore.value && !loadingEmployees.value) {
        loadMoreEmployees();
    }
};

// 远程搜索员工（重构为使用新的加载方法）
const handleEmployeeRemoteSearch = async (query) => {
    // 重置搜索状态
    employeeSearchQuery.value = query;
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;

    if (!query) {
        // 如果没有查询内容，清空选项
        employeeList.value = [];
        return;
    }

    // 加载第一页数据
    await loadEmployees(query, 1, false);
};

// 处理员工选择框可见性变化
const handleEmployeeSelectVisibleChange = (visible) => {
    if (visible && employeeList.value.length === 0) {
        // 当下拉框打开且没有数据时，根据是否选择部门来加载数据
        if (formDepartmentId.value) {
            // 如果选择了部门，加载该部门的员工
            loadEmployees('', 1, false);
        } else {
            // 如果没有选择部门，加载所有员工的前100条
            loadEmployees('', 1, false);
        }
    }
};

// 处理员工变更（增加部门自动填充功能）
const handleEmployeeChange = (employeeId) => {
    if (!employeeId) {
        form.position = '';
        return;
    }

    // 查找选中的员工信息
    const selectedEmployee = employeeList.value.find(
        (emp) => emp.employeeId === employeeId
    );
    if (selectedEmployee) {
        // 自动填充职位
        form.position = selectedEmployee.positionName || '';

        // 记录员工的实际部门信息用于提交，但不自动切换界面显示的部门
        if (selectedEmployee.departmentId) {
            form.departmentId = selectedEmployee.departmentId;
        }
    }
};

// 加载业绩数据
const loadPerformanceData = async () => {
    loading.value = true;
    performanceData.value = [];

    try {
        const params = {
            page: pagination.page,
            size: pagination.size,
            employeeName: searchEmployeeName.value || undefined,
            departmentId: searchDepartmentId.value || undefined,
            yearMonth: searchYearMonth.value || undefined,
        };

        const res = await getPerformancePage(params);

        if (res.code === 200) {
            performanceData.value = res.data.records || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取业绩数据失败');
        }
    } catch (error) {
        ElMessage.error('加载业绩数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.employeeId = null;
    form.employeeName = '';
    form.departmentId = null;
    form.department = '';
    form.position = '';
    form.date = '';
    form.estimatedPerformance = 0;
    form.actualPerformance = 0;

    // 重置部门选择框绑定值
    formDepartmentId.value = '';

    // 重置员工选择相关状态
    employeeList.value = [];
    employeeSearchQuery.value = '';
    employeeCurrentPage.value = 1;
    employeeHasMore.value = true;
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    try {
        // 确保部门列表已加载，只在列表为空时加载
        if (departmentList.value.length === 0) {
            await loadDepartmentList();
        }
        
        // 用后端数据填充表单
        const res = await getPerformanceById(row.id);
        if (res.code === 200) {
            const data = res.data;
            
            // 先设置其他属性
            form.id = data.id;
            form.employeeId = data.employeeId;
            form.employeeName = data.employeeName;
            form.department = data.department; // 保存部门名称
            form.position = data.position;
            form.date = data.date;
            form.estimatedPerformance = data.estimatedPerformance;
            form.actualPerformance = data.actualPerformance;

            // 根据部门名称查找部门ID
            let departmentId = null;
            if (data.department) {
                const foundDepartment = departmentList.value.find(
                    dept => dept.departmentName === data.department
                );
                if (foundDepartment) {
                    departmentId = foundDepartment.departmentId;
                }
            }
            
            // 如果API返回了departmentId，则优先使用
            if (data.departmentId) {
                departmentId = typeof data.departmentId === 'string' 
                    ? parseInt(data.departmentId, 10) 
                    : data.departmentId;
            }
                
            // 设置部门ID
            form.departmentId = departmentId;
            formDepartmentId.value = departmentId;
            
            // 如果没有找到部门ID，重新加载部门列表
            if (!departmentId && data.department) {
                console.warn(`部门 "${data.department}" 在部门列表中不存在，正在重新加载部门列表...`);
                await loadDepartmentList();
                
                // 重新尝试查找部门
                const deptAfterReload = departmentList.value.find(
                    dept => dept.departmentName === data.department
                );
                if (deptAfterReload) {
                    departmentId = deptAfterReload.departmentId;
                    form.departmentId = departmentId;
                    formDepartmentId.value = departmentId;
                }
            }
            
            // 直接使用业绩数据构造员工对象，无需再调用API
            if (data.employeeId) {
                // 构造与远程搜索结果格式一致的员工对象
                employeeList.value = [{
                    employeeId: data.employeeId,
                    name: data.employeeName,
                    positionName: data.position,
                    departmentId: departmentId
                }];
            }

            // 对话框显示
            dialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取业绩详情失败');
        }
    } catch (error) {
        ElMessage.error('获取业绩详情失败: ' + (error.message || '未知错误'));
    }
};

// 确认删除业绩记录
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除员工 "${row.employeeName}" 在 "${row.date}" 的业绩记录吗？此操作不可撤销！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deletePerformance(row.id);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                        duration: 2000,
                    });
                    loadPerformanceData();
                } else {
                    ElMessage.error(res.message || '删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error('删除失败: ' + (error.message || '未知错误'));
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedPerformances.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }

    const ids = selectedPerformances.value.map((item) => item.id);
    const names = selectedPerformances.value
        .map((item) => `${item.employeeName}(${item.date})`)
        .join('、');

    ElMessageBox.confirm(
        `确定要删除以下员工的业绩记录吗？\n${names}\n此操作不可撤销！`,
        '批量删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await batchDeletePerformance(ids);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '批量删除成功',
                        duration: 2000,
                    });
                    selectedPerformances.value = [];
                    loadPerformanceData();
                } else {
                    ElMessage.error(res.message || '批量删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error(
                    '批量删除失败: ' + (error.message || '未知错误')
                );
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            // 检查是否有部门信息（从员工信息中获取）
            if (!form.departmentId) {
                ElMessage.error('无法获取员工部门信息，请重新选择员工');
                return false;
            }

            formLoading.value = true;
            try {
                // 准备提交的数据
                const submitData = {
                    id: form.id, // 编辑时需要ID
                    employeeId: form.employeeId,
                    date: form.date,
                    estimatedPerformance: form.estimatedPerformance,
                    actualPerformance: form.actualPerformance,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 新增业绩记录
                    res = await addPerformance(submitData);
                } else {
                    // 更新业绩记录
                    res = await updatePerformance(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加成功'
                                : '更新成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadPerformanceData();
                } else {
                    ElMessage.error(
                        res.message ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败')
                    );
                }
            } catch (error) {
                ElMessage.error('提交失败: ' + (error.message || '未知错误'));
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

// 表格选中行变化
const handleSelectionChange = (selection) => {
    selectedPerformances.value = selection;
};

// 搜索
const handleSearch = () => {
    pagination.page = 1;
    loadPerformanceData();
};

// 重置搜索
const handleReset = () => {
    searchEmployeeName.value = '';
    searchDepartmentId.value = '';
    searchYearMonth.value = '';
    pagination.page = 1;
    loadPerformanceData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadPerformanceData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadPerformanceData();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 导入Excel对话框控制
const showImportDialog = ref(false);
const showImportErrorsDialog = ref(false);
const currentImportResult = ref(null);

// 打开导入对话框
const handleOpenImportDialog = () => {
    showImportDialog.value = true;
};

// 处理Excel导入成功事件 (替代旧的 handleImportSubmit)
const handlePerformanceImportSuccess = (importResult, importType) => {
    // Ensure this handler is specific to performance if needed, or make it generic
    if (importType === 'performance') { 
        currentImportResult.value = importResult;
        loading.value = true; // Keep loading state or manage it based on importResult
        try {
            if (importResult && (importResult.failureCount > 0 || (importResult.generalErrors && importResult.generalErrors.length > 0))) {
                // Message is already shown by ExcelImportDialog, just open error details
                showImportErrorsDialog.value = true;
            } else {
                // Message is already shown by ExcelImportDialog
                // ElMessage.success('业绩数据导入成功'); // Already handled by component
                showImportDialog.value = false; // Close import dialog if open, though it closes itself
            }
            loadPerformanceData(); // Refresh data regardless of partial success or full success
        } catch (error) {
            ElMessage.error('处理导入结果时发生错误: ' + (error.message || '未知错误'));
            currentImportResult.value = null;
        } finally {
            loading.value = false;
        }
    }
};

// Formatter functions for performance import messages
const formatPerformanceSuccessMsg = (importResult) => {
    return `业绩数据全部导入成功！共处理 ${importResult.processedRows || 0} 行，成功导入 ${importResult.successCount || 0} 条记录。`;
};

const formatPerformancePartialSuccessMsg = (importResult) => {
    let msg = `业绩数据导入处理完成。共处理 ${importResult.processedRows || 0} 行，成功 ${importResult.successCount || 0} 行，失败/跳过 ${importResult.failureCount || 0} 行。`;
    if (importResult.generalErrors && importResult.generalErrors.length > 0) {
        msg += ` 通用错误: ${importResult.generalErrors.join('; ')}`;
    }
    return msg;
};

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadPerformanceData();
});
</script>

<template>
    <div class="performance-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>

                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    :loading="loadingDepartments"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>

                <el-date-picker
                    v-model="searchYearMonth"
                    type="month"
                    placeholder="选择年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                />

                <el-button
                    type="primary"
                    @click="handleSearch"
                >
                    <el-icon>
                        <Search />
                    </el-icon>搜索
                </el-button>

                <el-button @click="handleReset">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>

            <div class="action-box">
                <el-button
                    type="danger"
                    :disabled="selectedPerformances.length === 0"
                    @click="handleBatchDelete"
                >
                    <el-icon>
                        <Delete />
                    </el-icon>批量删除
                </el-button>

                <el-button
                    type="primary"
                    class="add-btn"
                    @click="handleAdd"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加业绩
                </el-button>

                <el-button
                type="success"
                @click="handleOpenImportDialog"
            >
                <el-icon>
                    <Upload />
                </el-icon>导入业绩
            </el-button>
            </div>
        </div>

        <!-- 业绩表格 -->
        <el-table
            v-loading="loading"
            :data="performanceData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            />

            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="100"
                show-overflow-tooltip
            />

            <el-table-column
                label="部门"
                prop="department"
                min-width="120"
                show-overflow-tooltip
            />

            <el-table-column
                label="职位"
                prop="position"
                min-width="120"
                show-overflow-tooltip
            />

            <el-table-column
                label="年月"
                prop="date"
                width="100"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag type="info">{{ row.date }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="预估业绩"
                prop="estimatedPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.estimatedPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实际业绩"
                prop="actualPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月备用金"
                prop="totalPettyCash"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalPettyCash !== null && row.totalPettyCash !== undefined">
                        {{ formatCurrency(row.totalPettyCash) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月发布工资"
                prop="totalSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalSalary !== null && row.totalSalary !== undefined">
                        {{ formatCurrency(row.totalSalary) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月平均部门开销"
                prop="averageDepartmentExpense"
                min-width="170"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.averageDepartmentExpense !== null && row.averageDepartmentExpense !== undefined">
                        {{ formatCurrency(row.averageDepartmentExpense) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月员工费用"
                prop="totalEmployeeOtherExpenses"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalEmployeeOtherExpenses !== null && row.totalEmployeeOtherExpenses !== undefined">
                        {{ formatCurrency(row.totalEmployeeOtherExpenses) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月预计盈亏"
                prop="estimatedMonthlyProfitLoss"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.estimatedMonthlyProfitLoss !== null && row.estimatedMonthlyProfitLoss !== undefined">
                        {{ formatCurrency(row.estimatedMonthlyProfitLoss) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月实际盈亏"
                prop="actualMonthlyProfitLoss"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.actualMonthlyProfitLoss !== null && row.actualMonthlyProfitLoss !== undefined">
                        {{ formatCurrency(row.actualMonthlyProfitLoss) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="150"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加业绩记录' : '编辑业绩记录'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="部门"
                    prop="departmentId"
                >
                    <el-select
                        v-model="formDepartmentId"
                        placeholder="选择部门（可选，用于筛选员工）"
                        :loading="loadingDepartments"
                        @change="handleDepartmentChange"
                        clearable
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                    <div class="el-form-item__help">
                        <span class="help-text">
                        </span>
                    </div>
                </el-form-item>

                <el-form-item
                    label="员工"
                    prop="employeeId"
                    required
                >
                    <el-select
                        v-model="form.employeeId"
                        :placeholder="formDepartmentId ? '点击下拉选择部门员工或输入姓名搜索' : '点击下拉选择员工或输入姓名搜索'"
                        @change="handleEmployeeChange"
                        remote
                        filterable
                        :remote-method="handleEmployeeRemoteSearch"
                        :loading="loadingEmployees"
                        clearable
                        style="width: 100%"
                        popper-class="employee-select-dropdown"
                        @visible-change="handleEmployeeSelectVisibleChange"
                    >
                        <template #empty>
                            <div class="empty-text">
                                <p v-if="!employeeSearchQuery && employeeList.length === 0">
                                    {{ formDepartmentId ? '点击下拉加载部门员工列表' : '点击下拉加载员工列表' }}
                                </p>
                                <p v-else-if="employeeSearchQuery && employeeList.length === 0">
                                    {{ formDepartmentId ? '该部门未找到匹配的员工' : '未找到匹配的员工' }}
                                </p>
                                <p v-else>暂无数据</p>
                            </div>
                        </template>

                        <!-- 员工选项列表 -->
                        <div
                            v-if="employeeList.length > 0"
                            class="employee-options-container"
                            @scroll="handleEmployeeScroll"
                        >
                            <el-option
                                v-for="employee in employeeList"
                                :key="employee.employeeId"
                                :label="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : '')) + (employee.isResigned ? '（已离职）' : '')"
                                :value="employee.employeeId"
                                :class="{ 'resigned-employee': employee.isResigned }"
                            />

                            <!-- 加载更多提示 -->
                            <div
                                v-if="employeeHasMore"
                                class="load-more-option"
                                @click="loadMoreEmployees"
                            >
                                <el-icon v-if="loadingEmployees" class="is-loading">
                                    <Loading />
                                </el-icon>
                                <span>{{ loadingEmployees ? '加载中...' : '滚动或点击加载更多' }}</span>
                            </div>

                            <!-- 没有更多数据提示 -->
                            <div v-else class="no-more-data">
                                <span>已显示全部 {{ employeeList.length }} 个员工</span>
                            </div>
                        </div>
                    </el-select>
                    <div class="el-form-item__help">
                        <span v-if="employeeList.length > 0" class="help-text">
                            {{ formDepartmentId ? '已显示该部门' : '已显示' }} {{ employeeList.length }} 个员工{{ employeeHasMore ? '，滚动或点击加载更多' : '' }}
                        </span>
                        <span v-else-if="formDepartmentId" class="help-text">
                            当前显示：{{ getDepartmentName(formDepartmentId) }} 部门员工
                        </span>
                        <span v-else class="help-text">
                        </span>
                    </div>
                </el-form-item>

                <el-form-item
                    label="年月"
                    prop="date"
                    required
                >
                    <el-date-picker
                        v-model="form.date"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="预估业绩"
                    prop="estimatedPerformance"
                    required
                >
                    <el-input-number
                        v-model="form.estimatedPerformance"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="实际业绩"
                    prop="actualPerformance"
                    required
                >
                    <el-input-number
                        v-model="form.actualPerformance"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                    />
                </el-form-item>
            </el-form>

            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- Excel导入对话框 -->
        <ExcelImportDialog
            v-if="showImportDialog" 
            :model-value="showImportDialog"
            @update:modelValue="showImportDialog = $event"
            importType="performance" 
            dialogTitle="导入业绩数据"
            templateFileName="业绩导入模板.xlsx" 
            uploadUrl="/api/performance/import" 
            :maxFileSizeMB="200" 
            @import-success="handlePerformanceImportSuccess"
            :successMessageFormatter="formatPerformanceSuccessMsg"
            :partialSuccessMessageFormatter="formatPerformancePartialSuccessMsg"
        />

        <!-- 导入错误详情对话框 -->
        <ImportErrorsDialog 
            v-model="showImportErrorsDialog" 
            :import-result="currentImportResult" 
            title="业绩导入结果"
        />
    </div>
</template>

<style scoped>
.performance-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input,
.search-box .el-select,
.search-box .el-date-picker {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

.progress-column {
    padding-right: 10px;
}

:deep(.el-progress) {
    margin: 0 auto;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.completion-rate-text {
    font-size: 12px;
    color: #67c23a;
    font-weight: bold;
    margin-top: 4px;
}

/* 添加总工资样式 */
:deep(.el-tag.el-tag--info) {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
}

.loading-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: #909399;
    font-size: 14px;
}

.loading-text .el-icon {
    animation: rotating 2s linear infinite;
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 员工选择相关样式 */
.empty-text {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 10px;
    margin: 0;
}

.help-text {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}

.employee-options-container {
    max-height: 300px;
    overflow-y: auto;
}

.load-more-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    color: #409eff;
    cursor: pointer;
    border-top: 1px solid #ebeef5;
    transition: background-color 0.3s;
    background-color: #fafafa;
}

.load-more-option:hover {
    background-color: #f0f9ff;
}

.load-more-option .el-icon {
    margin-right: 4px;
}

.load-more-option .is-loading {
    animation: rotating 2s linear infinite;
}

.no-more-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    color: #909399;
    font-size: 12px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
}

:deep(.employee-select-dropdown) {
    max-height: 350px;
}

:deep(.employee-select-dropdown .el-select-dropdown__list) {
    padding: 0;
}

:deep(.resigned-employee) {
    color: #909399;
    background-color: #f5f7fa;
}

:deep(.resigned-employee:hover) {
    background-color: #ecf5ff;
}
</style> 