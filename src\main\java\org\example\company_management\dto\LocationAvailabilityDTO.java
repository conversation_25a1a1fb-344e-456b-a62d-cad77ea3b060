package org.example.company_management.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 地点可用性查询结果DTO
 * 用于返回地点的开放时间、已预订时间段等信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocationAvailabilityDTO {
    
    /**
     * 地点ID
     */
    private Integer locationId;
    
    /**
     * 地点名称
     */
    private String locationName;
    
    /**
     * 地点状态
     */
    private String status;
    
    /**
     * 开放开始时间
     */
    private LocalTime openTime;
    
    /**
     * 开放结束时间
     */
    private LocalTime closeTime;
    
    /**
     * 可用星期（1-7，逗号分隔）
     */
    private String availableDays;
    
    /**
     * 可用星期列表（解析后的）
     */
    private List<Integer> availableDaysList;

    /**
     * 开放开始日期（可选，NULL表示无限制）
     */
    private LocalDate availableStartDate;

    /**
     * 开放结束日期（可选，NULL表示无限制）
     */
    private LocalDate availableEndDate;

    /**
     * 已预订的时间段列表
     */
    private List<BookedTimeSlot> bookedTimeSlots;
    
    /**
     * 已预订时间段内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BookedTimeSlot {
        
        /**
         * 会议ID
         */
        private Long meetingId;
        
        /**
         * 会议主题
         */
        private String meetingTitle;
        
        /**
         * 开始时间
         */
        private LocalDateTime startTime;
        
        /**
         * 结束时间
         */
        private LocalDateTime endTime;
        
        /**
         * 预订人姓名
         */
        private String bookedByName;
        
        /**
         * 预订人ID
         */
        private Integer bookedById;
        
        /**
         * 是否为当前编辑的会议（编辑模式下使用）
         */
        private Boolean isCurrentMeeting;
    }
}
