package org.example.company_management.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 日期范围验证注解
 * 验证开始日期和结束日期的逻辑关系
 * 用于会议地点的可用日期范围验证
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateRangeValidator.class)
@Documented
public @interface ValidDateRange {
    
    String message() default "日期范围设置不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 开始日期字段名
     */
    String startDateField() default "availableStartDate";
    
    /**
     * 结束日期字段名
     */
    String endDateField() default "availableEndDate";
}
