package org.example.company_management.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.entity.Department;
import org.example.company_management.entity.DepartmentLeader;
import org.example.company_management.entity.Position;
import org.example.company_management.mapper.DepartmentMapper;
import org.example.company_management.service.DepartmentLeaderService;
import org.example.company_management.service.DepartmentService;
import org.example.company_management.service.EmployeeService;
import org.example.company_management.service.PositionService;
import org.example.company_management.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 部门服务实现
 */
@Slf4j
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private PositionService positionService;

    @Autowired
    private DepartmentLeaderService departmentLeaderService;

    @Override
    public List<Department> list() {
        return departmentMapper.selectAll();
    }

    @Override
    public PageResult<Department> pageList(int pageNum, int pageSize) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<Department> departments = departmentMapper.selectByPage(offset, pageSize);

        // 为每个部门加载负责人信息（用于列表显示）
        for (Department department : departments) {
            loadDepartmentLeadersForList(department);
        }

        // 查询总数
        int total = departmentMapper.countTotal();

        // 封装返回结果
        return new PageResult<>(pageNum, pageSize, total, departments);
    }

    @Override
    public PageResult<Department> pageList(int pageNum, int pageSize, String departmentName, Integer leaderId, String leaderName, Integer parentDepartmentId) {
        // 计算起始位置
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<Department> departments = departmentMapper.selectByPageAndCondition(
                offset, pageSize, departmentName, leaderId, leaderName, parentDepartmentId);

        // 为每个部门加载负责人信息（用于列表显示）
        for (Department department : departments) {
            loadDepartmentLeadersForList(department);
        }

        // 查询总数
        int total = departmentMapper.countTotalByCondition(departmentName, leaderId, leaderName, parentDepartmentId);

        // 封装返回结果
        return new PageResult<>(pageNum, pageSize, total, departments);
    }

//    /**
//     * 兼容旧版本的方法，调用新版本实现
//     */
//    @Override
//    public PageResult<Department> pageList(int pageNum, int pageSize, String departmentName, Integer leaderId, Integer parentDepartmentId) {
//        return pageList(pageNum, pageSize, departmentName, leaderId, null, parentDepartmentId);
//    }

    @Override
    public List<Department> search(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果关键词为空，返回所有部门
            return departmentMapper.selectAll();
        }
        return departmentMapper.selectByKeyword(keyword.trim());
    }

    @Override
    public Department getById(Integer id) {
        return departmentMapper.selectById(id);
    }

    @Override
    public Department getByIdWithLeaders(Integer id, Boolean includeLeaders) {
        if (id == null) {
            return null;
        }

        // 获取部门基本信息
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            return null;
        }

        // 如果需要包含负责人信息
        if (includeLeaders != null && includeLeaders) {
            // 获取部门的所有激活状态负责人
            List<DepartmentLeader> leaders = departmentLeaderService.getActiveDepartmentLeaders(id);

            // 只设置编辑表单需要的字段，避免冗余数据
            if (leaders != null && !leaders.isEmpty()) {
                // 设置完整的负责人列表（用于编辑表单）
                department.setLeaders(leaders);

                // 设置便捷访问字段
                List<Integer> leaderIds = new ArrayList<>();
                List<String> leaderNames = new ArrayList<>();
                Integer primaryLeaderId = null;
                String primaryLeaderName = null;

                for (DepartmentLeader leader : leaders) {
                    leaderIds.add(leader.getEmployeeId());
                    leaderNames.add(leader.getLeaderName());

                    // 找到主要负责人
                    if ("PRIMARY".equals(leader.getLeaderRole())) {
                        primaryLeaderId = leader.getEmployeeId();
                        primaryLeaderName = leader.getLeaderName();
                    }
                }

                department.setLeaderIds(leaderIds);
                department.setLeaderNames(leaderNames);
                department.setPrimaryLeaderId(primaryLeaderId);
                department.setPrimaryLeaderName(primaryLeaderName);

                // 兼容性：设置原有字段
                if (primaryLeaderId != null) {
                    department.setLeaderId(primaryLeaderId);
                    department.setDepartmentLeader(primaryLeaderName);
                }
            }
        }

        return department;
    }



    @Override
    @Transactional
    public void add(Department department) {
        // 校验部门名称
        if (department == null || !StringUtils.hasText(department.getDepartmentName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }

        // 检查部门名称是否已存在
        List<Department> departments = departmentMapper.selectAll();
        for (Department existingDept : departments) {
            if (existingDept.getDepartmentName().equals(department.getDepartmentName())) {
                throw new IllegalArgumentException("部门名称已存在，请使用其他名称");
            }
        }

        // 设置默认状态
        if (!StringUtils.hasText(department.getStatus())) {
            department.setStatus("Active");
        }

        // 设置创建和更新时间
        Date now = new Date();
        department.setCreateTime(now);
        department.setUpdateTime(now);

        // 保存部门
        departmentMapper.insert(department);

        // 处理负责人信息
        if (department.getLeaderIds() != null && !department.getLeaderIds().isEmpty()) {
            // 使用新的多负责人方式
            departmentLeaderService.setDepartmentLeaders(
                department.getDepartmentId(),
                department.getLeaderIds(),
                department.getPrimaryLeaderId()
            );
            log.info("为新部门设置负责人：部门ID={}, 负责人数量={}",
                    department.getDepartmentId(), department.getLeaderIds().size());
        } else if (department.getLeaderId() != null) {
            // 兼容原有的单负责人方式
            departmentLeaderService.setPrimaryLeader(department.getDepartmentId(), department.getLeaderId());
            log.info("为新部门设置单个负责人：部门ID={}, 负责人ID={}",
                    department.getDepartmentId(), department.getLeaderId());
        }

        log.info("成功添加部门：{}", department.getDepartmentName());
    }

    @Override
    @Transactional
    public void update(Department department) {
        // 校验部门名称
        if (department == null || !StringUtils.hasText(department.getDepartmentName())) {
            throw new IllegalArgumentException("部门名称不能为空");
        }

        // 检查部门ID是否存在
        Department existingDept = departmentMapper.selectById(department.getDepartmentId());
        if (existingDept == null) {
            throw new IllegalArgumentException("部门不存在");
        }

        // 检查部门名称是否与其他部门重复
        List<Department> departments = departmentMapper.selectAll();
        for (Department dept : departments) {
            if (dept.getDepartmentName().equals(department.getDepartmentName()) &&
                    !dept.getDepartmentId().equals(department.getDepartmentId())) {
                throw new IllegalArgumentException("部门名称已存在，请使用其他名称");
            }
        }

        // 设置更新时间
        department.setUpdateTime(new Date());

        // 更新部门基本信息
        departmentMapper.update(department);

        // 处理负责人信息更新
        if (department.getLeaderIds() != null) {
            // 使用新的多负责人方式
            departmentLeaderService.setDepartmentLeaders(
                department.getDepartmentId(),
                department.getLeaderIds(),
                department.getPrimaryLeaderId()
            );
            log.info("更新部门负责人：部门ID={}, 负责人数量={}",
                    department.getDepartmentId(), department.getLeaderIds().size());
        } else if (department.getLeaderId() != null) {
            // 兼容原有的单负责人方式
            departmentLeaderService.setPrimaryLeader(department.getDepartmentId(), department.getLeaderId());
            log.info("更新部门单个负责人：部门ID={}, 负责人ID={}",
                    department.getDepartmentId(), department.getLeaderId());
        }

        log.info("成功更新部门：{}", department.getDepartmentName());
    }

    @Override
    public void delete(Integer id) {
        // 校验部门ID
        if (id == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        // 检查部门是否存在
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            throw new IllegalArgumentException("部门不存在，无法删除");
        }

        // 检查部门名称，防止删除"待分配"部门
        if ("待分配".equals(department.getDepartmentName())) {
            throw new IllegalArgumentException("无法删除'待分配'部门，因为它用于存放未分配员工");
        }

        // 检查是否有下级部门
        List<Department> subDepartments = departmentMapper.selectByParentId(id);
        if (subDepartments != null && !subDepartments.isEmpty()) {
            throw new IllegalArgumentException("该部门下有" + subDepartments.size() + "个下级部门，请先转移或删除这些下级部门后再删除");
        }

        // 检查部门下是否有员工
        if (department.getEmployeeCount() != null && department.getEmployeeCount() > 0) {
            throw new IllegalArgumentException("该部门下有" + department.getEmployeeCount() + "名员工，请先转移或删除这些员工后再删除部门");
        }

        // 检查部门下是否有职位
        List<Position> positions = positionService.getByDepartmentId(id);
        if (positions != null && !positions.isEmpty()) {
            throw new IllegalArgumentException("该部门下有" + positions.size() + "个职位，请先转移或删除这些职位后再删除部门");
        }

        // 执行删除操作
        int result = departmentMapper.deleteById(id);
        if (result <= 0) {
            throw new RuntimeException("删除部门失败，请稍后重试");
        }
    }

    /**
     * 获取或创建"待分配"部门
     *
     * @return 待分配部门ID
     */
    private Integer getOrCreateUnassignedDepartment() {
        // 查找是否已存在"待分配"部门
        List<Department> departments = departmentMapper.selectAll();
        for (Department dept : departments) {
            if ("待分配".equals(dept.getDepartmentName())) {
                return dept.getDepartmentId();
            }
        }

        // 如果不存在，创建一个
        Department unassignedDept = new Department();
        unassignedDept.setDepartmentName("待分配");
        unassignedDept.setLeaderId(null); // 使用null表示没有负责人
        unassignedDept.setDepartmentDescription("存放暂未分配部门的员工");
        unassignedDept.setStatus("Active");

        // 设置创建和更新时间
        Date now = new Date();
        unassignedDept.setCreateTime(now);
        unassignedDept.setUpdateTime(now);

        // 保存部门
        departmentMapper.insert(unassignedDept);

        return unassignedDept.getDepartmentId();
    }

    @Override
    public List<Department> getSubDepartments(Integer parentId) {
        return departmentMapper.selectByParentId(parentId);
    }

    @Override
    public List<Department> getDepartmentsByLeaderId(Integer leaderId) {
        if (leaderId == null) {
            return new ArrayList<>();
        }
        return departmentMapper.selectByLeaderId(leaderId);
    }

    // ========== 兼容性方法实现 ==========

    @Override
    public List<Integer> getManagedDepartmentIds(Integer employeeId) {
        return departmentLeaderService.getActiveDepartmentIds(employeeId);
    }

    // ========== 私有辅助方法 ==========

    /**
     * 为列表显示加载部门负责人信息（简化版）
     * 只加载必要的显示字段，避免过多的数据传输
     */
    private void loadDepartmentLeadersForList(Department department) {
        if (department == null || department.getDepartmentId() == null) {
            return;
        }

        try {
            // 获取部门的所有激活状态负责人
            List<DepartmentLeader> leaders = departmentLeaderService.getActiveDepartmentLeaders(department.getDepartmentId());

            if (leaders != null && !leaders.isEmpty()) {
                // 设置负责人ID和姓名列表
                List<Integer> leaderIds = new ArrayList<>();
                List<String> leaderNames = new ArrayList<>();
                Integer primaryLeaderId = null;
                String primaryLeaderName = null;

                for (DepartmentLeader leader : leaders) {
                    leaderIds.add(leader.getEmployeeId());
                    leaderNames.add(leader.getLeaderName());

                    // 找到主要负责人
                    if ("PRIMARY".equals(leader.getLeaderRole())) {
                        primaryLeaderId = leader.getEmployeeId();
                        primaryLeaderName = leader.getLeaderName();
                    }
                }

                // 设置便捷访问字段
                department.setLeaderIds(leaderIds);
                department.setLeaderNames(leaderNames);
                department.setPrimaryLeaderId(primaryLeaderId);
                department.setPrimaryLeaderName(primaryLeaderName);

                // 兼容性：更新原有字段
                if (primaryLeaderId != null) {
                    department.setLeaderId(primaryLeaderId);
                    department.setDepartmentLeader(primaryLeaderName);
                }
            }
        } catch (Exception e) {
            log.warn("加载部门负责人信息失败：部门ID={}, 错误={}", department.getDepartmentId(), e.getMessage());
            // 出错时不影响主流程，保持原有的兼容性字段
        }
    }
}