package org.example.company_management.config;

import lombok.extern.slf4j.Slf4j;
import org.example.company_management.interceptor.JwtAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * {{CHENGQI: 更新时间: 2025-06-13 10:30:14 +08:00}}
 * {{CHENGQI: 更新内容: 添加推广图片静态资源映射配置，支持动态路径配置}}
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private JwtAuthInterceptor jwtAuthInterceptor;

    @Value("${app.upload.path}")
    private String uploadPath;

    @Value("${app.upload.url-prefix}")
    private String urlPrefix;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtAuthInterceptor)
                // 拦截所有请求
                .addPathPatterns("/**")
                // 排除登录和注册相关接口
                .excludePathPatterns(
                        "/auth/login",
                        "/auth/employee/login",   // 员工客户端登录接口
                        "/auth/code",        // 登录获取验证码接口
                        "/auth/register",    // 注册接口
                        "/auth/register/code", // 注册获取验证码接口
                        "/error",
                        "/images/**",        // 排除图片静态资源访问（向后兼容）
                        urlPrefix + "/**"    // 排除新的统一附件访问路径
                );
    }

    /**
     * 配置静态资源映射
     * 动态配置文件存储路径，支持Windows和Linux路径格式
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 保证路径以'file:'开头并以'/'结尾
        String resourceLocation = uploadPath.startsWith("file:") ? uploadPath : "file:" + uploadPath;
        if (!resourceLocation.endsWith("/")) {
            resourceLocation += "/";
        }

        // 历史兼容路径 - 必须保留
        registry.addResourceHandler("/images/**")
                .addResourceLocations(resourceLocation)
                .setCachePeriod(3600 * 24 * 7);

        // 新的统一附件访问路径
        registry.addResourceHandler(urlPrefix + "/**")
                .addResourceLocations(resourceLocation)
                .setCachePeriod(3600 * 24 * 7);

        log.info("静态资源映射配置完成:");
        log.info("  - 历史兼容: /images/** -> {}", resourceLocation);
        log.info("  - 新统一路径: {} -> {}", urlPrefix + "/**", resourceLocation);
        log.info("  - JWT拦截器排除路径: /images/**, {}", urlPrefix + "/**");
    }
}