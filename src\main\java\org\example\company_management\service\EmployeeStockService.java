package org.example.company_management.service;

import org.example.company_management.dto.EmployeeStockDTO;
import org.example.company_management.dto.EmployeeStockQueryDTO;
import org.example.company_management.dto.StockOverviewDTO;
import org.example.company_management.dto.StockOverviewQueryDTO;
import org.example.company_management.utils.PageResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 员工股票服务接口
 */
public interface EmployeeStockService {

    /**
     * 添加员工股票记录
     *
     * @param employeeStockDTO 员工股票DTO
     * @return 添加成功的员工股票DTO
     */
    EmployeeStockDTO addEmployeeStock(EmployeeStockDTO employeeStockDTO);

    /**
     * 根据ID删除员工股票记录
     *
     * @param id 员工股票记录ID
     * @return 是否删除成功
     */
    boolean deleteEmployeeStock(Long id);

    /**
     * 更新员工股票记录
     *
     * @param employeeStockDTO 员工股票DTO
     * @return 更新后的员工股票DTO
     */
    EmployeeStockDTO updateEmployeeStock(EmployeeStockDTO employeeStockDTO);

    /**
     * 批量添加员工股票记录
     *
     * @param batchDTO 包含员工ID列表的DTO
     * @return 添加成功的员工股票DTO列表
     */
    List<EmployeeStockDTO> addEmployeeStockBatch(EmployeeStockDTO batchDTO);

    // 删除：getEmployeeStockById - 前端不需要单独查看详情

    /**
     * 分页查询员工股票记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    PageResult<EmployeeStockDTO> getEmployeeStockByPage(EmployeeStockQueryDTO queryDTO);

    /**
     * 根据员工ID查询员工股票记录
     *
     * @param employeeId 员工ID
     * @return 员工股票DTO列表
     */
    List<EmployeeStockDTO> getEmployeeStocksByEmployeeId(Integer employeeId);

    // 删除：getEmployeeStocksByDepartmentId - 可通过分页查询+部门筛选实现

    /**
     * 查询指定员工的股票总数量
     *
     * @param employeeId 员工ID
     * @return 股票总数量
     */
    Integer getTotalQuantityByEmployeeId(Integer employeeId);

    /**
     * 查询指定员工的股票总价值
     *
     * @param employeeId 员工ID
     * @return 股票总价值
     */
    BigDecimal getTotalValueByEmployeeId(Integer employeeId);

    /**
     * 查询已解禁的员工股票记录
     *
     * @return 已解禁的员工股票DTO列表
     */
    List<EmployeeStockDTO> getUnlockedEmployeeStocks();

    /**
     * 查询未解禁的员工股票记录
     *
     * @return 未解禁的员工股票DTO列表
     */
    List<EmployeeStockDTO> getLockedEmployeeStocks();

    // 删除：getEmployeeStocksByStockId - 使用场景较少

    /**
     * 检查员工是否已持有指定股票
     * 保留：后端内部验证使用
     *
     * @param employeeId 员工ID
     * @param stockId    股票价格ID
     * @param excludeId  排除的记录ID（用于更新时检查）
     * @return 是否已持有
     */
    boolean isEmployeeHasStock(Integer employeeId, Long stockId, Long excludeId);

    /**
     * 查询员工股票统计信息
     *
     * @return 统计信息列表
     */
    List<Map<String, Object>> getStockStatistics();

    /**
     * 获取持股总览数据（管理员专用）
     * <p>
     * 分页查询所有员工的持股情况汇总，包括已解禁、未解禁、可提现、已提现等信息
     * </p>
     *
     * @param queryDTO 查询条件DTO
     * @return 持股总览分页数据
     */
    PageResult<StockOverviewDTO> getStockOverview(StockOverviewQueryDTO queryDTO);

    // ==================== 用户端专用方法 ====================

    /**
     * 获取指定员工的股票统计信息（用户端专用）
     * <p>
     * 包括总股票数量、总价值、已解禁数量、未解禁数量等
     * </p>
     *
     * @param employeeId 员工ID
     * @return 员工股票统计信息
     */
    Map<String, Object> getEmployeeStockStatistics(Integer employeeId);

    /**
     * 获取指定员工的股票总数量（用户端专用）
     *
     * @param employeeId 员工ID
     * @return 股票总数量
     */
    Integer getTotalStockQuantityByEmployee(Integer employeeId);

    /**
     * 获取指定员工的股票总价值（用户端专用）
     *
     * @param employeeId 员工ID
     * @return 股票总价值
     */
    BigDecimal getTotalStockValueByEmployee(Integer employeeId);

    /**
     * 获取指定员工的股票详细统计信息（新增）
     * <p>
     * 包括总数量、总价值、已解禁数量、已解禁价值、未解禁数量、未解禁价值、
     * 可提现数量、可提现价值、已提现数量、已提现价值、最新股价等
     * </p>
     *
     * @param employeeId 员工ID
     * @return 详细统计信息
     */
    org.example.company_management.dto.EmployeeStockDetailedStatisticsDTO getDetailedStockStatistics(Integer employeeId);

    /**
     * 计算指定员工的可提现数量和金额（新增）
     *
     * @param employeeId 员工ID
     * @return 可提现数量和金额信息
     */
    org.example.company_management.dto.AvailableQuantityDTO getAvailableQuantityInfo(Integer employeeId);

    // ==================== 汇总表相关方法 ====================

    /**
     * 获取员工股票汇总信息（高性能查询）
     *
     * @param employeeId 员工ID
     * @return 股票汇总信息
     */
    org.example.company_management.entity.EmployeeStockSummary getStockSummary(Integer employeeId);

    /**
     * 重新计算指定员工的股票汇总数据
     *
     * @param employeeId 员工ID
     * @return 是否成功
     */
    boolean recalculateStockSummary(Integer employeeId);

    /**
     * 重新计算所有员工的股票汇总数据
     *
     * @return 成功处理的员工数量
     */
    int recalculateAllStockSummary();

    /**
     * 检查股票汇总数据一致性
     *
     * @return 不一致的记录列表
     */
    List<Map<String, Object>> checkStockSummaryConsistency();
}
