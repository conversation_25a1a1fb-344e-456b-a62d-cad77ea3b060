package org.example.company_management.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.AvailableQuantityDTO;
import org.example.company_management.dto.EmployeeStockDTO;
import org.example.company_management.dto.EmployeeStockDetailedStatisticsDTO;
import org.example.company_management.dto.EmployeeStockQueryDTO;
import org.example.company_management.dto.StockOverviewDTO;
import org.example.company_management.dto.StockOverviewQueryDTO;
import org.example.company_management.entity.EmployeeStock;
import org.example.company_management.entity.EmployeeStockSummary;
import org.example.company_management.exception.BusinessException;
import org.example.company_management.mapper.EmployeeStockMapper;
import org.example.company_management.mapper.EmployeeStockSummaryMapper;
import org.example.company_management.service.EmployeeStockService;
import org.example.company_management.service.StockPriceService;
import org.example.company_management.dto.StockPriceDTO;
import org.example.company_management.utils.BeanCopyUtils;
import org.example.company_management.utils.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工股票服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeStockServiceImpl implements EmployeeStockService {

    private final EmployeeStockMapper employeeStockMapper;
    private final EmployeeStockSummaryMapper employeeStockSummaryMapper;
    private final StockPriceService stockPriceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployeeStockDTO addEmployeeStock(EmployeeStockDTO employeeStockDTO) {
        log.info("添加员工股票记录: {}", employeeStockDTO);

        // 注释：由于员工股票始终使用最新价格，允许员工多次获得股票
        // 不再检查重复持股，允许员工在不同时间获得股票

        // 自动获取最新的股票价格ID
        StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
        if (latestStockPrice == null) {
            throw new BusinessException("系统中没有股票价格记录，请先添加股票价格");
        }
        log.info("使用最新股票价格: ID={}, 日期={}, 单价={}",
                latestStockPrice.getId(), latestStockPrice.getTime(), latestStockPrice.getUnitPrice());

        // DTO转Entity
        EmployeeStock employeeStock = BeanCopyUtils.copyBean(employeeStockDTO, EmployeeStock.class);
        // 强制使用最新的股票价格ID
        employeeStock.setStockId(latestStockPrice.getId());

        // 插入数据库
        int result = employeeStockMapper.insert(employeeStock);
        if (result <= 0) {
            throw new BusinessException("添加员工股票记录失败");
        }

        // 返回添加后的数据（包含关联信息）
        EmployeeStock addedStock = employeeStockMapper.selectById(employeeStock.getId());
        return BeanCopyUtils.copyBean(addedStock, EmployeeStockDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEmployeeStock(Long id) {
        log.info("删除员工股票记录: {}", id);

        // 检查记录是否存在
        EmployeeStock employeeStock = employeeStockMapper.selectById(id);
        if (employeeStock == null) {
            throw new BusinessException("员工股票记录不存在");
        }

        // 删除记录
        int result = employeeStockMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployeeStockDTO updateEmployeeStock(EmployeeStockDTO employeeStockDTO) {
        log.info("更新员工股票记录: {}", employeeStockDTO);
        log.info("接收到的更新参数 - id: {}, employeeId: {}, 将自动使用最新股票价格",
                employeeStockDTO.getId(), employeeStockDTO.getEmployeeId());

        // 检查记录是否存在
        EmployeeStock existingStock = employeeStockMapper.selectById(employeeStockDTO.getId());
        if (existingStock == null) {
            throw new BusinessException("员工股票记录不存在");
        }

        // 自动获取最新的股票价格ID（更新时也使用最新价格）
        StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
        if (latestStockPrice == null) {
            throw new BusinessException("系统中没有股票价格记录，请先添加股票价格");
        }

        // 注释：由于员工股票始终使用最新价格，不再检查重复持股
        // 允许员工有多条股票记录

        // DTO转Entity
        EmployeeStock employeeStock = BeanCopyUtils.copyBean(employeeStockDTO, EmployeeStock.class);
        // 强制使用最新的股票价格ID
        employeeStock.setStockId(latestStockPrice.getId());

        // 更新数据库
        int result = employeeStockMapper.updateById(employeeStock);
        if (result <= 0) {
            throw new BusinessException("更新员工股票记录失败");
        }

        // 返回更新后的数据（包含关联信息）
        EmployeeStock updatedStock = employeeStockMapper.selectById(employeeStock.getId());
        return BeanCopyUtils.copyBean(updatedStock, EmployeeStockDTO.class);
    }

    // 删除：getEmployeeStockById - 前端不需要单独查看详情

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<EmployeeStockDTO> addEmployeeStockBatch(EmployeeStockDTO batchDTO) {
        log.info("批量添加员工股票记录: {}", batchDTO);
        log.info("接收到的employeeIds: {}, 将自动使用最新股票价格", batchDTO.getEmployeeIds());

        List<EmployeeStockDTO> results = new ArrayList<>();

        if (batchDTO.getEmployeeIds() == null || batchDTO.getEmployeeIds().isEmpty()) {
            throw new BusinessException("员工ID列表不能为空");
        }

        // 自动获取最新的股票价格ID
        StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
        if (latestStockPrice == null) {
            throw new BusinessException("系统中没有股票价格记录，请先添加股票价格");
        }

        for (Integer employeeId : batchDTO.getEmployeeIds()) {
            // 注释：由于员工股票始终使用最新价格，不再检查重复持股
            // 允许员工多次获得股票

            // 创建员工股票记录
            EmployeeStock employeeStock = new EmployeeStock();
            employeeStock.setStockId(latestStockPrice.getId()); // 使用最新的股票价格ID
            employeeStock.setEmployeeId(employeeId);
            employeeStock.setQuantity(batchDTO.getQuantity());
            employeeStock.setAcquisitionTime(batchDTO.getAcquisitionTime());
            employeeStock.setUnlockTime(batchDTO.getUnlockTime());
            employeeStock.setRemark(batchDTO.getRemark());

            // 插入数据库
            int result = employeeStockMapper.insert(employeeStock);
            if (result > 0) {
                // 查询插入后的完整数据（包含关联信息）
                EmployeeStock insertedStock = employeeStockMapper.selectById(employeeStock.getId());
                EmployeeStockDTO dto = BeanCopyUtils.copyBean(insertedStock, EmployeeStockDTO.class);
                results.add(dto);
            } else {
                log.error("员工ID {} 的股票记录插入失败", employeeId);
                throw new BusinessException("员工ID " + employeeId + " 的股票记录添加失败");
            }
        }

        log.info("批量添加员工股票记录完成，成功添加 {} 条记录", results.size());
        return results;
    }

    @Override
    public PageResult<EmployeeStockDTO> getEmployeeStockByPage(EmployeeStockQueryDTO queryDTO) {
        log.info("分页查询员工股票记录: {}", queryDTO);

        // 设置分页参数
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询参数（不包含分页参数）
        Map<String, Object> params = buildQueryParamsWithoutPagination(queryDTO);

        // 查询数据
        List<EmployeeStock> employeeStocks = employeeStockMapper.selectByPageWithPageHelper(params);
        PageInfo<EmployeeStock> pageInfo = new PageInfo<>(employeeStocks);

        // 转换为DTO
        List<EmployeeStockDTO> employeeStockDTOs = employeeStocks.stream()
                .map(employeeStock -> BeanCopyUtils.copyBean(employeeStock, EmployeeStockDTO.class))
                .collect(Collectors.toList());

        return new PageResult<EmployeeStockDTO>(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getTotal(), employeeStockDTOs);
    }

    @Override
    public List<EmployeeStockDTO> getEmployeeStocksByEmployeeId(Integer employeeId) {
        log.info("根据员工ID查询员工股票记录: {}", employeeId);

        List<EmployeeStock> employeeStocks = employeeStockMapper.selectByEmployeeId(employeeId);
        return employeeStocks.stream()
                .map(employeeStock -> BeanCopyUtils.copyBean(employeeStock, EmployeeStockDTO.class))
                .collect(Collectors.toList());
    }

    // 删除：getEmployeeStocksByDepartmentId - 可通过分页查询+部门筛选实现

    @Override
    public Integer getTotalQuantityByEmployeeId(Integer employeeId) {
        log.info("查询员工股票总数量: {}", employeeId);

        Integer totalQuantity = employeeStockMapper.sumQuantityByEmployeeId(employeeId);
        return totalQuantity != null ? totalQuantity : 0;
    }

    @Override
    public BigDecimal getTotalValueByEmployeeId(Integer employeeId) {
        log.info("查询员工股票总价值: {}", employeeId);

        BigDecimal totalValue = employeeStockMapper.sumTotalValueByEmployeeId(employeeId);
        return totalValue != null ? totalValue : BigDecimal.ZERO;
    }

    @Override
    public List<EmployeeStockDTO> getUnlockedEmployeeStocks() {
        log.info("查询已解禁的员工股票记录");

        List<EmployeeStock> employeeStocks = employeeStockMapper.selectUnlockedStocks();
        return employeeStocks.stream()
                .map(employeeStock -> BeanCopyUtils.copyBean(employeeStock, EmployeeStockDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeStockDTO> getLockedEmployeeStocks() {
        log.info("查询未解禁的员工股票记录");

        List<EmployeeStock> employeeStocks = employeeStockMapper.selectLockedStocks();
        return employeeStocks.stream()
                .map(employeeStock -> BeanCopyUtils.copyBean(employeeStock, EmployeeStockDTO.class))
                .collect(Collectors.toList());
    }

    // 删除：getEmployeeStocksByStockId - 使用场景较少

    @Override
    public boolean isEmployeeHasStock(Integer employeeId, Long stockId, Long excludeId) {
        int count = employeeStockMapper.countByEmployeeAndStock(employeeId, stockId, excludeId);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getStockStatistics() {
        log.info("查询员工股票统计信息");

        return employeeStockMapper.selectStockStatistics();
    }

    /**
     * 构建查询参数（包含分页参数，用于原有的手动分页方法）
     */
    private Map<String, Object> buildQueryParams(EmployeeStockQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();

        // 分页参数
        if (queryDTO.getPageNum() != null && queryDTO.getPageSize() != null) {
            params.put("offset", (queryDTO.getPageNum() - 1) * queryDTO.getPageSize());
            params.put("limit", queryDTO.getPageSize());
        }

        // 查询条件
        if (queryDTO.getEmployeeId() != null) {
            params.put("employeeId", queryDTO.getEmployeeId());
        }
        if (queryDTO.getEmployeeName() != null && !queryDTO.getEmployeeName().trim().isEmpty()) {
            params.put("employeeName", queryDTO.getEmployeeName().trim());
        }
        if (queryDTO.getDepartmentId() != null) {
            params.put("departmentId", queryDTO.getDepartmentId());
        }
        if (queryDTO.getAcquisitionStartTime() != null) {
            params.put("acquisitionStartTime", queryDTO.getAcquisitionStartTime());
        }
        if (queryDTO.getAcquisitionEndTime() != null) {
            params.put("acquisitionEndTime", queryDTO.getAcquisitionEndTime());
        }
        if (queryDTO.getIsUnlocked() != null) {
            params.put("isUnlocked", queryDTO.getIsUnlocked());
        }
        if (queryDTO.getRemark() != null && !queryDTO.getRemark().trim().isEmpty()) {
            params.put("remark", queryDTO.getRemark().trim());
        }

        // 排序参数
        if (queryDTO.getOrderBy() != null && !queryDTO.getOrderBy().trim().isEmpty()) {
            params.put("orderBy", queryDTO.getOrderBy());
        }
        if (queryDTO.getOrderDirection() != null && !queryDTO.getOrderDirection().trim().isEmpty()) {
            params.put("orderDirection", queryDTO.getOrderDirection());
        }

        return params;
    }

    /**
     * 构建查询参数（不包含分页参数，用于PageHelper分页）
     */
    private Map<String, Object> buildQueryParamsWithoutPagination(EmployeeStockQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();

        // 查询条件
        if (queryDTO.getEmployeeId() != null) {
            params.put("employeeId", queryDTO.getEmployeeId());
        }
        if (queryDTO.getEmployeeName() != null && !queryDTO.getEmployeeName().trim().isEmpty()) {
            params.put("employeeName", queryDTO.getEmployeeName().trim());
        }
        if (queryDTO.getDepartmentId() != null) {
            params.put("departmentId", queryDTO.getDepartmentId());
        }
        if (queryDTO.getAcquisitionStartTime() != null) {
            params.put("acquisitionStartTime", queryDTO.getAcquisitionStartTime());
        }
        if (queryDTO.getAcquisitionEndTime() != null) {
            params.put("acquisitionEndTime", queryDTO.getAcquisitionEndTime());
        }
        if (queryDTO.getIsUnlocked() != null) {
            params.put("isUnlocked", queryDTO.getIsUnlocked());
        }
        if (queryDTO.getRemark() != null && !queryDTO.getRemark().trim().isEmpty()) {
            params.put("remark", queryDTO.getRemark().trim());
        }

        // 排序参数
        if (queryDTO.getOrderBy() != null && !queryDTO.getOrderBy().trim().isEmpty()) {
            params.put("orderBy", queryDTO.getOrderBy());
        }
        if (queryDTO.getOrderDirection() != null && !queryDTO.getOrderDirection().trim().isEmpty()) {
            params.put("orderDirection", queryDTO.getOrderDirection());
        }

        return params;
    }

    // ==================== 用户端专用方法实现 ====================

    @Override
    public Map<String, Object> getEmployeeStockStatistics(Integer employeeId) {
        log.info("获取员工股票统计信息: employeeId={}", employeeId);

        Map<String, Object> statistics = new HashMap<>();

        try {
            // 获取总股票数量
            Integer totalQuantity = getTotalStockQuantityByEmployee(employeeId);
            statistics.put("totalQuantity", totalQuantity != null ? totalQuantity : 0);

            // 获取总股票价值
            BigDecimal totalValue = getTotalStockValueByEmployee(employeeId);
            statistics.put("totalValue", totalValue != null ? totalValue : BigDecimal.ZERO);

            // 获取已解禁股票数量
            Integer unlockedQuantity = employeeStockMapper.countUnlockedStocksByEmployee(employeeId);
            statistics.put("unlockedQuantity", unlockedQuantity != null ? unlockedQuantity : 0);

            // 获取未解禁股票数量
            Integer lockedQuantity = employeeStockMapper.countLockedStocksByEmployee(employeeId);
            statistics.put("lockedQuantity", lockedQuantity != null ? lockedQuantity : 0);

            // 获取股票记录总数
            Integer totalRecords = employeeStockMapper.countStockRecordsByEmployee(employeeId);
            statistics.put("totalRecords", totalRecords != null ? totalRecords : 0);

            // 获取最新股票价格
            StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
            if (latestStockPrice != null) {
                statistics.put("latestStockPrice", latestStockPrice.getUnitPrice());
                statistics.put("latestStockPriceTime", latestStockPrice.getTime());
            } else {
                statistics.put("latestStockPrice", BigDecimal.ZERO);
                statistics.put("latestStockPriceTime", null);
            }

            log.info("员工股票统计信息查询成功: {}", statistics);
            return statistics;

        } catch (Exception e) {
            log.error("获取员工股票统计信息失败: employeeId={}", employeeId, e);
            throw new BusinessException("获取股票统计信息失败");
        }
    }

    @Override
    public Integer getTotalStockQuantityByEmployee(Integer employeeId) {
        log.info("获取员工股票总数量: employeeId={}", employeeId);

        try {
            Integer totalQuantity = employeeStockMapper.sumQuantityByEmployee(employeeId);
            log.info("员工股票总数量查询成功: employeeId={}, totalQuantity={}", employeeId, totalQuantity);
            return totalQuantity != null ? totalQuantity : 0;
        } catch (Exception e) {
            log.error("获取员工股票总数量失败: employeeId={}", employeeId, e);
            throw new BusinessException("获取股票总数量失败");
        }
    }

    @Override
    public BigDecimal getTotalStockValueByEmployee(Integer employeeId) {
        log.info("获取员工股票总价值: employeeId={}", employeeId);

        try {
            BigDecimal totalValue = employeeStockMapper.sumValueByEmployee(employeeId);
            log.info("员工股票总价值查询成功: employeeId={}, totalValue={}", employeeId, totalValue);
            return totalValue != null ? totalValue : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取员工股票总价值失败: employeeId={}", employeeId, e);
            throw new BusinessException("获取股票总价值失败");
        }
    }

    @Override
    public EmployeeStockDetailedStatisticsDTO getDetailedStockStatistics(Integer employeeId) {
        log.info("获取员工股票详细统计信息（使用汇总表）: employeeId={}", employeeId);

        try {
            // 优先从汇总表获取数据（高性能）
            EmployeeStockSummary summary = employeeStockSummaryMapper.selectByEmployeeId(employeeId);
            EmployeeStockDetailedStatisticsDTO dto = new EmployeeStockDetailedStatisticsDTO();

            if (summary == null) {
                // 如果汇总表没有数据，尝试重新计算
                log.warn("汇总表中没有员工股票数据，尝试重新计算: employeeId={}", employeeId);
                recalculateStockSummary(employeeId);
                summary = employeeStockSummaryMapper.selectByEmployeeId(employeeId);
            }

            if (summary == null) {
                // 如果仍然没有数据，说明员工确实没有股票
                log.info("员工没有股票记录: employeeId={}", employeeId);
                dto.setTotalQuantity(0);
                dto.setTotalValue(BigDecimal.ZERO);
                dto.setUnlockedQuantity(0);
                dto.setUnlockedValue(BigDecimal.ZERO);
                dto.setLockedQuantity(0);
                dto.setLockedValue(BigDecimal.ZERO);
                dto.setAvailableQuantity(0);
                dto.setAvailableValue(BigDecimal.ZERO);
                dto.setWithdrawnQuantity(0);
                dto.setWithdrawnValue(BigDecimal.ZERO);
                dto.setTotalRecords(0);
                dto.setLatestStockPrice(BigDecimal.ZERO);
                dto.setLatestStockPriceTime(null);
            } else {
                // 从汇总表设置基础数据
                dto.setTotalQuantity(summary.getTotalQuantity());
                dto.setUnlockedQuantity(summary.getUnlockedQuantity());
                dto.setLockedQuantity(summary.getLockedQuantity());
                dto.setAvailableQuantity(summary.getAvailableQuantity());
                dto.setWithdrawnQuantity(summary.getWithdrawnQuantity());

                // 获取最新股价
                StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
                if (latestStockPrice != null) {
                    dto.setLatestStockPrice(latestStockPrice.getUnitPrice());
                    dto.setLatestStockPriceTime(latestStockPrice.getTime());

                    // 计算价值（使用最新股价）
                    BigDecimal unitPrice = latestStockPrice.getUnitPrice();
                    dto.setTotalValue(unitPrice.multiply(new BigDecimal(summary.getTotalQuantity())));
                    dto.setUnlockedValue(unitPrice.multiply(new BigDecimal(summary.getUnlockedQuantity())));
                    dto.setLockedValue(unitPrice.multiply(new BigDecimal(summary.getLockedQuantity())));
                    dto.setAvailableValue(unitPrice.multiply(new BigDecimal(summary.getAvailableQuantity())));
                    dto.setWithdrawnValue(unitPrice.multiply(new BigDecimal(summary.getWithdrawnQuantity())));
                } else {
                    // 没有股价数据时设置为0
                    dto.setLatestStockPrice(BigDecimal.ZERO);
                    dto.setLatestStockPriceTime(null);
                    dto.setTotalValue(BigDecimal.ZERO);
                    dto.setUnlockedValue(BigDecimal.ZERO);
                    dto.setLockedValue(BigDecimal.ZERO);
                    dto.setAvailableValue(BigDecimal.ZERO);
                    dto.setWithdrawnValue(BigDecimal.ZERO);
                }

                // 获取记录总数（从明细表查询）
                Integer totalRecords = employeeStockMapper.countByEmployeeId(employeeId);
                dto.setTotalRecords(totalRecords != null ? totalRecords : 0);
            }

            log.info("员工股票详细统计查询成功（汇总表）: employeeId={}, statistics={}", employeeId, dto);
            return dto;
        } catch (Exception e) {
            log.error("获取员工股票详细统计失败: employeeId={}", employeeId, e);
            throw new BusinessException("获取股票详细统计失败");
        }
    }

    @Override
    public AvailableQuantityDTO getAvailableQuantityInfo(Integer employeeId) {
        log.info("计算员工可提现数量（使用汇总表）: employeeId={}", employeeId);

        try {
            // 优先从汇总表获取数据（高性能）
            EmployeeStockSummary summary = employeeStockSummaryMapper.selectByEmployeeId(employeeId);
            AvailableQuantityDTO dto = new AvailableQuantityDTO();

            if (summary == null) {
                // 如果汇总表没有数据，尝试重新计算
                log.warn("汇总表中没有员工股票数据，尝试重新计算: employeeId={}", employeeId);
                recalculateStockSummary(employeeId);
                summary = employeeStockSummaryMapper.selectByEmployeeId(employeeId);
            }

            if (summary == null) {
                // 如果仍然没有数据，说明员工确实没有股票
                dto.setAvailableQuantity(0);
                dto.setCurrentPrice(BigDecimal.ZERO);
                dto.setAvailableValue(BigDecimal.ZERO);
            } else {
                // 从汇总表获取可提现数量
                dto.setAvailableQuantity(summary.getAvailableQuantity());

                // 获取当前股价
                StockPriceDTO latestStockPrice = stockPriceService.getLatestStockPrice();
                if (latestStockPrice != null) {
                    dto.setCurrentPrice(latestStockPrice.getUnitPrice());
                    dto.setAvailableValue(latestStockPrice.getUnitPrice().multiply(new BigDecimal(summary.getAvailableQuantity())));
                } else {
                    dto.setCurrentPrice(BigDecimal.ZERO);
                    dto.setAvailableValue(BigDecimal.ZERO);
                }
            }

            log.info("员工可提现数量计算成功（汇总表）: employeeId={}, availableInfo={}", employeeId, dto);
            return dto;
        } catch (Exception e) {
            log.error("计算员工可提现数量失败: employeeId={}", employeeId, e);
            throw new BusinessException("计算可提现数量失败");
        }
    }

    // ==================== 汇总表相关方法实现 ====================

    @Override
    public EmployeeStockSummary getStockSummary(Integer employeeId) {
        log.info("获取员工股票汇总信息: employeeId={}", employeeId);
        try {
            EmployeeStockSummary summary = employeeStockSummaryMapper.selectByEmployeeIdWithDetails(employeeId);
            if (summary == null) {
                log.warn("员工股票汇总信息不存在，尝试重新计算: employeeId={}", employeeId);
                recalculateStockSummary(employeeId);
                summary = employeeStockSummaryMapper.selectByEmployeeIdWithDetails(employeeId);
            }
            return summary;
        } catch (Exception e) {
            log.error("获取员工股票汇总信息失败: employeeId={}", employeeId, e);
            throw new BusinessException("获取股票汇总信息失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateStockSummary(Integer employeeId) {
        log.info("重新计算员工股票汇总数据: employeeId={}", employeeId);
        try {
            int result = employeeStockSummaryMapper.recalculateByEmployeeId(employeeId);
            log.info("员工股票汇总数据重新计算完成: employeeId={}, result={}", employeeId, result);
            return result > 0;
        } catch (Exception e) {
            log.error("重新计算员工股票汇总数据失败: employeeId={}", employeeId, e);
            throw new BusinessException("重新计算股票汇总数据失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recalculateAllStockSummary() {
        log.info("重新计算所有员工的股票汇总数据");
        try {
            int result = employeeStockSummaryMapper.recalculateAll();
            log.info("所有员工股票汇总数据重新计算完成: result={}", result);
            return result;
        } catch (Exception e) {
            log.error("重新计算所有员工股票汇总数据失败", e);
            throw new BusinessException("重新计算所有股票汇总数据失败");
        }
    }

    @Override
    public List<Map<String, Object>> checkStockSummaryConsistency() {
        log.info("检查股票汇总数据一致性");
        try {
            List<Map<String, Object>> inconsistentData = employeeStockSummaryMapper.checkDataConsistency();
            log.info("股票汇总数据一致性检查完成: 发现{}条不一致记录", inconsistentData.size());
            return inconsistentData;
        } catch (Exception e) {
            log.error("检查股票汇总数据一致性失败", e);
            throw new BusinessException("检查数据一致性失败");
        }
    }

    @Override
    public PageResult<StockOverviewDTO> getStockOverview(StockOverviewQueryDTO queryDTO) {
        log.info("查询持股总览: {}", queryDTO);

        try {
            // 验证分页参数
            queryDTO.validatePagination();

            // 设置分页参数
            PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("employeeName", queryDTO.getEmployeeNameLike());
            params.put("departmentId", queryDTO.getDepartmentId());
            params.put("onlyWithStock", queryDTO.getOnlyWithStock());
            params.put("orderSql", queryDTO.getOrderSql());

            // 执行查询
            List<StockOverviewDTO> list = employeeStockMapper.selectStockOverview(params);

            // 封装分页结果
            PageInfo<StockOverviewDTO> pageInfo = new PageInfo<>(list);

            log.info("持股总览查询完成: 总记录数={}, 当前页记录数={}", pageInfo.getTotal(), list.size());

            return new PageResult<StockOverviewDTO>(
                pageInfo.getPageNum(),
                pageInfo.getPageSize(),
                pageInfo.getTotal(),
                pageInfo.getList()
            );

        } catch (Exception e) {
            log.error("查询持股总览失败", e);
            throw new BusinessException("查询持股总览失败: " + e.getMessage());
        }
    }
}
