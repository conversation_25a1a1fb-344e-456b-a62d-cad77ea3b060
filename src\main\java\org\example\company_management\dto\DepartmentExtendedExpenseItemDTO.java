package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class DepartmentExtendedExpenseItemDTO {

    @NotNull(message = "部门ID不能为空")
    private Long departmentId;

    // Optional: For richer response messages, not directly used in persistence logic unless joined.
    // This field will be populated from the frontend if available in the selected item.
    private String departmentName;

    @NotNull(message = "开销日期不能为空")
    private LocalDate expenseDate; // Expected as YYYY-MM-DD (first day of the month from frontend)

    @NotBlank(message = "项目名称不能为空")
    @Size(max = 255, message = "项目名称长度不能超过255个字符")
    private String itemName;

    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;
} 