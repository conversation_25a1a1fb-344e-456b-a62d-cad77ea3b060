package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 仪表盘数据访问接口
 */
@Mapper
public interface DashboardMapper {

    /**
     * 统计员工总数
     */
    int countTotalEmployees();

    /**
     * 统计在职员工数量
     */
    int countActiveEmployees();

    /**
     * 统计部门总数
     */
    int countTotalDepartments();

    /**
     * 统计职位总数
     */
    int countTotalPositions();

    /**
     * 计算总业绩
     */
    double sumTotalPerformance();

    // ==================== 成就相关查询方法 ====================

    /**
     * 获取员工第一次获得新客户的时间
     * @param employeeId 员工ID
     * @return 第一次获得客户的时间，如果没有则返回null
     */
    LocalDateTime getFirstClientAchievementDate(@Param("employeeId") Integer employeeId);

    /**
     * 获取员工第一次获得提成的时间（业绩达到1万）
     * @param employeeId 员工ID
     * @return 第一次达到1万业绩的月份，如果没有则返回null
     */
    String getFirstCommissionAchievementDate(@Param("employeeId") Integer employeeId);

    /**
     * 获取员工第一次达到3万业绩的时间
     * @param employeeId 员工ID
     * @return 第一次达到3万业绩的月份，如果没有则返回null
     */
    String getFirst30kPerformanceAchievementDate(@Param("employeeId") Integer employeeId);

    /**
     * 获取员工的所有业绩记录（按日期升序）
     * 用于计算累计能力值，找到首次转正的时间点
     * @param employeeId 员工ID
     * @return 业绩记录列表
     */
    java.util.List<java.util.Map<String, Object>> getPerformanceRecordsForCapability(@Param("employeeId") Integer employeeId);

    /**
     * 获取员工指定月份的工资信息
     * @param employeeId 员工ID
     * @param date 年月（格式：YYYY-MM）
     * @return 工资信息
     */
    java.util.Map<String, Object> getSalaryByEmployeeAndDate(@Param("employeeId") Integer employeeId, @Param("date") String date);
}