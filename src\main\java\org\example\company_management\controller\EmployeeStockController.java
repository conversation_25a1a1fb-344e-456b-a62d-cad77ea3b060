package org.example.company_management.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.company_management.dto.AvailableQuantityDTO;
import org.example.company_management.dto.EmployeeStockDTO;
import org.example.company_management.dto.EmployeeStockDetailedStatisticsDTO;
import org.example.company_management.dto.EmployeeStockQueryDTO;
import org.example.company_management.dto.StockOverviewDTO;
import org.example.company_management.dto.StockOverviewQueryDTO;
import org.example.company_management.service.EmployeeStockService;
import org.example.company_management.utils.PageResult;
import org.example.company_management.utils.Result;
import org.example.company_management.utils.ThreadLocalUtil;
import org.example.company_management.validation.ValidationGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 员工股票控制器
 */
@Slf4j
@RestController
@RequestMapping("/employee-stock")
@RequiredArgsConstructor
public class EmployeeStockController {

    private final EmployeeStockService employeeStockService;

    @PostMapping
    public Result<EmployeeStockDTO> addEmployeeStock(
            @RequestBody @Validated(ValidationGroups.Add.class) EmployeeStockDTO employeeStockDTO) {
        log.info("添加员工股票记录: {}", employeeStockDTO);

        EmployeeStockDTO result = employeeStockService.addEmployeeStock(employeeStockDTO);
        return Result.success(result);
    }

    @PostMapping("/batch")
    public Result<List<EmployeeStockDTO>> addEmployeeStockBatch(
            @RequestBody @Validated(ValidationGroups.Add.class) EmployeeStockDTO batchDTO) {
        log.info("批量添加员工股票记录: {}", batchDTO);

        List<EmployeeStockDTO> result = employeeStockService.addEmployeeStockBatch(batchDTO);
        return Result.success(result);
    }

    @DeleteMapping("/{id}")
    public Result<Boolean> deleteEmployeeStock(@PathVariable Long id) {
        log.info("删除员工股票记录: {}", id);

        boolean result = employeeStockService.deleteEmployeeStock(id);
        return Result.success(result);
    }

    @PutMapping
    public Result<EmployeeStockDTO> updateEmployeeStock(
            @RequestBody @Validated(ValidationGroups.Update.class) EmployeeStockDTO employeeStockDTO) {
        log.info("更新员工股票记录: {}", employeeStockDTO);

        EmployeeStockDTO result = employeeStockService.updateEmployeeStock(employeeStockDTO);
        return Result.success(result);
    }

    // 删除：getEmployeeStockById - 前端不需要单独查看详情

    @GetMapping("/page")
    public Result<PageResult<EmployeeStockDTO>> getEmployeeStockByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer employeeId,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) String acquisitionStartTime,
            @RequestParam(required = false) String acquisitionEndTime,
            @RequestParam(required = false) Boolean isUnlocked,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderDirection) {

        log.info("分页查询员工股票记录: pageNum={}, pageSize={}, employeeId={}, employeeName={}, departmentId={}, acquisitionStartTime={}, acquisitionEndTime={}, isUnlocked={}, orderBy={}, orderDirection={}",
                pageNum, pageSize, employeeId, employeeName, departmentId, acquisitionStartTime, acquisitionEndTime, isUnlocked, orderBy, orderDirection);

        // 构建查询DTO
        EmployeeStockQueryDTO queryDTO = new EmployeeStockQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        queryDTO.setEmployeeId(employeeId);
        queryDTO.setEmployeeName(employeeName);
        queryDTO.setDepartmentId(departmentId);
        queryDTO.setAcquisitionStartTime(acquisitionStartTime);
        queryDTO.setAcquisitionEndTime(acquisitionEndTime);
        queryDTO.setIsUnlocked(isUnlocked);
        queryDTO.setOrderBy(orderBy);
        queryDTO.setOrderDirection(orderDirection);

        PageResult<EmployeeStockDTO> result = employeeStockService.getEmployeeStockByPage(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/by-employee/{employeeId}")
    public Result<List<EmployeeStockDTO>> getEmployeeStocksByEmployeeId(@PathVariable Integer employeeId) {
        log.info("根据员工ID查询股票记录: {}", employeeId);

        List<EmployeeStockDTO> result = employeeStockService.getEmployeeStocksByEmployeeId(employeeId);
        return Result.success(result);
    }

    // 删除：getEmployeeStocksByDepartmentId - 可通过分页查询+部门筛选实现

    @GetMapping("/quantity/{employeeId}")
    public Result<Integer> getTotalQuantityByEmployeeId(@PathVariable Integer employeeId) {
        log.info("查询员工股票总数量: {}", employeeId);

        Integer result = employeeStockService.getTotalQuantityByEmployeeId(employeeId);
        return Result.success(result);
    }

    @GetMapping("/value/{employeeId}")
    public Result<BigDecimal> getTotalValueByEmployeeId(@PathVariable Integer employeeId) {
        log.info("查询员工股票总价值: {}", employeeId);

        BigDecimal result = employeeStockService.getTotalValueByEmployeeId(employeeId);
        return Result.success(result);
    }

    @GetMapping("/unlocked")
    public Result<List<EmployeeStockDTO>> getUnlockedEmployeeStocks() {
        log.info("查询已解禁股票记录");

        List<EmployeeStockDTO> result = employeeStockService.getUnlockedEmployeeStocks();
        return Result.success(result);
    }

    @GetMapping("/locked")
    public Result<List<EmployeeStockDTO>> getLockedEmployeeStocks() {
        log.info("查询未解禁股票记录");

        List<EmployeeStockDTO> result = employeeStockService.getLockedEmployeeStocks();
        return Result.success(result);
    }

    // 删除：getEmployeeStocksByStockId - 使用场景较少

    @GetMapping("/statistics")
    public Result<List<Map<String, Object>>> getStockStatistics() {
        log.info("查询股票统计信息");

        List<Map<String, Object>> result = employeeStockService.getStockStatistics();
        return Result.success(result);
    }

    // 删除：checkEmployeeHasStock - 后端内部验证，前端不需要直接调用

    /**
     * 获取持股总览数据（管理员专用）
     * <p>
     * 分页查询所有员工的持股情况汇总，包括已解禁、未解禁、可提现、已提现等信息
     * </p>
     *
     * @param pageNum 页码，默认为1
     * @param pageSize 每页大小，默认为15
     * @param employeeName 员工姓名，支持模糊搜索
     * @param departmentId 部门ID，精确匹配
     * @param orderBy 排序字段
     * @param orderDirection 排序方向
     * @return 持股总览分页数据
     */
    @GetMapping("/overview")
    public Result<PageResult<StockOverviewDTO>> getStockOverview(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "15") Integer pageSize,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderDirection) {

        log.info("查询持股总览: pageNum={}, pageSize={}, employeeName={}, departmentId={}, orderBy={}, orderDirection={}",
                pageNum, pageSize, employeeName, departmentId, orderBy, orderDirection);

        try {
            // 构建查询DTO
            StockOverviewQueryDTO queryDTO = new StockOverviewQueryDTO();
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            queryDTO.setEmployeeName(employeeName);
            queryDTO.setDepartmentId(departmentId);
            queryDTO.setOrderBy(orderBy);
            queryDTO.setOrderDirection(orderDirection);

            // 验证分页参数
            queryDTO.validatePagination();

            PageResult<StockOverviewDTO> result = employeeStockService.getStockOverview(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询持股总览失败", e);
            return Result.error("查询持股总览失败：" + e.getMessage());
        }
    }

    // ==================== 用户端专用接口 ====================

    /**
     * 分页查询当前登录员工的股票记录（用户端专用）
     * <p>
     * 从ThreadLocal获取当前登录员工ID，只返回该员工的股票记录
     * </p>
     *
     * @param pageNum 页码，默认为1
     * @param pageSize 每页大小，默认为10
     * @param acquisitionStartTime 获取时间开始（可选）
     * @param acquisitionEndTime 获取时间结束（可选）
     * @param isUnlocked 是否已解禁（可选）
     * @param orderBy 排序字段（可选）
     * @param orderDirection 排序方向（可选）
     * @return 当前员工的股票记录分页列表
     */
    @GetMapping("/my/page")
    public Result<PageResult<EmployeeStockDTO>> getMyEmployeeStockByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String acquisitionStartTime,
            @RequestParam(required = false) String acquisitionEndTime,
            @RequestParam(required = false) Boolean isUnlocked,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderDirection) {

        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("分页查询当前员工股票记录: employeeId={}, pageNum={}, pageSize={}, remark={}",
                    employeeId, pageNum, pageSize, remark);

            // 构建查询DTO，强制设置employeeId
            EmployeeStockQueryDTO queryDTO = new EmployeeStockQueryDTO();
            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            queryDTO.setEmployeeId(employeeId); // 强制设置为当前登录员工ID
            queryDTO.setAcquisitionStartTime(acquisitionStartTime);
            queryDTO.setAcquisitionEndTime(acquisitionEndTime);
            queryDTO.setIsUnlocked(isUnlocked);
            queryDTO.setRemark(remark); // 添加备注搜索
            queryDTO.setOrderBy(orderBy);
            queryDTO.setOrderDirection(orderDirection);

            PageResult<EmployeeStockDTO> result = employeeStockService.getEmployeeStockByPage(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询当前员工股票记录失败", e);
            return Result.error("查询股票记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的股票统计信息（用户端专用）
     * <p>
     * 包括总股票数量、总价值、已解禁数量、未解禁数量等
     * </p>
     *
     * @return 当前员工的股票统计信息
     */
    @GetMapping("/my/statistics")
    public Result<Map<String, Object>> getMyStockStatistics() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("查询当前员工股票统计信息: employeeId={}", employeeId);

            Map<String, Object> result = employeeStockService.getEmployeeStockStatistics(employeeId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询当前员工股票统计信息失败", e);
            return Result.error("查询股票统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的股票总数量（用户端专用）
     *
     * @return 当前员工的股票总数量
     */
    @GetMapping("/my/total-quantity")
    public Result<Integer> getMyTotalStockQuantity() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("查询当前员工股票总数量: employeeId={}", employeeId);

            Integer totalQuantity = employeeStockService.getTotalStockQuantityByEmployee(employeeId);
            return Result.success(totalQuantity);
        } catch (Exception e) {
            log.error("查询当前员工股票总数量失败", e);
            return Result.error("查询股票总数量失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的股票总价值（用户端专用）
     *
     * @return 当前员工的股票总价值
     */
    @GetMapping("/my/total-value")
    public Result<BigDecimal> getMyTotalStockValue() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("查询当前员工股票总价值: employeeId={}", employeeId);

            BigDecimal totalValue = employeeStockService.getTotalStockValueByEmployee(employeeId);
            return Result.success(totalValue);
        } catch (Exception e) {
            log.error("查询当前员工股票总价值失败", e);
            return Result.error("查询股票总价值失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的股票详细统计信息（用户端专用）
     *
     * @return 当前员工的股票详细统计信息
     */
    @GetMapping("/my/detailed-statistics")
    public Result<EmployeeStockDetailedStatisticsDTO> getMyDetailedStatistics() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("查询当前员工股票详细统计: employeeId={}", employeeId);

            EmployeeStockDetailedStatisticsDTO statistics = employeeStockService.getDetailedStockStatistics(employeeId);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询当前员工股票详细统计失败", e);
            return Result.error("查询股票详细统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录员工的可提现数量信息（用户端专用）
     *
     * @return 当前员工的可提现数量和金额信息
     */
    @GetMapping("/my/available-quantity")
    public Result<AvailableQuantityDTO> getMyAvailableQuantity() {
        try {
            // 从ThreadLocal中获取当前登录员工ID
            Map<String, Object> employeeInfo = ThreadLocalUtil.get("employee");
            if (employeeInfo == null) {
                return Result.error("未找到登录信息");
            }

            Integer employeeId = (Integer) employeeInfo.get("employeeId");
            if (employeeId == null) {
                return Result.error("员工ID不存在");
            }

            log.info("查询当前员工可提现数量: employeeId={}", employeeId);

            AvailableQuantityDTO availableQuantity = employeeStockService.getAvailableQuantityInfo(employeeId);
            return Result.success(availableQuantity);
        } catch (Exception e) {
            log.error("查询当前员工可提现数量失败", e);
            return Result.error("查询可提现数量失败：" + e.getMessage());
        }
    }
}
