import request from '@/utils/request'

/**
 * 获取部门列表（不分页）
 * @returns {Promise<any>}
 */
export function getDepartmentList() {
	return request({
		url: '/department/list',
		method: 'get',
	})
}

/**
 * 分页查询部门
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.departmentName] 部门名称
 * @param {number} [params.leaderId] 部门负责人ID
 * @param {string} [params.leaderName] 部门负责人姓名
 * @returns {Promise<any>}
 */
export function getDepartmentPage(params) {
	return request({
		url: '/department/page',
		method: 'get',
		params,
	})
}

/**
 * 根据ID查询部门
 * @param {number} id 部门ID
 * @param {boolean} includeLeaders 是否包含负责人信息，默认为true
 * @returns {Promise<any>}
 */
export function getDepartmentById(id, includeLeaders = true) {
	return request({
		url: `/department/${id}`,
		method: 'get',
		params: {
			includeLeaders
		}
	})
}

/**
 * 新增部门
 * @param {Object} data 部门信息
 * @returns {Promise<any>}
 */
export function addDepartment(data) {
	return request({
		url: '/department/add',
		method: 'post',
		data,
	})
}

/**
 * 更新部门
 * @param {Object} data 部门信息
 * @returns {Promise<any>}
 */
export function updateDepartment(data) {
	return request({
		url: '/department/update',
		method: 'put',
		data,
	})
}

/**
 * 删除部门
 * @param {number} id 部门ID
 * @returns {Promise<any>}
 */
export function deleteDepartment(id) {
	return request({
		url: `/department/${id}`,
		method: 'delete',
	})
}

/**
 * 更新部门状态
 * @param {number} id 部门ID
 * @param {string} status 状态
 * @returns {Promise<any>}
 */
export function updateDepartmentStatus(id, status) {
	return request({
		url: '/department/status',
		method: 'put',
		params: { id, status },
	})
}

/**
 * 搜索部门
 * @param {string} keyword 搜索关键字（根据部门名称进行模糊匹配）
 * @returns {Promise<any>}
 */
export function searchDepartment(keyword) {
	return request({
		url: '/department/search',
		method: 'get',
		params: { keyword },
	})
}

/**
 * 获取部门的子部门列表
 * @param {number} departmentId 部门ID
 * @returns {Promise<any>}
 */
export function getSubDepartments(departmentId) {
	return request({
		url: `/department/${departmentId}/sub`,
		method: 'get',
	})
}

/**
 * 获取部门下的员工列表
 * @param {number} departmentId 部门ID
 * @returns {Promise<any>}
 */
export function getDepartmentEmployees(departmentId) {
	return request({
		url: `/employee/department/${departmentId}`,
		method: 'get',
	})
}

/**
 * 获取部门下的员工列表（分页）
 * @param {number} departmentId 部门ID
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} [params.name] 员工姓名
 * @returns {Promise<any>}
 */
export function getDepartmentEmployeesPage(departmentId, params) {
	return request({
		url: `/employee/department/${departmentId}/page`,
		method: 'get',
		params,
	})
}


