package org.example.company_management.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.company_management.validation.ValidationGroups;

/**
 * 股票提现审核DTO
 * 用于管理员审核提现申请
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockWithdrawalAuditDTO {
    
    /**
     * 审核人ID（从ThreadLocal获取，前端不需要传递）
     */
    private Integer auditorId;
    
    /**
     * 审核动作：APPROVE-批准，REJECT-拒绝
     */
    @NotBlank(message = "审核动作不能为空", groups = {ValidationGroups.Update.class})
    @Pattern(regexp = "^(APPROVE|REJECT)$", message = "审核动作只能是APPROVE或REJECT", groups = {ValidationGroups.Update.class})
    private String action;
    
    /**
     * 拒绝理由（当action为REJECT时必填）
     */
    @Size(max = 500, message = "拒绝理由不能超过500字符", groups = {ValidationGroups.Update.class})
    private String rejectReason;
    
    /**
     * 审核备注
     */
    @Size(max = 255, message = "审核备注不能超过255字符", groups = {ValidationGroups.Update.class})
    private String remark;
    
    /**
     * 验证拒绝理由
     * 当action为REJECT时，rejectReason不能为空
     */
    public boolean isValid() {
        if ("REJECT".equals(action)) {
            return rejectReason != null && !rejectReason.trim().isEmpty();
        }
        return true;
    }
}
