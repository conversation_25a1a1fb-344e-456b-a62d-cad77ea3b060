<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    View,
    Loading,
    Warning,
} from '@element-plus/icons-vue';
import {
    getDepartmentPage,
    addDepartment,
    updateDepartment,
    deleteDepartment,
    getDepartmentList,
    getSubDepartments,
    getDepartmentEmployeesPage,
    getDepartmentById
} from '@/api/department';
import { searchEmployeeByName, getEmployeePage, getEmployeeList } from '@/api/employee';

// 部门数据
const departmentData = ref([]);
const allDepartments = ref([]); // 存储所有部门，用于显示上级部门名称和选择上级部门
const loading = ref(true);
const searchText = ref('');
const searchLeaderText = ref(''); // 用于显示部门负责人搜索的名称
const searchLeaderId = ref(null); // 部门负责人ID，用于实际查询
const searchChildText = ref(''); // 子部门搜索字段 - 存储显示的名称
const searchChildId = ref(null); // 用于存储选中的子部门查询的部门ID

// 分页设置
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 负责人选择分页相关状态
const leaderOptions = ref([]); // 负责人选项列表
const leaderSearchLoading = ref(false); // 负责人搜索加载状态
const leaderSearchQuery = ref(''); // 负责人搜索关键词
const leaderCurrentPage = ref(1); // 负责人当前页
const leaderPageSize = ref(20); // 负责人每页大小
const leaderHasMore = ref(true); // 负责人是否有更多数据

// 表单数据
const form = reactive({
    department_id: null,
    department_name: '',
    department_leader: '', // 兼容性字段，显示主要负责人姓名
    department_description: '',
    status: 'Active',
    leader_id: null, // 兼容性字段，主要负责人ID
    parent_department_id: null, // 上级部门ID字段
    parent_department_name: '', // 添加上级部门名称字段，用于显示
    // 多负责人相关字段
    leaders: [], // 负责人列表
    leaderIds: [], // 负责人ID列表
    primaryLeaderId: null, // 主要负责人ID
    selectedLeaders: [], // 选中的负责人（用于表单显示）
    selectedLeaderIds: [], // 新增：选中的负责人ID数组（用于el-select的v-model）
});

// 验证上级部门，防止选择自己或自己的下级部门作为上级部门
const validateParentDepartment = (rule, value, callback) => {
    if (!value || !form.parent_department_id) {
        // 允许不选择上级部门
        callback();
        return;
    }

    if (form.department_id && form.parent_department_id === form.department_id) {
        callback(new Error('不能选择自己作为上级部门'));
        return;
    }

    // 检查是否选择了自己的下级部门作为上级部门（防止循环依赖）
    if (form.department_id) {
        checkIfCircularDependency(form.department_id, form.parent_department_id, callback);
    } else {
        callback();
    }
};

// 检查是否存在循环依赖
const checkIfCircularDependency = (departmentId, parentId, callback) => {
    // 递归检查parentId是否是departmentId的子部门
    const checkSubDepartments = async (deptId) => {
        try {
            const res = await getSubDepartments(deptId);
            if (res.code === 200 && res.data) {
                for (const subDept of res.data) {
                    if (subDept.departmentId === parentId) {
                        return true; // 存在循环依赖
                    }
                    if (await checkSubDepartments(subDept.departmentId)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.error("检查部门依赖关系失败:", error);
            return false;
        }
    };

    checkSubDepartments(departmentId).then(hasCircular => {
        if (hasCircular) {
            callback(new Error('不能选择子部门作为上级部门'));
        } else {
            callback();
        }
    });
};

// 验证负责人选择
const validateLeaders = (rule, value, callback) => {
    if (!form.selectedLeaders || form.selectedLeaders.length === 0) {
        callback(new Error('请至少选择一个部门负责人'));
        return;
    }

    if (form.selectedLeaders.length > 1 && !form.primaryLeaderId) {
        callback(new Error('选择多个负责人时请指定主要负责人'));
        return;
    }

    callback();
};

// 表单规则
const rules = {
    department_name: [
        { required: true, message: '请输入部门名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    selectedLeaders: [
        { validator: validateLeaders, trigger: 'change' },
    ],
    department_description: [
        { max: 255, message: '长度不能超过 255 个字符', trigger: 'blur' },
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    parent_department_name: [
        { validator: validateParentDepartment, trigger: 'change' }
    ],
};

// 部门详情抽屉
const drawerVisible = ref(false);
const currentDepartment = ref(null);

// 员工数据相关状态 - 新增
const departmentEmployees = ref([]);
const employeesLoading = ref(false);
const employeeSearchName = ref(''); // 员工搜索关键词

// 员工分页设置 - 新增
const employeePagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
});

// 部门负责人选择（已移动到上方统一管理）

// Mock数据生成函数（仅在开发环境使用，正式环境应移除）
const generateMockData = () => {
    const mockData = [];
    for (let i = 1; i <= 20; i++) {
        mockData.push({
            departmentId: i,
            departmentName: `测试部门${i}`,
            departmentLeader: `负责人${i}`,
            description: `这是测试部门${i}的描述信息`,
            status: i % 3 === 0 ? 'Inactive' : 'Active',
            createTime: '2023-04-01 10:00:00',
            updateTime: '2023-04-01 10:00:00',
        });
    }
    return mockData;
};

// 加载部门数据
const loadDepartmentData = async () => {
    loading.value = true;
    departmentData.value = [];

    try {
        // 如果有子部门搜索ID，则调用子部门API
        if (searchChildId.value) {
            const res = await getSubDepartments(searchChildId.value);
            if (res.code === 200) {
                // 子部门查询不支持后端分页，需要在前端手动分页
                const allSubDepartments = res.data || [];
                pagination.total = allSubDepartments.length;
                
                // 计算当前页的数据
                const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
                const endIndex = startIndex + pagination.pageSize;
                departmentData.value = allSubDepartments.slice(startIndex, endIndex);
                
                // 重置页码，但保持分页大小不变
                if (pagination.currentPage > Math.ceil(pagination.total / pagination.pageSize)) {
                    pagination.currentPage = 1;
                }
            } else {
                ElMessage.error(res.msg || '获取子部门数据失败');
            }
        } else {
            // 常规部门分页查询
            const res = await getDepartmentPage({
                pageNum: pagination.currentPage,
                pageSize: pagination.pageSize,
                departmentName: searchText.value || undefined,
                leaderId: searchLeaderId.value || undefined, // 使用部门负责人ID
                leaderName: searchLeaderText.value || undefined // 使用部门负责人姓名
            });

            if (res.code === 200) {
                const departments = res.data.list || [];

                // 暂时不批量加载负责人信息，避免大量API请求
                // 负责人信息将在需要时（如查看详情、编辑）单独加载
                departmentData.value = departments;
                pagination.total = res.data.total || 0;
            } else {
                ElMessage.error(res.msg || '获取部门数据失败');

                // 如果后端接口失败，可以使用mock数据（仅开发环境）
                if (import.meta.env.DEV) {
                    const mockData = generateMockData();
                    departmentData.value = mockData.slice(
                        (pagination.currentPage - 1) * pagination.pageSize,
                        pagination.currentPage * pagination.pageSize
                    );
                    pagination.total = mockData.length;
                    console.warn('使用Mock数据代替后端数据');
                }
            }
        }
    } catch (error) {
        console.error('加载部门数据失败:', error);
        ElMessage.error('加载部门数据失败: ' + (error.message || '未知错误'));

        // 如果后端接口报错，可以使用mock数据（仅开发环境）
        if (import.meta.env.DEV) {
            const mockData = generateMockData();
            departmentData.value = mockData.slice(
                (pagination.currentPage - 1) * pagination.pageSize,
                pagination.currentPage * pagination.pageSize
            );
            pagination.total = mockData.length;
            console.warn('使用Mock数据代替后端数据');
        }
    } finally {
        loading.value = false;
    }
};

// 加载所有部门数据（不分页）
const loadAllDepartments = async () => {
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            allDepartments.value = res.data || [];
        } else {
            console.error('获取所有部门列表失败:', res.msg);
        }
    } catch (error) {
        console.error('获取所有部门列表出错:', error);
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.department_id = null;
    form.department_name = '';
    form.department_leader = '';
    form.department_description = '';
    form.status = 'Active';
    form.leader_id = null; // 重置部门负责人ID
    form.parent_department_id = null; // 重置上级部门ID
    form.parent_department_name = ''; // 重置上级部门名称
    // 重置多负责人相关字段
    form.leaders = [];
    form.leaderIds = [];
    form.primaryLeaderId = null;
    form.selectedLeaders = [];
    form.selectedLeaderIds = [];

    // 重置负责人选择分页状态
    leaderOptions.value = [];
    leaderSearchQuery.value = '';
    leaderCurrentPage.value = 1;
    leaderHasMore.value = true;
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    try {
        // 调用API获取完整的部门信息，包括所有负责人
        const res = await getDepartmentById(row.departmentId, true);
        if (res.code === 200 && res.data) {
            const department = res.data;

            // 设置基本信息
            form.department_id = department.departmentId;
            form.department_name = department.departmentName;
            form.department_description = department.departmentDescription || '';
            form.status = department.status;
            form.parent_department_id = department.parentDepartmentId;

            // 设置上级部门名称显示
            if (department.parentDepartmentId) {
                form.parent_department_name = getParentDepartmentName(department.parentDepartmentId);
            } else {
                form.parent_department_name = '';
            }

            // 处理多负责人信息
            if (department.leaders && department.leaders.length > 0) {
                // 设置负责人列表（适配新的数据结构）
                form.selectedLeaders = department.leaders.map(leader => ({
                    employeeId: leader.employeeId,
                    name: leader.leaderName,
                    email: leader.leaderEmail || '',
                    departmentName: leader.departmentName || '',
                    positionName: leader.positionName || '',
                    role: leader.leaderRole || 'MEMBER'
                }));

                // 设置负责人ID列表
                form.leaderIds = department.leaders.map(leader => leader.employeeId);
                form.selectedLeaderIds = department.leaders.map(leader => leader.employeeId);

                // 设置主要负责人ID
                form.primaryLeaderId = department.primaryLeaderId;

                // 兼容性：设置单个负责人字段
                form.leader_id = department.primaryLeaderId;
                form.department_leader = department.primaryLeaderName || '';

                // 将选中的负责人添加到选项列表中，确保编辑时能正确显示
                const existingIds = leaderOptions.value.map(emp => emp.employeeId);
                form.selectedLeaders.forEach(leader => {
                    if (!existingIds.includes(leader.employeeId)) {
                        leaderOptions.value.unshift({
                            employeeId: leader.employeeId,
                            name: leader.name,
                            email: leader.email,
                            departmentName: leader.departmentName,
                            positionName: leader.positionName,
                            isResigned: false
                        });
                    }
                });
            } else {
                // 如果没有负责人信息，使用兼容性字段
                form.leader_id = department.leaderId;
                form.department_leader = department.departmentLeader || '';

                if (department.leaderId && department.departmentLeader) {
                    form.selectedLeaders = [{
                        employeeId: department.leaderId,
                        name: department.departmentLeader,
                        email: '',
                        departmentName: '',
                        positionName: '',
                        role: 'PRIMARY'
                    }];
                    form.primaryLeaderId = department.leaderId;
                    form.leaderIds = [department.leaderId];
                    form.selectedLeaderIds = [department.leaderId];

                    // 确保选中的负责人在选项列表中
                    const existingIds = leaderOptions.value.map(emp => emp.employeeId);
                    if (!existingIds.includes(department.leaderId)) {
                        leaderOptions.value.unshift({
                            employeeId: department.leaderId,
                            name: department.departmentLeader,
                            email: '',
                            departmentName: '',
                            positionName: '',
                            isResigned: false
                        });
                    }
                }
            }
        } else {
            ElMessage.error(res.msg || '获取部门信息失败');
            return;
        }
    } catch (error) {
        console.error('获取部门信息失败:', error);
        ElMessage.error('获取部门信息失败: ' + (error.message || '未知错误'));
        return;
    }

    dialogVisible.value = true;
};

// 确认删除部门
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除部门 "${row.departmentName}" 吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deleteDepartment(row.departmentId);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除部门成功',
                        duration: 2000,
                    });
                    loadDepartmentData();
                    loadAllDepartments(); // 刷新所有部门数据
                } else {
                    ElMessage({
                        type: 'error',
                        message: res.msg || '删除失败',
                        duration: 3000,
                    });
                    loading.value = false;
                }
            } catch (error) {
                console.error('删除部门失败:', error);
                ElMessage({
                    type: 'error',
                    message: '删除失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备负责人数据 - 使用form.leaderIds，它已经是过滤后的有效ID数组
                const leaderIds = form.leaderIds.filter(id => id != null && id !== undefined);
                const primaryLeaderId = form.primaryLeaderId;

                // 准备提交的数据，转换为后端需要的格式
                const submitData = {
                    departmentId: form.department_id,
                    departmentName: form.department_name,
                    departmentDescription: form.department_description,
                    status: form.status,
                    parentDepartmentId: form.parent_department_id,
                    // 多负责人数据
                    leaderIds: leaderIds,
                    primaryLeaderId: primaryLeaderId,
                    // 兼容性字段
                    leaderId: primaryLeaderId,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 添加部门
                    res = await addDepartment(submitData);
                } else {
                    // 更新部门
                    res = await updateDepartment(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加部门成功'
                                : '更新部门成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    // 重新加载部门数据
                    loadDepartmentData();
                    // 重新加载所有部门数据
                    loadAllDepartments();
                } else {
                    ElMessage({
                        type: 'error',
                        message:
                            res.msg ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败'),
                        duration: 3000,
                    });
                }
            } catch (error) {
                console.error('提交表单失败:', error);
                ElMessage({
                    type: 'error',
                    message: '提交失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
            } finally {
                formLoading.value = false;
            }
        } else {
            console.log('表单验证失败');
            ElMessage({
                type: 'warning',
                message: '请完善表单信息',
                duration: 2000,
            });
            return false;
        }
    });
};

// 处理表格行类名
const tableRowClassName = ({ row }) => {
    if (row.status === 'Inactive') {
        return 'inactive-row';
    }
    return '';
};

// 搜索
const handleSearch = () => {
    pagination.currentPage = 1;
    loadDepartmentData();
};

// 清空部门负责人搜索
const clearLeaderSearch = () => {
    searchLeaderText.value = '';
    searchLeaderId.value = null;
    handleSearch();
};

// 刷新
const handleRefresh = () => {
    searchText.value = '';
    searchLeaderText.value = ''; // 清空部门负责人搜索字段
    searchLeaderId.value = null; // 清空部门负责人ID
    searchChildText.value = ''; // 清空子部门搜索字段
    searchChildId.value = null; // 重置子部门搜索ID
    pagination.currentPage = 1;
    pagination.pageSize = 10; // 重置页大小
    loadDepartmentData();
    loadAllDepartments(); // 同时刷新所有部门数据
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    loadDepartmentData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    loadDepartmentData();
};

// 格式化时间
const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    // 只返回日期部分，不包含时间
    const date = new Date(dateTimeString);
    return date
        .toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
        })
        .replace(/\//g, '-');
};

// 查看部门详情
const handleViewDetail = async (row) => {
    try {
        // 获取完整的部门信息，包括所有负责人
        const res = await getDepartmentById(row.departmentId, true);
        if (res.code === 200 && res.data) {
            currentDepartment.value = { ...res.data };
        } else {
            // 如果获取失败，使用列表中的数据作为备选
            currentDepartment.value = { ...row };
            ElMessage.warning('获取部门详细信息失败，显示基本信息');
        }
    } catch (error) {
        console.error('获取部门详情失败:', error);
        // 出错时使用列表中的数据
        currentDepartment.value = { ...row };
        ElMessage.warning('获取部门详细信息失败，显示基本信息');
    }

    drawerVisible.value = true;

    // 打开抽屉时加载员工数据
    await loadDepartmentEmployees();
};

// 获取部门员工数据 - 新增
const loadDepartmentEmployees = async () => {
    if (!currentDepartment.value) return;
    
    employeesLoading.value = true;
    try {
        const params = {
            pageNum: employeePagination.currentPage,
            pageSize: employeePagination.pageSize,
            name: employeeSearchName.value || undefined
        };
        
        const res = await getDepartmentEmployeesPage(currentDepartment.value.departmentId, params);
        
        if (res.code === 200) {
            departmentEmployees.value = res.data.list || [];
            employeePagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.msg || '获取部门员工数据失败');
            departmentEmployees.value = [];
            employeePagination.total = 0;
        }
    } catch (error) {
        console.error('获取部门员工数据失败:', error);
        ElMessage.error('获取部门员工数据失败: ' + (error.message || '未知错误'));
        departmentEmployees.value = [];
        employeePagination.total = 0;
    } finally {
        employeesLoading.value = false;
    }
};

// 搜索员工 - 新增
const handleEmployeeSearch = () => {
    employeePagination.currentPage = 1;
    loadDepartmentEmployees();
};

// 处理员工搜索输入
const handleEmployeeSearchInput = (value) => {
    // 防抖处理，避免频繁搜索
    clearTimeout(handleEmployeeSearchInput.timer);
    handleEmployeeSearchInput.timer = setTimeout(() => {
        handleEmployeeSearch();
    }, 300);
};

// 重置员工搜索 - 新增
const handleEmployeeReset = () => {
    employeeSearchName.value = '';
    employeePagination.currentPage = 1;
    loadDepartmentEmployees();
};

// 员工分页变化 - 新增
const handleEmployeeCurrentChange = (page) => {
    employeePagination.currentPage = page;
    loadDepartmentEmployees();
};

// 员工每页条数变化 - 新增
const handleEmployeeSizeChange = (size) => {
    employeePagination.pageSize = size;
    employeePagination.currentPage = 1;
    loadDepartmentEmployees();
};

// ========== 负责人选择分页加载逻辑 ==========

// 加载负责人数据（参考MeetingForm.vue实现）
const loadLeaders = async (query = '', page = 1, append = false) => {
    leaderSearchLoading.value = true;
    try {
        // 使用员工API来获取负责人数据
        const res = await getEmployeePage({
            pageNum: page,
            pageSize: leaderPageSize.value,
            name: query || undefined
        });

        if (res.code === 200) {
            let employees = [];
            if (res.data && res.data.list) {
                employees = res.data.list;
            } else if (Array.isArray(res.data)) {
                employees = res.data;
            } else {
                employees = [];
            }

            // 为每个员工添加离职状态
            employees.forEach(emp => {
                emp.isResigned = !!(emp.exitDate);
            });

            if (append) {
                leaderOptions.value = [...leaderOptions.value, ...employees];
            } else {
                leaderOptions.value = employees;
            }

            // 更新分页状态
            leaderCurrentPage.value = page;
            leaderHasMore.value = employees.length === leaderPageSize.value;
        } else {
            ElMessage.error(res.msg || '加载负责人失败');
        }
    } catch (error) {
        ElMessage.error('加载负责人失败: ' + (error.message || '未知错误'));
    } finally {
        leaderSearchLoading.value = false;
    }
};

// 加载更多负责人
const loadMoreLeaders = async () => {
    if (!leaderHasMore.value || leaderSearchLoading.value) return;

    const nextPage = leaderCurrentPage.value + 1;
    await loadLeaders(leaderSearchQuery.value, nextPage, true);
};

// 远程搜索负责人
const handleLeaderSearch = async (query) => {
    // 重置搜索状态
    leaderSearchQuery.value = query;
    leaderCurrentPage.value = 1;
    leaderHasMore.value = true;

    if (!query) {
        // 如果没有查询内容，清空选项
        leaderOptions.value = [];
        return;
    }

    // 加载第一页数据
    await loadLeaders(query, 1, false);
};

// 处理负责人选择框可见性变化
const handleLeaderSelectVisibleChange = (visible) => {
    if (visible && leaderOptions.value.length === 0) {
        // 当下拉框打开且没有数据时，加载负责人列表
        loadLeaders('', 1, false);
    }

    // 当下拉框打开时，设置滚动监听器
    if (visible) {
        nextTick(() => {
            const dropdownEl = document.querySelector('.employee-select-dropdown .el-scrollbar__wrap');
            if (dropdownEl) {
                dropdownEl.addEventListener('scroll', handleLeaderScrollDirect);
            }
        });
    } else {
        // 当下拉框关闭时，移除滚动监听器
        const dropdownEl = document.querySelector('.employee-select-dropdown .el-scrollbar__wrap');
        if (dropdownEl) {
            dropdownEl.removeEventListener('scroll', handleLeaderScrollDirect);
        }
    }
};



// 处理负责人下拉框滚动事件
const handleLeaderScroll = (event) => {
    const { target } = event;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 当滚动到底部附近时（距离底部10px以内），加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10 && leaderHasMore.value && !leaderSearchLoading.value) {
        loadMoreLeaders();
    }
};

// 直接监听Element Plus滚动容器的滚动事件
const handleLeaderScrollDirect = (event) => {
    const { target } = event;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 当滚动到底部附近时（距离底部10px以内），加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10 && leaderHasMore.value && !leaderSearchLoading.value) {
        loadMoreLeaders();
    }
};

// 搜索部门负责人（兼容原有的autocomplete搜索）
const searchLeader = async (query, callback) => {
    if (!query || query.trim() === '') {
        if (callback) callback([]);
        return;
    }

    try {
        const res = await searchEmployeeByName(query);
        if (res.code === 200) {
            const employees = res.data.list || [];
            const options = employees.map(emp => ({
                value: emp.employeeId,
                label: emp.name,
                email: emp.email
            }));

            if (callback) callback(options);
        } else {
            console.error('搜索员工失败:', res.msg);
            if (callback) callback([]);
        }
    } catch (error) {
        console.error('搜索员工出错:', error);
        if (callback) callback([]);
    }
};

// 移除负责人
const removeLeader = (leaderToRemove) => {
    const index = form.selectedLeaders.findIndex(leader => leader.employeeId === leaderToRemove.employeeId);
    if (index > -1) {
        form.selectedLeaders.splice(index, 1);

        // 更新负责人ID列表
        form.leaderIds = form.selectedLeaders.map(leader => leader.employeeId);
        form.selectedLeaderIds = form.selectedLeaders.map(leader => leader.employeeId);

        // 如果移除的是主要负责人，清空主要负责人选择
        if (form.primaryLeaderId === leaderToRemove.employeeId) {
            form.primaryLeaderId = null;
            form.leader_id = null;
            form.department_leader = '';
        }

        // 如果只剩一个负责人，自动设为主要负责人
        if (form.selectedLeaders.length === 1) {
            form.primaryLeaderId = form.selectedLeaders[0].employeeId;
            form.leader_id = form.selectedLeaders[0].employeeId;
            form.department_leader = form.selectedLeaders[0].name;
        }
    }
};

// 选择部门负责人（兼容性方法）
const handleLeaderSelect = (item) => {
    if (item && item.value) {
        form.leader_id = item.value;
        form.department_leader = item.label;
    }
};

// 处理负责人搜索选择
const handleLeaderSearchSelect = (item) => {
    try {
        if (item && typeof item === 'object') {
            searchLeaderId.value = item.value; // 保留ID用于精确搜索
            searchLeaderText.value = item.label; // 设置显示文本
            handleSearch();
        }
    } catch (error) {
        console.warn('处理负责人选择时出错:', error);
    }
};

// ========== 多负责人相关方法 ==========

// 处理负责人多选变化
const handleLeadersChange = (selectedLeaderIds) => {
    // 确保参数是数组，并过滤掉null和undefined值
    const ids = Array.isArray(selectedLeaderIds) ? selectedLeaderIds : [];
    const validIds = ids.filter(id => id != null && id !== undefined);

    // 更新负责人ID列表
    form.leaderIds = validIds;

    // 根据选中的ID构建selectedLeaders数组
    form.selectedLeaders = validIds.map(id => {
        const employee = leaderOptions.value.find(emp => emp.employeeId === id);
        if (employee) {
            return {
                employeeId: employee.employeeId,
                name: employee.name,
                email: employee.email || '',
                departmentName: employee.departmentName || '',
                positionName: employee.positionName || ''
            };
        }
        return null;
    }).filter(Boolean);

    // 如果主要负责人不在选中列表中，清空主要负责人
    if (form.primaryLeaderId && !form.leaderIds.includes(form.primaryLeaderId)) {
        form.primaryLeaderId = null;
        form.leader_id = null;
        form.department_leader = '';
    }

    // 如果只有一个负责人，自动设为主要负责人
    if (validIds.length === 1) {
        setPrimaryLeader(validIds[0]);
    }

    // 如果没有负责人，清空相关字段
    if (validIds.length === 0) {
        form.primaryLeaderId = null;
        form.leader_id = null;
        form.department_leader = '';
    }
};

// 处理主要负责人选择变化
const handlePrimaryLeaderChange = (primaryLeaderId) => {
    form.primaryLeaderId = primaryLeaderId;

    // 更新兼容性字段
    if (primaryLeaderId) {
        const primaryLeader = form.selectedLeaders.find(leader => leader.value === primaryLeaderId);
        if (primaryLeader) {
            form.leader_id = primaryLeader.value;
            form.department_leader = primaryLeader.label;
        }
    }
};

// 设置主要负责人（点击标签时调用）
const setPrimaryLeader = (leaderId) => {
    form.primaryLeaderId = leaderId;

    // 更新兼容性字段
    const primaryLeader = form.selectedLeaders.find(leader => leader.employeeId === leaderId);
    if (primaryLeader) {
        form.leader_id = primaryLeader.employeeId;
        form.department_leader = primaryLeader.name;
    }

    // 提供用户反馈
    ElMessage({
        type: 'success',
        message: `已设置 ${primaryLeader?.name} 为主要负责人`,
        duration: 1500,
    });
};

// 格式化负责人显示
const formatLeadersDisplay = (leaders) => {
    if (!leaders || leaders.length === 0) {
        return '无';
    }

    // 按角色排序，主要负责人在前
    const sortedLeaders = [...leaders].sort((a, b) => {
        if (a.leaderRole === 'PRIMARY') return -1;
        if (b.leaderRole === 'PRIMARY') return 1;
        return 0;
    });

    if (sortedLeaders.length <= 3) {
        return sortedLeaders.map(leader => {
            const name = leader.leaderName || leader.label;
            return leader.leaderRole === 'PRIMARY' ? `★${name}` : name;
        }).join(', ');
    } else {
        const displayLeaders = sortedLeaders.slice(0, 3);
        const remainingCount = sortedLeaders.length - 3;
        const displayText = displayLeaders.map(leader => {
            const name = leader.leaderName || leader.label;
            return leader.leaderRole === 'PRIMARY' ? `★${name}` : name;
        }).join(', ');
        return `${displayText} +${remainingCount}`;
    }
};

// 获取负责人完整列表的提示文本
const getLeadersTooltip = (leaders) => {
    if (!leaders || leaders.length === 0) {
        return '暂无负责人';
    }

    return leaders.map(leader => {
        const name = leader.leaderName || leader.label;
        const role = leader.leaderRole === 'PRIMARY' ? '主要负责人' :
                    leader.leaderRole === 'DEPUTY' ? '副负责人' : '协助负责人';
        return `${name} (${role})`;
    }).join('\n');
};



// 搜索子部门
const searchChildDepartment = (query, cb) => {
    const results = allDepartments.value.filter(dept => 
        dept.departmentName.toLowerCase().includes(query.toLowerCase()));
    cb(results.map(dept => ({
        value: dept.departmentName,  // 使用部门名称作为value
        departmentId: dept.departmentId,  // 保存ID用于后续处理
        label: dept.departmentName
    })));
};

// 选择子部门搜索的部门
const handleChildDepartmentSelect = (item) => {
    if (item) {
        searchChildId.value = item.departmentId; // 存储部门ID
        searchChildText.value = item.label; // 显示部门名称
        // 重置页码到第一页，确保子部门搜索结果从第一页开始显示
        pagination.currentPage = 1;
        // 自动执行子部门搜索
        handleSubDeptSearch();
    }
};

// 清除子部门搜索
const clearChildSearch = () => {
    searchChildId.value = null;
    searchChildText.value = '';
    pagination.currentPage = 1;
    pagination.pageSize = 10; // 重置页大小
    loadDepartmentData();
};

// 选择部门进行子部门搜索
const handleSubDeptSearch = () => {
    if (!searchChildId.value) {
        ElMessage.warning('请先选择要查询子部门的部门');
        return;
    }
    
    loadDepartmentData();
};

// 根据部门ID获取部门名称
const getParentDepartmentName = (parentId) => {
    if (!parentId) return '无';
    const parentDept = allDepartments.value.find(
        (dept) => dept.departmentId === parentId
    );
    return parentDept ? parentDept.departmentName : '未知部门';
};

// 搜索上级部门
const searchParentDepartment = (query, cb) => {
    const results = allDepartments.value.filter(dept => 
        dept.departmentName.toLowerCase().includes(query.toLowerCase()) &&
        (!form.department_id || dept.departmentId !== form.department_id) // 排除自己
    );
    cb(results.map(dept => ({
        value: dept.departmentName,
        departmentId: dept.departmentId,
        label: dept.departmentName
    })));
};

// 选择上级部门
const handleParentDepartmentSelect = (item) => {
    form.parent_department_name = item.value;
    form.parent_department_id = item.departmentId;
};

// 初始加载
onMounted(() => {
    loadDepartmentData();
    loadAllDepartments(); // 加载所有部门数据
});
</script>

<template>
    <div class="department-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchText"
                    placeholder="搜索部门名称"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <el-autocomplete
                    v-model="searchLeaderText"
                    placeholder="部门负责人"
                    style="width: 220px;"
                    :fetch-suggestions="searchLeader"
                    @select="handleLeaderSearchSelect"
                    :trigger-on-focus="false"
                    :debounce="300"
                    clearable
                    @clear="clearLeaderSearch"
                    value-key="label"
                    :highlight-first-item="false"
                >
                    <template #prefix>
                        <el-icon class="el-input__icon">
                            <Search />
                        </el-icon>
                    </template>
                    <template #default="{ item }">
                        <div class="employee-suggestion-item">
                            <div class="employee-name">{{ item.label }}</div>
                            <div class="employee-email">{{ item.email }}</div>
                        </div>
                    </template>
                </el-autocomplete>
                
                <!-- 子部门搜索 -->
                <el-autocomplete
                    v-model="searchChildText"
                    :fetch-suggestions="searchChildDepartment"
                    placeholder="搜索部门的子部门"
                    style="width: 220px;"
                    clearable
                    @select="handleChildDepartmentSelect"
                    @clear="clearChildSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                    <!-- 添加自定义模板显示部门名称 -->
                    <template #default="{ item }">
                        <div>{{ item.label }}</div>
                    </template>
                </el-autocomplete>
                
                <el-button
                    type="primary"
                    @click="handleSearch"
                >搜索</el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>
            <div class="action-box">
                <el-button
                    type="primary"
                    @click="handleAdd"
                    class="add-btn"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加部门
                </el-button>
            </div>
        </div>

        <!-- 表格 -->
        <el-table
            v-loading="loading"
            :data="departmentData"
            border
            row-key="departmentId"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="index"
                width="70"
                align="center"
                label="序号"
                fixed
                class-name="index-column"
            />
            <el-table-column
                prop="departmentName"
                label="部门名称"
                min-width="150"
                show-overflow-tooltip
            />
            <el-table-column
                label="部门负责人"
                min-width="200"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="leaders-display">
                        <template v-if="row.leaderNames && row.leaderNames.length > 0">
                            <!-- 显示前3个负责人 -->
                            <template v-for="(name, index) in row.leaderNames.slice(0, 3)" :key="index">
                                <span class="leader-name">
                                    <!-- 主要负责人显示星号 -->
                                    <span v-if="row.leaderIds && row.leaderIds[index] === row.primaryLeaderId" class="primary-star">★</span>
                                    {{ name }}
                                </span>
                                <span v-if="index < Math.min(row.leaderNames.length, 3) - 1" class="leader-separator">、</span>
                            </template>
                            <!-- 如果超过3个，显示+N，鼠标悬停显示全部 -->
                            <el-tooltip v-if="row.leaderNames.length > 3" placement="top" effect="dark">
                                <template #content>
                                    <div class="tooltip-leaders">
                                        <div class="tooltip-title">全部负责人：</div>
                                        <div v-for="(name, index) in row.leaderNames" :key="index" class="tooltip-leader-item">
                                            <span v-if="row.leaderIds && row.leaderIds[index] === row.primaryLeaderId" class="tooltip-primary-star">★</span>
                                            {{ name }}
                                            <span v-if="row.leaderIds && row.leaderIds[index] === row.primaryLeaderId" class="tooltip-role">(主要负责人)</span>
                                        </div>
                                    </div>
                                </template>
                                <span class="more-leaders">
                                    +{{ row.leaderNames.length - 3 }}
                                </span>
                            </el-tooltip>
                        </template>
                        <template v-else>
                            <!-- 兼容性：显示单个负责人 -->
                            <span class="leader-name">
                                <span v-if="row.leaderId === row.primaryLeaderId" class="primary-star">★</span>
                                {{ row.departmentLeader || '无' }}
                            </span>
                        </template>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="departmentDescription"
                label="部门描述"
                min-width="200"
                show-overflow-tooltip
            />
            <el-table-column
                label="上级部门"
                min-width="150"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ getParentDepartmentName(row.parentDepartmentId) || '无' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                width="100"
                align="center"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-tag
                        :type="row.status === 'Active' ? 'success' : 'danger'"
                        class="status-tag"
                    >
                        {{ row.status === 'Active' ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                label="创建时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.createTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="更新时间"
                min-width="160"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ formatDateTime(row.updateTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="180"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="view-btn"
                            @click="handleViewDetail(row)"
                            title="查看详情"
                        >
                            <el-icon>
                                <View />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加部门' : '编辑部门'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="left"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="部门名称"
                    prop="department_name"
                    required
                >
                    <el-input
                        v-model="form.department_name"
                        placeholder="请输入部门名称"
                    />
                </el-form-item>
                <el-form-item
                    label="部门负责人"
                    prop="selectedLeaders"
                    required
                >
                    <div class="leaders-selection">
                        <!-- 负责人多选器 -->
                        <el-select
                            v-model="form.selectedLeaderIds"
                            multiple
                            filterable
                            remote
                            reserve-keyword
                            placeholder="点击下拉选择负责人或输入姓名搜索"
                            :remote-method="handleLeaderSearch"
                            :loading="leaderSearchLoading"
                            style="width: 100%; margin-bottom: 15px;"
                            @change="handleLeadersChange"
                            clearable
                            popper-class="employee-select-dropdown"
                            @visible-change="handleLeaderSelectVisibleChange"
                        >
                            <template #empty>
                                <div class="empty-text">
                                    <p v-if="!leaderSearchQuery && leaderOptions.length === 0">
                                        点击下拉加载负责人列表
                                    </p>
                                    <p v-else-if="leaderSearchQuery && leaderOptions.length === 0">
                                        未找到匹配的负责人
                                    </p>
                                    <p v-else>暂无数据</p>
                                </div>
                            </template>

                            <!-- 负责人选项列表 -->
                            <div
                                v-if="leaderOptions.length > 0"
                                class="employee-options-container"
                                @scroll="handleLeaderScroll"
                            >
                                <el-option
                                    v-for="employee in leaderOptions"
                                    :key="employee.employeeId"
                                    :label="employee.name"
                                    :value="employee.employeeId"
                                    :class="{ 'resigned-employee': employee.isResigned }"
                                >
                                    <div class="employee-option-content">
                                        <span class="employee-name">{{ employee.name }}</span>
                                        <span v-if="employee.departmentName || employee.positionName || employee.isResigned" class="employee-info">
                                            <span v-if="employee.departmentName">({{ employee.departmentName }}</span><span v-if="employee.positionName">{{ employee.departmentName ? '-' : '(' }}{{ employee.positionName }}</span><span v-if="employee.departmentName || employee.positionName">)</span>
                                            <span v-if="employee.isResigned" class="resigned-status">（已离职）</span>
                                        </span>
                                    </div>
                                </el-option>

                                <!-- 加载更多提示 -->
                                <div
                                    v-if="leaderHasMore"
                                    class="load-more-option"
                                    @click="loadMoreLeaders"
                                >
                                    <el-icon v-if="leaderSearchLoading" class="is-loading">
                                        <Loading />
                                    </el-icon>
                                    <span>{{ leaderSearchLoading ? '加载中...' : '滚动或点击加载更多' }}</span>
                                </div>

                                <!-- 没有更多数据提示 -->
                                <div v-else class="no-more-data">
                                    <span>已显示全部 {{ leaderOptions.length }} 个负责人</span>
                                </div>
                            </div>
                        </el-select>

                        <!-- 选中的负责人管理 -->
                        <div v-if="form.selectedLeaders && form.selectedLeaders.length > 0" class="selected-leaders-management">
                            
                            <div class="leaders-tags">
                                <el-tag
                                    v-for="leader in form.selectedLeaders"
                                    :key="leader.employeeId"
                                    :type="leader.employeeId === form.primaryLeaderId ? 'primary' : 'info'"
                                    class="leader-tag clickable"
                                    closable
                                    @close="removeLeader(leader)"
                                    @click="setPrimaryLeader(leader.employeeId)"
                                >
                                    <span v-if="leader.employeeId === form.primaryLeaderId" class="primary-star">★</span>
                                    {{ leader.name }}
                                    <span class="leader-role">
                                        {{ leader.employeeId === form.primaryLeaderId ? '主要负责人' : '点击设为主要' }}
                                    </span>
                                </el-tag>
                            </div>
                            <div v-if="!form.primaryLeaderId" class="primary-warning">
                                <el-icon class="warning-icon"><Warning /></el-icon>
                                请点击任意负责人标签设为主要负责人
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item
                    label="上级部门"
                    prop="parent_department_name"
                >
                    <el-autocomplete
                        v-model="form.parent_department_name"
                        placeholder="请选择上级部门"
                        style="width: 100%"                       
                        :fetch-suggestions="searchParentDepartment"
                        @select="handleParentDepartmentSelect"
                        :trigger-on-focus="false"
                        :debounce="300"
                        clearable
                    >
                        <template #default="{ item }">
                            <div>{{ item.label }}</div>
                        </template>
                        <template #suffix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-autocomplete>
                </el-form-item>
                <el-form-item
                    label="部门描述"
                    prop="department_description"
                >
                    <el-input
                        v-model="form.department_description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入部门描述"
                    />
                </el-form-item>
                <el-form-item
                    label="状态"
                    prop="status"
                    required
                >
                    <el-select
                        v-model="form.status"
                        placeholder="请选择状态"
                        style="width: 100%"
                    >
                        <el-option
                            label="启用"
                            value="Active"
                        />
                        <el-option
                            label="禁用"
                            value="Inactive"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- 部门详情抽屉 -->
        <el-drawer
            v-model="drawerVisible"
            :title="`${currentDepartment?.departmentName || ''} - 部门详情`"
            direction="rtl"
            size="80%"
            destroy-on-close
            :modal-class="'department-detail-drawer'"
        >
            <!-- 部门详情容器 -->
            <div class="department-detail-container">
                <!-- 部门基本信息卡片 -->
                <el-card class="detail-card">
                    <template #header>
                        <div class="card-header">
                            <h3>部门基本信息</h3>
                            <el-tag
                                :type="currentDepartment?.status === 'Active' ? 'success' : 'danger'"
                                class="status-tag"
                            >
                                {{ currentDepartment?.status === 'Active' ? '启用' : '禁用' }}
                            </el-tag>
                        </div>
                    </template>

                    <div class="department-info">
                        <div class="info-item">
                            <div class="info-label">部门名称：</div>
                            <div class="info-value">{{ currentDepartment?.departmentName }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">部门负责人：</div>
                            <div class="info-value">
                                <div class="leaders-detail-center">
                                    <template v-if="currentDepartment?.leaderNames && currentDepartment.leaderNames.length > 0">
                                        <div v-for="(name, index) in currentDepartment.leaderNames" :key="index" class="leader-item-center">
                                            <el-tag
                                                :type="currentDepartment.leaderIds && currentDepartment.leaderIds[index] === currentDepartment.primaryLeaderId ? 'primary' : 'info'"
                                                class="leader-detail-tag-center"
                                                size="default"
                                            >
                                                <span v-if="currentDepartment.leaderIds && currentDepartment.leaderIds[index] === currentDepartment.primaryLeaderId" class="primary-star">★</span>
                                                {{ name }}
                                                <span v-if="currentDepartment.leaderIds && currentDepartment.leaderIds[index] === currentDepartment.primaryLeaderId" class="leader-role-text">(主要负责人)</span>
                                            </el-tag>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <!-- 兼容性：显示单个负责人 -->
                                        <div class="leader-item-center">
                                            <el-tag
                                                :type="currentDepartment?.leaderId === currentDepartment?.primaryLeaderId ? 'primary' : 'info'"
                                                class="leader-detail-tag-center"
                                                size="default"
                                            >
                                                <span v-if="currentDepartment?.leaderId === currentDepartment?.primaryLeaderId" class="primary-star">★</span>
                                                {{ currentDepartment?.departmentLeader || '暂无' }}
                                                <span v-if="currentDepartment?.leaderId === currentDepartment?.primaryLeaderId" class="leader-role-text">(主要负责人)</span>
                                            </el-tag>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">部门描述：</div>
                            <div class="info-value">{{ currentDepartment?.departmentDescription || '暂无描述' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">上级部门：</div>
                            <div class="info-value">{{ getParentDepartmentName(currentDepartment?.parentDepartmentId) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">创建时间：</div>
                            <div class="info-value">{{ formatDateTime(currentDepartment?.createTime) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">更新时间：</div>
                            <div class="info-value">{{ formatDateTime(currentDepartment?.updateTime) }}</div>
                        </div>
                    </div>
                </el-card>

                <!-- 员工列表卡片 -->
                <el-card class="employee-card">
                    <template #header>
                        <div class="card-header">
                            <h3>部门员工列表</h3>
                            <div class="employee-search-box">
                                <el-input
                                    v-model="employeeSearchName"
                                    placeholder="搜索员工姓名"
                                    clearable
                                    @input="handleEmployeeSearchInput"
                                >
                                    <template #prefix>
                                        <el-icon>
                                            <Search />
                                        </el-icon>
                                    </template>
                                </el-input>
                                <el-button
                                    type="primary"
                                    @click="handleEmployeeSearch"
                                >搜索</el-button>
                                <el-button @click="handleEmployeeReset">
                                    <el-icon><RefreshRight /></el-icon>重置
                                </el-button>
                            </div>
                        </div>
                    </template>

                    <div
                        class="employee-list"
                        v-loading="employeesLoading"
                    >
                        <el-table
                            :data="departmentEmployees"
                            stripe
                            border
                            style="width: 100%;"
                            max-height="calc(100vh - 500px)"
                        >
                            <el-table-column
                                type="index"
                                label="序号"
                                width="60"
                                align="center"
                            />
                            <el-table-column
                                prop="name"
                                label="姓名"
                                min-width="100"
                            />
                            <el-table-column
                                prop="email"
                                label="邮箱"
                                min-width="150"
                            />
                            <el-table-column
                                prop="positionName"
                                label="职位"
                                min-width="120"
                            />
                            <el-table-column
                                prop="entryDate"
                                label="入职日期"
                                min-width="120"
                            >
                                <template #default="{ row }">
                                    {{ formatDateTime(row.entryDate) }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="status"
                                label="状态"
                                width="80"
                                align="center"
                            >
                                <template #default="{ row }">
                                    <el-tag :type="row.status === 'Active' ? 'success' : 'danger'">
                                        {{ row.status === 'Active' ? '在职' : '离职' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 员工分页器 -->
                        <div class="employee-pagination-container">
                            <el-pagination
                                background
                                layout="total, sizes, prev, pager, next, jumper"
                                :current-page="employeePagination.currentPage"
                                :page-size="employeePagination.pageSize"
                                :total="employeePagination.total"
                                :page-sizes="[10, 20, 50]"
                                @size-change="handleEmployeeSizeChange"
                                @current-change="handleEmployeeCurrentChange"
                            />
                        </div>

                        <div
                            v-if="!employeesLoading && departmentEmployees.length === 0"
                            class="no-data"
                        >
                            暂无员工数据
                        </div>
                    </div>
                </el-card>
            </div>
        </el-drawer>
    </div>
</template>

<style scoped>
.department-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input, .search-box .el-autocomplete {
    width: 220px;
    flex-shrink: 0;
}

:deep(.search-box .el-autocomplete) {
    width: 220px !important;
}

:deep(.el-autocomplete__inner) {
    width: 220px !important;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.view-btn,
.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.view-btn {
    background-color: #e1f3d8;
    color: #67c23a;
}

.view-btn:hover {
    background-color: #67c23a;
    color: white;
    transform: translateY(-2px);
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

:deep(.status-tag:hover) {
    transform: scale(1.1);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.department-detail {
    padding: 20px;
}

.employee-list {
    margin-top: 30px;
}

.employee-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.employee-list-header h3 {
    margin: 0;
    color: #303133;
    font-size: 18px;
}

.employee-count {
    background-color: #f0f9eb;
    color: #67c23a;
    padding: 5px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.no-data {
    text-align: center;
    color: #909399;
    padding: 30px 0;
    font-size: 14px;
    background-color: #f8f8f9;
    border-radius: 4px;
    margin-top: 15px;
}

.employee-search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
}

.employee-search-box .el-input {
    width: 220px;
}

.employee-pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding: 10px 0;
}

.employee-suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.employee-name {
    font-weight: bold;
    color: #303133;
}

.employee-email {
    color: #909399;
    font-size: 13px;
    margin-left: 10px;
}

:deep(.leader-suggestions) {
    width: auto !important;
    min-width: 100% !important;
}

:deep(.leader-suggestions .el-autocomplete-suggestion__list) {
    max-height: 280px;
}

:deep(.loading-icon) {
    animation: rotating 2s linear infinite;
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.back-to-parent {
    margin-bottom: 20px;
}

.sub-departments {
    margin-top: 20px;
}

.sub-department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.sub-department-header h3 {
    margin: 0;
    color: #303133;
    font-size: 18px;
}

.sub-department-count {
    background-color: #f0f9eb;
    color: #67c23a;
    padding: 5px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.department-tree {
    margin-top: 15px;
}

.department-tabs {
    margin-top: 20px;
}

.sub-department-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sub-department-content {
    margin-top: 10px;
}

.sub-department-actions {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
}

.employee-count-tag {
    margin-left: 10px;
}

/* 全局样式：确保抽屉内的级联菜单可以正确显示 */
.department-detail-drawer .el-drawer__body {
    overflow: visible !important;
}

/* 部门详情页样式 */
.department-detail-container {
    padding: 20px;
}

.detail-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #303133;
    font-size: 18px;
}

.department-info {
    padding: 10px 0;
}

.info-item {
    display: flex;
    margin-bottom: 15px;
    line-height: 24px;
}

.info-label {
    width: 120px;
    color: #606266;
    font-weight: 500;
}

.info-value {
    flex: 1;
    color: #303133;
    text-align: center;
}

.employee-card {
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 60px;
}

.employee-list {
    margin-top: 10px;
}

.employee-search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.employee-search-box .el-input {
    width: 220px;
}

.employee-pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.no-data {
    text-align: center;
    color: #909399;
    padding: 30px 0;
    font-size: 14px;
    background-color: #f8f8f9;
    border-radius: 4px;
    margin-top: 15px;
}

/* 全局样式：确保抽屉内的级联菜单可以正确显示 */
.department-detail-drawer .el-drawer__body {
    overflow: visible !important;
    padding: 0;
}

/* ========== 多负责人相关样式 ========== */

.leaders-display {
    display: flex;
    align-items: center;
}

.leaders-text {
    cursor: help;
    color: #303133;
}

.no-leader {
    color: #909399;
    font-style: italic;
}

.leaders-selection {
    width: 100%;
}

.employee-option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
}

.employee-name {
    font-weight: 500;
    color: #303133;
}

.employee-email {
    color: #909399;
    font-size: 12px;
    margin-left: 10px;
}

.selected-leaders-preview {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.preview-title {
    font-size: 13px;
    color: #606266;
    margin-bottom: 8px;
    font-weight: 500;
}

.leaders-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.leader-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.leader-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.primary-star {
    color: #f39c12;
    font-weight: bold;
    margin-right: 2px;
}

.leader-role {
    font-size: 11px;
    opacity: 0.8;
    margin-left: 4px;
    padding: 1px 4px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

/* 表格中负责人显示的样式 */
:deep(.leaders-display) {
    max-width: 100%;
    overflow: hidden;
    line-height: 1.4;
}

:deep(.leaders-text) {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 表格中负责人姓名样式 */
.leader-name {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    color: #606266;
}

.leader-separator {
    color: #909399;
    margin: 0 2px;
}

.more-leaders {
    color: #909399;
    font-size: 12px;
    margin-left: 4px;
    font-style: italic;
    cursor: pointer;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.more-leaders:hover {
    color: #409eff;
}

/* 表格中主要负责人星号样式 */
.leaders-display .primary-star {
    color: #f39c12;
    font-weight: bold;
    margin-right: 3px;
    font-size: 12px;
}

/* Tooltip中的负责人显示样式 */
.tooltip-leaders {
    max-width: 300px;
}

.tooltip-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #fff;
    font-size: 13px;
}

.tooltip-leader-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    line-height: 1.4;
}

.tooltip-primary-star {
    color: #f39c12;
    font-weight: bold;
    margin-right: 4px;
}

.tooltip-role {
    color: #ccc;
    font-size: 11px;
    margin-left: 6px;
}

/* 多选下拉框样式优化 */
:deep(.el-select__tags) {
    max-height: 120px;
    overflow-y: auto;
}

:deep(.el-select__tags .el-tag) {
    max-width: 200px;
    height: auto;
    min-height: 28px;
    padding: 4px 8px;
    margin: 2px 4px 2px 0;
    line-height: 1.4;
    white-space: normal;
    word-break: break-word;
}

:deep(.el-select__tags .el-tag .el-tag__content) {
    font-size: 13px;
    line-height: 1.4;
}

:deep(.el-tag.el-tag--info) {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
}

:deep(.el-tag.el-tag--primary) {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
    color: #409eff;
    font-weight: 500;
}

/* 员工选择下拉框样式（与Client.vue保持一致） */
:deep(.employee-select-dropdown) {
    max-height: 350px;
}

:deep(.employee-select-dropdown .el-select-dropdown__list) {
    padding: 0;
}

.employee-options-container {
    max-height: 300px;
    overflow-y: auto;
}

.load-more-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    color: #409eff;
    cursor: pointer;
    border-top: 1px solid #ebeef5;
    transition: background-color 0.3s;
    background-color: #fafafa;
}

.load-more-option:hover {
    background-color: #f0f9ff;
}

.load-more-option .el-icon {
    margin-right: 4px;
}

.load-more-option .is-loading {
    animation: rotating 2s linear infinite;
}

.no-more-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    color: #909399;
    font-size: 12px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
}

:deep(.resigned-employee) {
    color: #909399;
    background-color: #f5f7fa;
}

:deep(.resigned-employee:hover) {
    background-color: #ecf5ff;
}

.leader-department {
    font-size: 11px;
    color: #909399;
    margin-left: 4px;
}

/* 负责人标签中的部门信息样式 */
.leader-tag .leader-department {
    font-size: 11px;
    color: #666;
    opacity: 0.8;
    margin-left: 4px;
}

.leader-tag .leader-role {
    font-size: 10px;
    opacity: 0.7;
    margin-left: 6px;
    padding: 1px 4px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

/* 员工选择下拉框样式优化 */
.employee-option-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 4px 0;
}

.employee-name {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
    line-height: 1.4;
}

.employee-details {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 2px;
    font-size: 12px;
    color: #909399;
}

.employee-department {
    color: #409eff;
}

.employee-position {
    color: #67c23a;
}

.employee-status.active {
    color: #67c23a;
}

.employee-status.resigned {
    color: #f56c6c;
}

/* 员工选择下拉框选项样式 */
.employee-option-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.employee-name {
    font-weight: 500;
    color: #303133;
}

.employee-info {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.resigned-status {
    color: #f56c6c;
    font-weight: 500;
}

/* 部门详情中的负责人显示样式 */
.leaders-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.leader-item {
    margin-bottom: 4px;
}

.leader-detail-tag {
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 13px;
}

/* 部门详情中负责人居中显示样式 */
.leaders-detail-center {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    justify-content: center;
    min-height: 32px;
    padding: 8px 0;
}

.leader-item-center {
    margin-bottom: 6px;
}

.leader-detail-tag-center {
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.leader-role-text {
    font-size: 11px;
    opacity: 0.8;
    margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .leaders-tags {
        flex-direction: column;
        align-items: flex-start;
    }

    .leader-tag {
        width: 100%;
        justify-content: space-between;
    }

    .selected-leaders-preview {
        padding: 8px;
    }

    .leaders-detail {
        flex-direction: column;
        align-items: flex-start;
    }

    .leader-detail-tag {
        width: 100%;
        justify-content: space-between;
    }
}
</style>