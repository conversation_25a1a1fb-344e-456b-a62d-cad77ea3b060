package org.example.company_management.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.company_management.entity.DepartmentLeader;

import java.util.List;

/**
 * 部门负责人关联Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface DepartmentLeaderMapper {
    
    // ========== 基础CRUD操作 ==========
    
    /**
     * 插入部门负责人关联记录
     * @param departmentLeader 部门负责人关联信息
     * @return 影响行数
     */
    int insert(DepartmentLeader departmentLeader);
    
    /**
     * 根据ID删除部门负责人关联记录
     * @param id 关联记录ID
     * @return 影响行数
     */
    int deleteById(Integer id);
    
    /**
     * 删除指定部门的指定负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @return 影响行数
     */
    int deleteByDepartmentAndEmployee(@Param("departmentId") Integer departmentId, 
                                     @Param("employeeId") Integer employeeId);
    
    /**
     * 删除部门的所有负责人
     * @param departmentId 部门ID
     * @return 影响行数
     */
    int deleteByDepartmentId(Integer departmentId);
    
    /**
     * 删除员工的所有部门负责人关联
     * @param employeeId 员工ID
     * @return 影响行数
     */
    int deleteByEmployeeId(Integer employeeId);
    
    /**
     * 更新部门负责人关联记录
     * @param departmentLeader 部门负责人关联信息
     * @return 影响行数
     */
    int updateById(DepartmentLeader departmentLeader);
    
    /**
     * 根据ID查询部门负责人关联记录
     * @param id 关联记录ID
     * @return 部门负责人关联信息
     */
    DepartmentLeader selectById(Integer id);
    
    // ========== 业务查询方法 ==========
    
    /**
     * 查询部门的所有负责人
     * @param departmentId 部门ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 部门负责人列表
     */
    List<DepartmentLeader> selectByDepartmentId(@Param("departmentId") Integer departmentId,
                                               @Param("includeInactive") Boolean includeInactive);
    
    /**
     * 查询员工负责的所有部门
     * @param employeeId 员工ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 部门负责人列表
     */
    List<DepartmentLeader> selectByEmployeeId(@Param("employeeId") Integer employeeId,
                                             @Param("includeInactive") Boolean includeInactive);
    
    /**
     * 查询部门的主要负责人
     * @param departmentId 部门ID
     * @return 主要负责人信息，如果没有则返回null
     */
    DepartmentLeader selectPrimaryLeaderByDepartmentId(Integer departmentId);
    
    /**
     * 查询部门的所有激活状态负责人ID列表
     * @param departmentId 部门ID
     * @return 负责人ID列表
     */
    List<Integer> selectActiveLeaderIdsByDepartmentId(Integer departmentId);
    
    /**
     * 查询员工负责的所有激活状态部门ID列表
     * @param employeeId 员工ID
     * @return 部门ID列表
     */
    List<Integer> selectActiveDepartmentIdsByEmployeeId(Integer employeeId);
    
    /**
     * 检查员工是否为指定部门的负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @return true-是负责人，false-不是负责人
     */
    boolean checkIsLeader(@Param("departmentId") Integer departmentId, 
                         @Param("employeeId") Integer employeeId);
    
    /**
     * 检查员工是否为指定部门的主要负责人
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @return true-是主要负责人，false-不是主要负责人
     */
    boolean checkIsPrimaryLeader(@Param("departmentId") Integer departmentId, 
                                @Param("employeeId") Integer employeeId);
    
    // ========== 统计查询方法 ==========
    
    /**
     * 统计部门的负责人数量
     * @param departmentId 部门ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 负责人数量
     */
    int countLeadersByDepartmentId(@Param("departmentId") Integer departmentId,
                                  @Param("includeInactive") Boolean includeInactive);
    
    /**
     * 统计员工负责的部门数量
     * @param employeeId 员工ID
     * @param includeInactive 是否包含非激活状态的记录
     * @return 负责部门数量
     */
    int countDepartmentsByEmployeeId(@Param("employeeId") Integer employeeId,
                                    @Param("includeInactive") Boolean includeInactive);
    
    // ========== 批量操作方法 ==========
    
    /**
     * 批量插入部门负责人关联记录
     * @param departmentLeaders 部门负责人关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<DepartmentLeader> departmentLeaders);
    
    /**
     * 更新部门负责人的激活状态
     * @param departmentId 部门ID
     * @param employeeId 员工ID
     * @param isActive 激活状态
     * @return 影响行数
     */
    int updateActiveStatus(@Param("departmentId") Integer departmentId,
                          @Param("employeeId") Integer employeeId,
                          @Param("isActive") Boolean isActive);
}
